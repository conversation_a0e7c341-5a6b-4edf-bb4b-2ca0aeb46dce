[package]
name = "ospbase"
version = "0.1.0"
edition = "2021"

[dependencies]
    bigdecimal = "0.4"
    chrono = { version = "0.4", features = ["serde"] }
    tokio = { version = "1", features = ["full"] }
    thiserror = "2"
    serde = { version = "1.0", features = ["derive"] }
    serde_json = "1.0"
    reqwest = "0.12"
    futures = "0.3"
    tokio-stream = "0.1"
    tokio-util = "0.7"
    lazy_static = "1"
    proc-macro2 = "1.0.89"
    quote = "1.0.32"
    syn = "2.0.29"
    once_cell = "1.20.2"
    rust_decimal = "1.3"
    strum = "0.26.0"
    strum_macros = "0.26.0"
    # proc-macro-crate = "3.2.0"

# [lib]
    # proc-macro = true
    
