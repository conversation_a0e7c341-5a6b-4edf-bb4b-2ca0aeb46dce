use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use uuid::Uuid;
use chrono::{DateTime, Utc};

/// iDME元模型驱动的核心结构
/// 基于华为iDME的"2类元模型，6类元关系"设计

/// 元模型类型枚举 - 2类元模型
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum MetaModelType {
    /// 实体元模型 - 描述业务实体的结构和属性
    EntityMetaModel,
    /// 关系元模型 - 描述实体间的关联关系
    RelationMetaModel,
}

/// 元关系类型枚举 - 6类元关系
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum MetaRelationType {
    /// 继承关系 - 表示类型的继承层次
    Inheritance,
    /// 组合关系 - 表示整体与部分的强关联
    Composition,
    /// 聚合关系 - 表示整体与部分的弱关联
    Aggregation,
    /// 关联关系 - 表示实体间的一般性关联
    Association,
    /// 依赖关系 - 表示实体间的依赖关系
    Dependency,
    /// 实现关系 - 表示接口与实现的关系
    Realization,
}

/// 数据类型定义
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum DataType {
    String,
    Integer,
    Float,
    Boolean,
    DateTime,
    Binary,
    Json,
    Reference(String), // 引用其他实体的ID
}

/// 属性定义
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AttributeDefinition {
    pub id: String,
    pub name: String,
    pub description: Option<String>,
    pub data_type: DataType,
    pub is_required: bool,
    pub is_unique: bool,
    pub default_value: Option<String>,
    pub constraints: Vec<String>,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

/// 实体元模型定义
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct EntityMetaModel {
    pub id: String,
    pub name: String,
    pub description: Option<String>,
    pub namespace: String,
    pub version: String,
    pub attributes: HashMap<String, AttributeDefinition>,
    pub parent_entity: Option<String>, // 继承的父实体ID
    pub is_abstract: bool,
    pub tags: Vec<String>,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

/// 关系元模型定义
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RelationMetaModel {
    pub id: String,
    pub name: String,
    pub description: Option<String>,
    pub relation_type: MetaRelationType,
    pub source_entity: String,
    pub target_entity: String,
    pub cardinality: Cardinality,
    pub attributes: HashMap<String, AttributeDefinition>,
    pub is_navigable: bool,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

/// 基数定义
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub struct Cardinality {
    pub source_min: u32,
    pub source_max: Option<u32>, // None表示无限制
    pub target_min: u32,
    pub target_max: Option<u32>,
}

/// 元模型统一接口
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum MetaModel {
    Entity(EntityMetaModel),
    Relation(RelationMetaModel),
}

/// 元模型仓库 - 管理所有元模型
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MetaModelRepository {
    pub entities: HashMap<String, EntityMetaModel>,
    pub relations: HashMap<String, RelationMetaModel>,
    pub namespaces: HashMap<String, Vec<String>>, // namespace -> entity_ids
}

/// 实体实例
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct EntityInstance {
    pub id: String,
    pub meta_model_id: String,
    pub attributes: HashMap<String, serde_json::Value>,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

/// 关系实例
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RelationInstance {
    pub id: String,
    pub meta_model_id: String,
    pub source_instance_id: String,
    pub target_instance_id: String,
    pub attributes: HashMap<String, serde_json::Value>,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

impl MetaModelRepository {
    /// 创建新的元模型仓库
    pub fn new() -> Self {
        Self {
            entities: HashMap::new(),
            relations: HashMap::new(),
            namespaces: HashMap::new(),
        }
    }

    /// 添加实体元模型
    pub fn add_entity_meta_model(&mut self, entity: EntityMetaModel) -> Result<(), String> {
        // 验证父实体是否存在
        if let Some(parent_id) = &entity.parent_entity {
            if !self.entities.contains_key(parent_id) {
                return Err(format!("Parent entity {} not found", parent_id));
            }
        }

        let namespace = entity.namespace.clone();
        let entity_id = entity.id.clone();
        
        self.entities.insert(entity.id.clone(), entity);
        
        // 更新命名空间索引
        self.namespaces
            .entry(namespace)
            .or_insert_with(Vec::new)
            .push(entity_id);

        Ok(())
    }

    /// 添加关系元模型
    pub fn add_relation_meta_model(&mut self, relation: RelationMetaModel) -> Result<(), String> {
        // 验证源实体和目标实体是否存在
        if !self.entities.contains_key(&relation.source_entity) {
            return Err(format!("Source entity {} not found", relation.source_entity));
        }
        if !self.entities.contains_key(&relation.target_entity) {
            return Err(format!("Target entity {} not found", relation.target_entity));
        }

        self.relations.insert(relation.id.clone(), relation);
        Ok(())
    }

    /// 获取实体的所有子类
    pub fn get_entity_children(&self, entity_id: &str) -> Vec<&EntityMetaModel> {
        self.entities
            .values()
            .filter(|entity| {
                entity.parent_entity.as_ref() == Some(&entity_id.to_string())
            })
            .collect()
    }

    /// 获取实体的所有关系
    pub fn get_entity_relations(&self, entity_id: &str) -> Vec<&RelationMetaModel> {
        self.relations
            .values()
            .filter(|relation| {
                relation.source_entity == entity_id || relation.target_entity == entity_id
            })
            .collect()
    }
}

impl Default for MetaModelRepository {
    fn default() -> Self {
        Self::new()
    }
}

impl EntityMetaModel {
    /// 创建新的实体元模型
    pub fn new(
        name: String,
        namespace: String,
        description: Option<String>,
    ) -> Self {
        let now = Utc::now();
        Self {
            id: Uuid::new_v4().to_string(),
            name,
            description,
            namespace,
            version: "1.0.0".to_string(),
            attributes: HashMap::new(),
            parent_entity: None,
            is_abstract: false,
            tags: Vec::new(),
            created_at: now,
            updated_at: now,
        }
    }

    /// 添加属性
    pub fn add_attribute(&mut self, attribute: AttributeDefinition) {
        self.attributes.insert(attribute.id.clone(), attribute);
        self.updated_at = Utc::now();
    }

    /// 设置父实体
    pub fn set_parent(&mut self, parent_id: String) {
        self.parent_entity = Some(parent_id);
        self.updated_at = Utc::now();
    }
}

impl RelationMetaModel {
    /// 创建新的关系元模型
    pub fn new(
        name: String,
        relation_type: MetaRelationType,
        source_entity: String,
        target_entity: String,
        cardinality: Cardinality,
    ) -> Self {
        let now = Utc::now();
        Self {
            id: Uuid::new_v4().to_string(),
            name,
            description: None,
            relation_type,
            source_entity,
            target_entity,
            cardinality,
            attributes: HashMap::new(),
            is_navigable: true,
            created_at: now,
            updated_at: now,
        }
    }
}

impl AttributeDefinition {
    /// 创建新的属性定义
    pub fn new(
        name: String,
        data_type: DataType,
        is_required: bool,
    ) -> Self {
        let now = Utc::now();
        Self {
            id: Uuid::new_v4().to_string(),
            name,
            description: None,
            data_type,
            is_required,
            is_unique: false,
            default_value: None,
            constraints: Vec::new(),
            created_at: now,
            updated_at: now,
        }
    }
}

impl Cardinality {
    /// 一对一关系
    pub fn one_to_one() -> Self {
        Self {
            source_min: 1,
            source_max: Some(1),
            target_min: 1,
            target_max: Some(1),
        }
    }

    /// 一对多关系
    pub fn one_to_many() -> Self {
        Self {
            source_min: 1,
            source_max: Some(1),
            target_min: 0,
            target_max: None,
        }
    }

    /// 多对多关系
    pub fn many_to_many() -> Self {
        Self {
            source_min: 0,
            source_max: None,
            target_min: 0,
            target_max: None,
        }
    }
}

/// 模型验证器
pub struct MetaModelValidator;

impl MetaModelValidator {
    /// 验证实体元模型
    pub fn validate_entity(entity: &EntityMetaModel) -> Result<(), Vec<String>> {
        let mut errors = Vec::new();

        if entity.name.is_empty() {
            errors.push("Entity name cannot be empty".to_string());
        }

        if entity.namespace.is_empty() {
            errors.push("Entity namespace cannot be empty".to_string());
        }

        // 验证属性
        for (attr_id, attr) in &entity.attributes {
            if attr.id != *attr_id {
                errors.push(format!("Attribute ID mismatch: {} vs {}", attr.id, attr_id));
            }
            if attr.name.is_empty() {
                errors.push(format!("Attribute {} name cannot be empty", attr.id));
            }
        }

        if errors.is_empty() {
            Ok(())
        } else {
            Err(errors)
        }
    }

    /// 验证关系元模型
    pub fn validate_relation(relation: &RelationMetaModel) -> Result<(), Vec<String>> {
        let mut errors = Vec::new();

        if relation.name.is_empty() {
            errors.push("Relation name cannot be empty".to_string());
        }

        if relation.source_entity.is_empty() {
            errors.push("Source entity cannot be empty".to_string());
        }

        if relation.target_entity.is_empty() {
            errors.push("Target entity cannot be empty".to_string());
        }

        // 验证基数
        if relation.cardinality.source_min > relation.cardinality.source_max.unwrap_or(u32::MAX) {
            errors.push("Source cardinality min cannot be greater than max".to_string());
        }

        if relation.cardinality.target_min > relation.cardinality.target_max.unwrap_or(u32::MAX) {
            errors.push("Target cardinality min cannot be greater than max".to_string());
        }

        if errors.is_empty() {
            Ok(())
        } else {
            Err(errors)
        }
    }
}

/// 实例管理器
pub struct InstanceManager {
    pub entities: HashMap<String, EntityInstance>,
    pub relations: HashMap<String, RelationInstance>,
    pub meta_repository: MetaModelRepository,
}

impl InstanceManager {
    pub fn new(meta_repository: MetaModelRepository) -> Self {
        Self {
            entities: HashMap::new(),
            relations: HashMap::new(),
            meta_repository,
        }
    }

    /// 创建实体实例
    pub fn create_entity_instance(
        &mut self,
        meta_model_id: String,
        attributes: HashMap<String, serde_json::Value>,
    ) -> Result<String, String> {
        // 验证元模型是否存在
        let meta_model = self.meta_repository.entities.get(&meta_model_id)
            .ok_or_else(|| format!("Meta model {} not found", meta_model_id))?;

        // 验证必需属性
        for (attr_id, attr_def) in &meta_model.attributes {
            if attr_def.is_required && !attributes.contains_key(attr_id) {
                return Err(format!("Required attribute {} is missing", attr_def.name));
            }
        }

        let instance = EntityInstance {
            id: Uuid::new_v4().to_string(),
            meta_model_id,
            attributes,
            created_at: Utc::now(),
            updated_at: Utc::now(),
        };

        let instance_id = instance.id.clone();
        self.entities.insert(instance_id.clone(), instance);
        Ok(instance_id)
    }

    /// 创建关系实例
    pub fn create_relation_instance(
        &mut self,
        meta_model_id: String,
        source_instance_id: String,
        target_instance_id: String,
        attributes: HashMap<String, serde_json::Value>,
    ) -> Result<String, String> {
        // 验证关系元模型是否存在
        let _relation_meta = self.meta_repository.relations.get(&meta_model_id)
            .ok_or_else(|| format!("Relation meta model {} not found", meta_model_id))?;

        // 验证实例是否存在
        if !self.entities.contains_key(&source_instance_id) {
            return Err(format!("Source instance {} not found", source_instance_id));
        }
        if !self.entities.contains_key(&target_instance_id) {
            return Err(format!("Target instance {} not found", target_instance_id));
        }

        let instance = RelationInstance {
            id: Uuid::new_v4().to_string(),
            meta_model_id,
            source_instance_id,
            target_instance_id,
            attributes,
            created_at: Utc::now(),
            updated_at: Utc::now(),
        };

        let instance_id = instance.id.clone();
        self.relations.insert(instance_id.clone(), instance);
        Ok(instance_id)
    }

    /// 查询实体的所有关系实例
    pub fn get_entity_relation_instances(&self, entity_id: &str) -> Vec<&RelationInstance> {
        self.relations
            .values()
            .filter(|relation| {
                relation.source_instance_id == entity_id || relation.target_instance_id == entity_id
            })
            .collect()
    }
}

/// 工业领域特定的元模型扩展
pub mod industrial_extensions {
    use super::*;

    /// BOM类型枚举
    #[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
    pub enum BomType {
        /// 工程BOM - Engineering BOM
        EBOM,
        /// 制造BOM - Manufacturing BOM
        MBOM,
        /// 服务BOM - Service BOM
        SBOM,
        /// 销售BOM - Sales BOM
        SalesBOM,
    }

    /// 产品生命周期阶段
    #[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
    pub enum LifecycleStage {
        Concept,
        Design,
        Development,
        Production,
        Service,
        Retirement,
    }

    /// 工业实体类型
    #[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
    pub enum IndustrialEntityType {
        Product,
        Component,
        Material,
        Process,
        Resource,
        Requirement,
        Document,
    }

    /// BOM项目
    #[derive(Debug, Clone, Serialize, Deserialize)]
    pub struct BomItem {
        pub id: String,
        pub parent_id: Option<String>,
        pub component_id: String,
        pub quantity: f64,
        pub unit: String,
        pub position: Option<String>,
        pub level: u32,
        pub bom_type: BomType,
        pub effective_date: Option<DateTime<Utc>>,
        pub obsolete_date: Option<DateTime<Utc>>,
        pub created_at: DateTime<Utc>,
        pub updated_at: DateTime<Utc>,
    }

    /// 工业实体元模型
    #[derive(Debug, Clone, Serialize, Deserialize)]
    pub struct IndustrialEntityMetaModel {
        pub base: EntityMetaModel,
        pub entity_type: IndustrialEntityType,
        pub lifecycle_stage: LifecycleStage,
        pub industry_domain: String,
        pub compliance_standards: Vec<String>,
    }

    /// 产品结构关系
    #[derive(Debug, Clone, Serialize, Deserialize)]
    pub struct ProductStructureRelation {
        pub base: RelationMetaModel,
        pub bom_type: BomType,
        pub quantity: f64,
        pub unit: String,
        pub position: Option<String>,
        pub is_critical: bool,
    }

    /// 工业模板库
    #[derive(Debug, Clone, Serialize, Deserialize)]
    pub struct IndustrialTemplateLibrary {
        pub bom_templates: HashMap<String, BomTemplate>,
        pub process_templates: HashMap<String, ProcessTemplate>,
        pub requirement_templates: HashMap<String, RequirementTemplate>,
    }

    /// BOM模板
    #[derive(Debug, Clone, Serialize, Deserialize)]
    pub struct BomTemplate {
        pub id: String,
        pub name: String,
        pub description: Option<String>,
        pub bom_type: BomType,
        pub industry_domain: String,
        pub template_entities: Vec<EntityMetaModel>,
        pub template_relations: Vec<RelationMetaModel>,
        pub created_at: DateTime<Utc>,
    }

    /// 工艺模板
    #[derive(Debug, Clone, Serialize, Deserialize)]
    pub struct ProcessTemplate {
        pub id: String,
        pub name: String,
        pub description: Option<String>,
        pub process_type: String,
        pub industry_domain: String,
        pub template_entities: Vec<EntityMetaModel>,
        pub template_relations: Vec<RelationMetaModel>,
        pub created_at: DateTime<Utc>,
    }

    /// 需求模板
    #[derive(Debug, Clone, Serialize, Deserialize)]
    pub struct RequirementTemplate {
        pub id: String,
        pub name: String,
        pub description: Option<String>,
        pub requirement_type: String,
        pub template_entities: Vec<EntityMetaModel>,
        pub template_relations: Vec<RelationMetaModel>,
        pub created_at: DateTime<Utc>,
    }

    impl BomItem {
        pub fn new(
            parent_id: Option<String>,
            component_id: String,
            quantity: f64,
            unit: String,
            bom_type: BomType,
        ) -> Self {
            let now = Utc::now();
            Self {
                id: Uuid::new_v4().to_string(),
                parent_id,
                component_id,
                quantity,
                unit,
                position: None,
                level: 0,
                bom_type,
                effective_date: None,
                obsolete_date: None,
                created_at: now,
                updated_at: now,
            }
        }
    }

    impl IndustrialEntityMetaModel {
        pub fn new(
            name: String,
            namespace: String,
            entity_type: IndustrialEntityType,
            industry_domain: String,
        ) -> Self {
            Self {
                base: EntityMetaModel::new(name, namespace, None),
                entity_type,
                lifecycle_stage: LifecycleStage::Concept,
                industry_domain,
                compliance_standards: Vec::new(),
            }
        }
    }

    impl IndustrialTemplateLibrary {
        pub fn new() -> Self {
            Self {
                bom_templates: HashMap::new(),
                process_templates: HashMap::new(),
                requirement_templates: HashMap::new(),
            }
        }

        /// 创建标准汽车BOM模板
        pub fn create_automotive_bom_template(&mut self) -> String {
            let template_id = Uuid::new_v4().to_string();

            let mut template_entities = Vec::new();
            let mut template_relations = Vec::new();

            // 创建车辆实体
            let mut vehicle_entity = EntityMetaModel::new(
                "Vehicle".to_string(),
                "automotive".to_string(),
                Some("Complete vehicle entity".to_string()),
            );
            vehicle_entity.add_attribute(AttributeDefinition::new(
                "vin".to_string(),
                DataType::String,
                true,
            ));
            vehicle_entity.add_attribute(AttributeDefinition::new(
                "model".to_string(),
                DataType::String,
                true,
            ));
            template_entities.push(vehicle_entity);

            // 创建子系统实体
            let subsystem_entity = EntityMetaModel::new(
                "Subsystem".to_string(),
                "automotive".to_string(),
                Some("Vehicle subsystem".to_string()),
            );
            template_entities.push(subsystem_entity);

            // 创建组合关系
            let composition_relation = RelationMetaModel::new(
                "VehicleSubsystemComposition".to_string(),
                MetaRelationType::Composition,
                "Vehicle".to_string(),
                "Subsystem".to_string(),
                Cardinality::one_to_many(),
            );
            template_relations.push(composition_relation);

            let template = BomTemplate {
                id: template_id.clone(),
                name: "Automotive BOM Template".to_string(),
                description: Some("Standard automotive BOM structure".to_string()),
                bom_type: BomType::EBOM,
                industry_domain: "automotive".to_string(),
                template_entities,
                template_relations,
                created_at: Utc::now(),
            };

            self.bom_templates.insert(template_id.clone(), template);
            template_id
        }
    }

    impl Default for IndustrialTemplateLibrary {
        fn default() -> Self {
            Self::new()
        }
    }
}

/// 业务流程元模型扩展 - 销售订单、发票、会计凭证
pub mod business_process_extensions {
    use super::*;

    /// 业务单据类型
    #[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
    pub enum BusinessDocumentType {
        /// 销售订单
        SalesOrder,
        /// 销售发票
        SalesInvoice,
        /// 会计凭证
        AccountingVoucher,
        /// 收款单
        Receipt,
        /// 付款单
        Payment,
        /// 采购订单
        PurchaseOrder,
        /// 采购发票
        PurchaseInvoice,
    }

    /// 单据状态
    #[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
    pub enum DocumentStatus {
        Draft,      // 草稿
        Pending,    // 待审核
        Approved,   // 已审核
        Processing, // 处理中
        Completed,  // 已完成
        Cancelled,  // 已取消
        Closed,     // 已关闭
    }

    /// 会计科目类型
    #[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
    pub enum AccountType {
        Asset,      // 资产
        Liability,  // 负债
        Equity,     // 所有者权益
        Revenue,    // 收入
        Expense,    // 费用
        Cost,       // 成本
    }

    /// 借贷方向
    #[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
    pub enum DebitCreditDirection {
        Debit,  // 借方
        Credit, // 贷方
    }

    /// 业务实体模板生成器
    pub struct BusinessEntityGenerator {
        pub meta_repo: MetaModelRepository,
    }

    impl BusinessEntityGenerator {
        pub fn new() -> Self {
            Self {
                meta_repo: MetaModelRepository::new(),
            }
        }

        /// 生成销售订单实体模型
        pub fn generate_sales_order_entity(&mut self) -> Result<String, String> {
            let mut sales_order_entity = EntityMetaModel::new(
                "SalesOrder".to_string(),
                "business.sales".to_string(),
                Some("销售订单实体模型".to_string()),
            );

            // 添加销售订单基本属性
            sales_order_entity.add_attribute(AttributeDefinition::new(
                "order_number".to_string(),
                DataType::String,
                true,
            ));

            sales_order_entity.add_attribute(AttributeDefinition::new(
                "order_date".to_string(),
                DataType::DateTime,
                true,
            ));

            sales_order_entity.add_attribute(AttributeDefinition::new(
                "customer_id".to_string(),
                DataType::Reference("Customer".to_string()),
                true,
            ));

            sales_order_entity.add_attribute(AttributeDefinition::new(
                "customer_name".to_string(),
                DataType::String,
                true,
            ));

            sales_order_entity.add_attribute(AttributeDefinition::new(
                "total_amount".to_string(),
                DataType::Float,
                true,
            ));

            sales_order_entity.add_attribute(AttributeDefinition::new(
                "currency".to_string(),
                DataType::String,
                true,
            ));

            sales_order_entity.add_attribute(AttributeDefinition::new(
                "status".to_string(),
                DataType::String,
                true,
            ));

            sales_order_entity.add_attribute(AttributeDefinition::new(
                "delivery_date".to_string(),
                DataType::DateTime,
                false,
            ));

            sales_order_entity.add_attribute(AttributeDefinition::new(
                "sales_person".to_string(),
                DataType::String,
                false,
            ));

            sales_order_entity.add_attribute(AttributeDefinition::new(
                "payment_terms".to_string(),
                DataType::String,
                false,
            ));

            sales_order_entity.add_attribute(AttributeDefinition::new(
                "notes".to_string(),
                DataType::String,
                false,
            ));

            let entity_id = sales_order_entity.id.clone();
            self.meta_repo.add_entity_meta_model(sales_order_entity)?;
            Ok(entity_id)
        }

        /// 生成销售订单明细实体模型
        pub fn generate_sales_order_line_entity(&mut self) -> Result<String, String> {
            let mut order_line_entity = EntityMetaModel::new(
                "SalesOrderLine".to_string(),
                "business.sales".to_string(),
                Some("销售订单明细实体模型".to_string()),
            );

            order_line_entity.add_attribute(AttributeDefinition::new(
                "line_number".to_string(),
                DataType::Integer,
                true,
            ));

            order_line_entity.add_attribute(AttributeDefinition::new(
                "product_id".to_string(),
                DataType::Reference("Product".to_string()),
                true,
            ));

            order_line_entity.add_attribute(AttributeDefinition::new(
                "product_name".to_string(),
                DataType::String,
                true,
            ));

            order_line_entity.add_attribute(AttributeDefinition::new(
                "quantity".to_string(),
                DataType::Float,
                true,
            ));

            order_line_entity.add_attribute(AttributeDefinition::new(
                "unit_price".to_string(),
                DataType::Float,
                true,
            ));

            order_line_entity.add_attribute(AttributeDefinition::new(
                "line_amount".to_string(),
                DataType::Float,
                true,
            ));

            order_line_entity.add_attribute(AttributeDefinition::new(
                "discount_rate".to_string(),
                DataType::Float,
                false,
            ));

            order_line_entity.add_attribute(AttributeDefinition::new(
                "tax_rate".to_string(),
                DataType::Float,
                false,
            ));

            let entity_id = order_line_entity.id.clone();
            self.meta_repo.add_entity_meta_model(order_line_entity)?;
            Ok(entity_id)
        }

        /// 生成销售发票实体模型
        pub fn generate_sales_invoice_entity(&mut self) -> Result<String, String> {
            let mut invoice_entity = EntityMetaModel::new(
                "SalesInvoice".to_string(),
                "business.finance".to_string(),
                Some("销售发票实体模型".to_string()),
            );

            invoice_entity.add_attribute(AttributeDefinition::new(
                "invoice_number".to_string(),
                DataType::String,
                true,
            ));

            invoice_entity.add_attribute(AttributeDefinition::new(
                "invoice_date".to_string(),
                DataType::DateTime,
                true,
            ));

            invoice_entity.add_attribute(AttributeDefinition::new(
                "sales_order_id".to_string(),
                DataType::Reference("SalesOrder".to_string()),
                false,
            ));

            invoice_entity.add_attribute(AttributeDefinition::new(
                "customer_id".to_string(),
                DataType::Reference("Customer".to_string()),
                true,
            ));

            invoice_entity.add_attribute(AttributeDefinition::new(
                "customer_name".to_string(),
                DataType::String,
                true,
            ));

            invoice_entity.add_attribute(AttributeDefinition::new(
                "subtotal_amount".to_string(),
                DataType::Float,
                true,
            ));

            invoice_entity.add_attribute(AttributeDefinition::new(
                "tax_amount".to_string(),
                DataType::Float,
                true,
            ));

            invoice_entity.add_attribute(AttributeDefinition::new(
                "total_amount".to_string(),
                DataType::Float,
                true,
            ));

            invoice_entity.add_attribute(AttributeDefinition::new(
                "currency".to_string(),
                DataType::String,
                true,
            ));

            invoice_entity.add_attribute(AttributeDefinition::new(
                "status".to_string(),
                DataType::String,
                true,
            ));

            invoice_entity.add_attribute(AttributeDefinition::new(
                "due_date".to_string(),
                DataType::DateTime,
                false,
            ));

            invoice_entity.add_attribute(AttributeDefinition::new(
                "payment_status".to_string(),
                DataType::String,
                false,
            ));

            let entity_id = invoice_entity.id.clone();
            self.meta_repo.add_entity_meta_model(invoice_entity)?;
            Ok(entity_id)
        }

        /// 生成会计凭证实体模型
        pub fn generate_accounting_voucher_entity(&mut self) -> Result<String, String> {
            let mut voucher_entity = EntityMetaModel::new(
                "AccountingVoucher".to_string(),
                "business.accounting".to_string(),
                Some("会计凭证实体模型".to_string()),
            );

            voucher_entity.add_attribute(AttributeDefinition::new(
                "voucher_number".to_string(),
                DataType::String,
                true,
            ));

            voucher_entity.add_attribute(AttributeDefinition::new(
                "voucher_date".to_string(),
                DataType::DateTime,
                true,
            ));

            voucher_entity.add_attribute(AttributeDefinition::new(
                "voucher_type".to_string(),
                DataType::String,
                true,
            ));

            voucher_entity.add_attribute(AttributeDefinition::new(
                "reference_document_type".to_string(),
                DataType::String,
                false,
            ));

            voucher_entity.add_attribute(AttributeDefinition::new(
                "reference_document_id".to_string(),
                DataType::String,
                false,
            ));

            voucher_entity.add_attribute(AttributeDefinition::new(
                "total_debit_amount".to_string(),
                DataType::Float,
                true,
            ));

            voucher_entity.add_attribute(AttributeDefinition::new(
                "total_credit_amount".to_string(),
                DataType::Float,
                true,
            ));

            voucher_entity.add_attribute(AttributeDefinition::new(
                "currency".to_string(),
                DataType::String,
                true,
            ));

            voucher_entity.add_attribute(AttributeDefinition::new(
                "status".to_string(),
                DataType::String,
                true,
            ));

            voucher_entity.add_attribute(AttributeDefinition::new(
                "prepared_by".to_string(),
                DataType::String,
                false,
            ));

            voucher_entity.add_attribute(AttributeDefinition::new(
                "approved_by".to_string(),
                DataType::String,
                false,
            ));

            voucher_entity.add_attribute(AttributeDefinition::new(
                "description".to_string(),
                DataType::String,
                false,
            ));

            let entity_id = voucher_entity.id.clone();
            self.meta_repo.add_entity_meta_model(voucher_entity)?;
            Ok(entity_id)
        }

        /// 生成会计凭证明细实体模型
        pub fn generate_voucher_line_entity(&mut self) -> Result<String, String> {
            let mut voucher_line_entity = EntityMetaModel::new(
                "VoucherLine".to_string(),
                "business.accounting".to_string(),
                Some("会计凭证明细实体模型".to_string()),
            );

            voucher_line_entity.add_attribute(AttributeDefinition::new(
                "line_number".to_string(),
                DataType::Integer,
                true,
            ));

            voucher_line_entity.add_attribute(AttributeDefinition::new(
                "account_code".to_string(),
                DataType::String,
                true,
            ));

            voucher_line_entity.add_attribute(AttributeDefinition::new(
                "account_name".to_string(),
                DataType::String,
                true,
            ));

            voucher_line_entity.add_attribute(AttributeDefinition::new(
                "debit_amount".to_string(),
                DataType::Float,
                false,
            ));

            voucher_line_entity.add_attribute(AttributeDefinition::new(
                "credit_amount".to_string(),
                DataType::Float,
                false,
            ));

            voucher_line_entity.add_attribute(AttributeDefinition::new(
                "description".to_string(),
                DataType::String,
                false,
            ));

            voucher_line_entity.add_attribute(AttributeDefinition::new(
                "cost_center".to_string(),
                DataType::String,
                false,
            ));

            voucher_line_entity.add_attribute(AttributeDefinition::new(
                "project_id".to_string(),
                DataType::Reference("Project".to_string()),
                false,
            ));

            let entity_id = voucher_line_entity.id.clone();
            self.meta_repo.add_entity_meta_model(voucher_line_entity)?;
            Ok(entity_id)
        }

        /// 生成客户实体模型
        pub fn generate_customer_entity(&mut self) -> Result<String, String> {
            let mut customer_entity = EntityMetaModel::new(
                "Customer".to_string(),
                "business.master".to_string(),
                Some("客户主数据实体模型".to_string()),
            );

            customer_entity.add_attribute(AttributeDefinition::new(
                "customer_code".to_string(),
                DataType::String,
                true,
            ));

            customer_entity.add_attribute(AttributeDefinition::new(
                "customer_name".to_string(),
                DataType::String,
                true,
            ));

            customer_entity.add_attribute(AttributeDefinition::new(
                "customer_type".to_string(),
                DataType::String,
                true,
            ));

            customer_entity.add_attribute(AttributeDefinition::new(
                "contact_person".to_string(),
                DataType::String,
                false,
            ));

            customer_entity.add_attribute(AttributeDefinition::new(
                "phone".to_string(),
                DataType::String,
                false,
            ));

            customer_entity.add_attribute(AttributeDefinition::new(
                "email".to_string(),
                DataType::String,
                false,
            ));

            customer_entity.add_attribute(AttributeDefinition::new(
                "address".to_string(),
                DataType::String,
                false,
            ));

            customer_entity.add_attribute(AttributeDefinition::new(
                "credit_limit".to_string(),
                DataType::Float,
                false,
            ));

            customer_entity.add_attribute(AttributeDefinition::new(
                "payment_terms".to_string(),
                DataType::String,
                false,
            ));

            let entity_id = customer_entity.id.clone();
            self.meta_repo.add_entity_meta_model(customer_entity)?;
            Ok(entity_id)
        }

        /// 生成业务流程关系模型
        pub fn generate_business_relations(&mut self) -> Result<Vec<String>, String> {
            let mut relation_ids = Vec::new();

            // 获取实体ID
            let sales_order_id = self.get_entity_id("SalesOrder")?;
            let order_line_id = self.get_entity_id("SalesOrderLine")?;
            let invoice_id = self.get_entity_id("SalesInvoice")?;
            let voucher_id = self.get_entity_id("AccountingVoucher")?;
            let voucher_line_id = self.get_entity_id("VoucherLine")?;
            let customer_id = self.get_entity_id("Customer")?;

            // 1. 销售订单与订单明细的组合关系
            let order_line_relation = RelationMetaModel::new(
                "SalesOrderToLines".to_string(),
                MetaRelationType::Composition,
                sales_order_id.clone(),
                order_line_id,
                Cardinality::one_to_many(),
            );
            relation_ids.push(order_line_relation.id.clone());
            self.meta_repo.add_relation_meta_model(order_line_relation)?;

            // 2. 销售订单与客户的关联关系
            let order_customer_relation = RelationMetaModel::new(
                "SalesOrderToCustomer".to_string(),
                MetaRelationType::Association,
                sales_order_id.clone(),
                customer_id.clone(),
                Cardinality {
                    source_min: 0,
                    source_max: None,
                    target_min: 1,
                    target_max: Some(1),
                },
            );
            relation_ids.push(order_customer_relation.id.clone());
            self.meta_repo.add_relation_meta_model(order_customer_relation)?;

            // 3. 销售发票与销售订单的依赖关系
            let invoice_order_relation = RelationMetaModel::new(
                "InvoiceToSalesOrder".to_string(),
                MetaRelationType::Dependency,
                invoice_id.clone(),
                sales_order_id,
                Cardinality {
                    source_min: 0,
                    source_max: None,
                    target_min: 0,
                    target_max: Some(1),
                },
            );
            relation_ids.push(invoice_order_relation.id.clone());
            self.meta_repo.add_relation_meta_model(invoice_order_relation)?;

            // 4. 销售发票与客户的关联关系
            let invoice_customer_relation = RelationMetaModel::new(
                "InvoiceToCustomer".to_string(),
                MetaRelationType::Association,
                invoice_id.clone(),
                customer_id,
                Cardinality {
                    source_min: 0,
                    source_max: None,
                    target_min: 1,
                    target_max: Some(1),
                },
            );
            relation_ids.push(invoice_customer_relation.id.clone());
            self.meta_repo.add_relation_meta_model(invoice_customer_relation)?;

            // 5. 会计凭证与凭证明细的组合关系
            let voucher_line_relation = RelationMetaModel::new(
                "VoucherToLines".to_string(),
                MetaRelationType::Composition,
                voucher_id.clone(),
                voucher_line_id,
                Cardinality::one_to_many(),
            );
            relation_ids.push(voucher_line_relation.id.clone());
            self.meta_repo.add_relation_meta_model(voucher_line_relation)?;

            // 6. 会计凭证与销售发票的依赖关系
            let voucher_invoice_relation = RelationMetaModel::new(
                "VoucherToInvoice".to_string(),
                MetaRelationType::Dependency,
                voucher_id,
                invoice_id,
                Cardinality {
                    source_min: 0,
                    source_max: None,
                    target_min: 0,
                    target_max: Some(1),
                },
            );
            relation_ids.push(voucher_invoice_relation.id.clone());
            self.meta_repo.add_relation_meta_model(voucher_invoice_relation)?;

            Ok(relation_ids)
        }

        /// 生成完整的销售业务流程模板
        pub fn generate_sales_business_template(&mut self) -> Result<BusinessProcessTemplate, String> {
            // 生成所有实体
            let customer_id = self.generate_customer_entity()?;
            let sales_order_id = self.generate_sales_order_entity()?;
            let order_line_id = self.generate_sales_order_line_entity()?;
            let invoice_id = self.generate_sales_invoice_entity()?;
            let voucher_id = self.generate_accounting_voucher_entity()?;
            let voucher_line_id = self.generate_voucher_line_entity()?;

            // 生成关系
            let relation_ids = self.generate_business_relations()?;

            let template = BusinessProcessTemplate {
                id: uuid::Uuid::new_v4().to_string(),
                name: "销售业务流程模板".to_string(),
                description: Some("包含销售订单、发票、会计凭证的完整业务流程".to_string()),
                process_type: "sales_process".to_string(),
                entity_ids: vec![
                    customer_id,
                    sales_order_id,
                    order_line_id,
                    invoice_id,
                    voucher_id,
                    voucher_line_id,
                ],
                relation_ids,
                workflow_steps: vec![
                    WorkflowStep {
                        step_id: "create_order".to_string(),
                        step_name: "创建销售订单".to_string(),
                        entity_type: "SalesOrder".to_string(),
                        action: "create".to_string(),
                        next_steps: vec!["create_invoice".to_string()],
                    },
                    WorkflowStep {
                        step_id: "create_invoice".to_string(),
                        step_name: "生成销售发票".to_string(),
                        entity_type: "SalesInvoice".to_string(),
                        action: "create".to_string(),
                        next_steps: vec!["create_voucher".to_string()],
                    },
                    WorkflowStep {
                        step_id: "create_voucher".to_string(),
                        step_name: "生成会计凭证".to_string(),
                        entity_type: "AccountingVoucher".to_string(),
                        action: "create".to_string(),
                        next_steps: vec![],
                    },
                ],
                created_at: chrono::Utc::now(),
            };

            Ok(template)
        }

        /// 辅助方法：根据实体名称获取实体ID
        fn get_entity_id(&self, entity_name: &str) -> Result<String, String> {
            self.meta_repo
                .entities
                .values()
                .find(|entity| entity.name == entity_name)
                .map(|entity| entity.id.clone())
                .ok_or_else(|| format!("Entity {} not found", entity_name))
        }
    }

    /// 业务流程模板
    #[derive(Debug, Clone, Serialize, Deserialize)]
    pub struct BusinessProcessTemplate {
        pub id: String,
        pub name: String,
        pub description: Option<String>,
        pub process_type: String,
        pub entity_ids: Vec<String>,
        pub relation_ids: Vec<String>,
        pub workflow_steps: Vec<WorkflowStep>,
        pub created_at: DateTime<Utc>,
    }

    /// 工作流步骤
    #[derive(Debug, Clone, Serialize, Deserialize)]
    pub struct WorkflowStep {
        pub step_id: String,
        pub step_name: String,
        pub entity_type: String,
        pub action: String,
        pub next_steps: Vec<String>,
    }

    impl Default for BusinessEntityGenerator {
        fn default() -> Self {
            Self::new()
        }
    }
}

/// 数据图谱和追溯功能
pub mod data_graph {
    use super::*;

    /// 数据节点
    #[derive(Debug, Clone, Serialize, Deserialize)]
    pub struct DataNode {
        pub id: String,
        pub entity_instance_id: String,
        pub node_type: String,
        pub properties: HashMap<String, serde_json::Value>,
        pub created_at: DateTime<Utc>,
    }

    /// 数据边
    #[derive(Debug, Clone, Serialize, Deserialize)]
    pub struct DataEdge {
        pub id: String,
        pub source_node_id: String,
        pub target_node_id: String,
        pub relation_instance_id: String,
        pub edge_type: String,
        pub properties: HashMap<String, serde_json::Value>,
        pub created_at: DateTime<Utc>,
    }

    /// 数据图谱
    #[derive(Debug, Clone, Serialize, Deserialize)]
    pub struct DataGraph {
        pub nodes: HashMap<String, DataNode>,
        pub edges: HashMap<String, DataEdge>,
        pub indexes: HashMap<String, Vec<String>>, // 索引：属性值 -> 节点ID列表
    }

    /// 追溯路径
    #[derive(Debug, Clone, Serialize, Deserialize)]
    pub struct TracePath {
        pub path_id: String,
        pub source_node_id: String,
        pub target_node_id: String,
        pub nodes: Vec<String>,
        pub edges: Vec<String>,
        pub path_length: usize,
        pub created_at: DateTime<Utc>,
    }

    /// 图谱查询器
    pub struct GraphQueryEngine {
        pub graph: DataGraph,
    }

    impl DataGraph {
        pub fn new() -> Self {
            Self {
                nodes: HashMap::new(),
                edges: HashMap::new(),
                indexes: HashMap::new(),
            }
        }

        /// 添加节点
        pub fn add_node(&mut self, node: DataNode) {
            // 更新索引
            for (key, value) in &node.properties {
                let index_key = format!("{}:{}", key, value);
                self.indexes
                    .entry(index_key)
                    .or_insert_with(Vec::new)
                    .push(node.id.clone());
            }

            self.nodes.insert(node.id.clone(), node);
        }

        /// 添加边
        pub fn add_edge(&mut self, edge: DataEdge) {
            self.edges.insert(edge.id.clone(), edge);
        }

        /// 根据属性查找节点
        pub fn find_nodes_by_property(&self, key: &str, value: &serde_json::Value) -> Vec<&DataNode> {
            let index_key = format!("{}:{}", key, value);
            if let Some(node_ids) = self.indexes.get(&index_key) {
                node_ids
                    .iter()
                    .filter_map(|id| self.nodes.get(id))
                    .collect()
            } else {
                Vec::new()
            }
        }

        /// 获取节点的邻居
        pub fn get_neighbors(&self, node_id: &str) -> Vec<&DataNode> {
            let mut neighbors = Vec::new();

            for edge in self.edges.values() {
                if edge.source_node_id == node_id {
                    if let Some(target_node) = self.nodes.get(&edge.target_node_id) {
                        neighbors.push(target_node);
                    }
                } else if edge.target_node_id == node_id {
                    if let Some(source_node) = self.nodes.get(&edge.source_node_id) {
                        neighbors.push(source_node);
                    }
                }
            }

            neighbors
        }
    }

    impl GraphQueryEngine {
        pub fn new(graph: DataGraph) -> Self {
            Self { graph }
        }

        /// 深度优先搜索追溯路径
        pub fn trace_path(&self, source_id: &str, target_id: &str, max_depth: usize) -> Option<TracePath> {
            let mut visited = std::collections::HashSet::new();
            let mut path = Vec::new();
            let mut edge_path = Vec::new();

            if self.dfs_trace(source_id, target_id, &mut visited, &mut path, &mut edge_path, max_depth) {
                let path_length = path.len();
                Some(TracePath {
                    path_id: Uuid::new_v4().to_string(),
                    source_node_id: source_id.to_string(),
                    target_node_id: target_id.to_string(),
                    nodes: path,
                    edges: edge_path,
                    path_length,
                    created_at: Utc::now(),
                })
            } else {
                None
            }
        }

        fn dfs_trace(
            &self,
            current: &str,
            target: &str,
            visited: &mut std::collections::HashSet<String>,
            path: &mut Vec<String>,
            edge_path: &mut Vec<String>,
            max_depth: usize,
        ) -> bool {
            if path.len() >= max_depth {
                return false;
            }

            visited.insert(current.to_string());
            path.push(current.to_string());

            if current == target {
                return true;
            }

            // 查找相邻节点
            for edge in self.graph.edges.values() {
                let next_node = if edge.source_node_id == current {
                    &edge.target_node_id
                } else if edge.target_node_id == current {
                    &edge.source_node_id
                } else {
                    continue;
                };

                if !visited.contains(next_node) {
                    edge_path.push(edge.id.clone());
                    if self.dfs_trace(next_node, target, visited, path, edge_path, max_depth) {
                        return true;
                    }
                    edge_path.pop();
                }
            }

            path.pop();
            visited.remove(current);
            false
        }

        /// 批量追溯查询（支持百亿数据分钟级追溯）
        pub fn batch_trace(&self, queries: Vec<(String, String)>) -> Vec<Option<TracePath>> {
            // 这里可以实现并行处理和优化算法
            queries
                .into_iter()
                .map(|(source, target)| self.trace_path(&source, &target, 10))
                .collect()
        }
    }

    impl Default for DataGraph {
        fn default() -> Self {
            Self::new()
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use super::industrial_extensions::*;
    use super::data_graph::*;

    #[test]
    fn test_create_entity_meta_model() {
        let mut entity = EntityMetaModel::new(
            "Product".to_string(),
            "manufacturing".to_string(),
            Some("A manufacturing product".to_string()),
        );

        // 添加属性
        entity.add_attribute(AttributeDefinition::new(
            "name".to_string(),
            DataType::String,
            true,
        ));
        entity.add_attribute(AttributeDefinition::new(
            "price".to_string(),
            DataType::Float,
            false,
        ));

        assert_eq!(entity.name, "Product");
        assert_eq!(entity.namespace, "manufacturing");
        assert_eq!(entity.attributes.len(), 2);
        assert!(entity.attributes.contains_key(&entity.attributes.values().next().unwrap().id));
    }

    #[test]
    fn test_create_relation_meta_model() {
        let relation = RelationMetaModel::new(
            "ProductComponent".to_string(),
            MetaRelationType::Composition,
            "product_id".to_string(),
            "component_id".to_string(),
            Cardinality::one_to_many(),
        );

        assert_eq!(relation.name, "ProductComponent");
        assert_eq!(relation.relation_type, MetaRelationType::Composition);
        assert_eq!(relation.cardinality.source_max, Some(1));
        assert_eq!(relation.cardinality.target_max, None);
    }

    #[test]
    fn test_meta_model_repository() {
        let mut repo = MetaModelRepository::new();

        // 创建产品实体
        let product_entity = EntityMetaModel::new(
            "Product".to_string(),
            "manufacturing".to_string(),
            Some("Product entity".to_string()),
        );
        let product_id = product_entity.id.clone();

        // 创建组件实体
        let component_entity = EntityMetaModel::new(
            "Component".to_string(),
            "manufacturing".to_string(),
            Some("Component entity".to_string()),
        );
        let component_id = component_entity.id.clone();

        // 添加到仓库
        repo.add_entity_meta_model(product_entity).unwrap();
        repo.add_entity_meta_model(component_entity).unwrap();

        // 创建关系
        let relation = RelationMetaModel::new(
            "ProductComponent".to_string(),
            MetaRelationType::Composition,
            product_id.clone(),
            component_id.clone(),
            Cardinality::one_to_many(),
        );

        repo.add_relation_meta_model(relation).unwrap();

        assert_eq!(repo.entities.len(), 2);
        assert_eq!(repo.relations.len(), 1);
        assert_eq!(repo.namespaces.get("manufacturing").unwrap().len(), 2);
    }

    #[test]
    fn test_instance_manager() {
        let mut repo = MetaModelRepository::new();

        // 创建产品元模型
        let mut product_entity = EntityMetaModel::new(
            "Product".to_string(),
            "manufacturing".to_string(),
            Some("Product entity".to_string()),
        );
        let name_attr = AttributeDefinition::new(
            "name".to_string(),
            DataType::String,
            true,
        );
        let name_attr_id = name_attr.id.clone();
        product_entity.add_attribute(name_attr);
        let product_meta_id = product_entity.id.clone();
        repo.add_entity_meta_model(product_entity).unwrap();

        let mut instance_manager = InstanceManager::new(repo);

        // 创建产品实例 - 使用属性ID作为键
        let mut attributes = HashMap::new();
        attributes.insert(name_attr_id, serde_json::Value::String("iPhone".to_string()));

        let instance_id = instance_manager
            .create_entity_instance(product_meta_id, attributes)
            .unwrap();

        assert!(instance_manager.entities.contains_key(&instance_id));
    }

    #[test]
    fn test_industrial_bom_template() {
        let mut template_lib = IndustrialTemplateLibrary::new();
        let template_id = template_lib.create_automotive_bom_template();

        assert!(template_lib.bom_templates.contains_key(&template_id));
        let template = template_lib.bom_templates.get(&template_id).unwrap();
        assert_eq!(template.bom_type, BomType::EBOM);
        assert_eq!(template.industry_domain, "automotive");
        assert!(!template.template_entities.is_empty());
        assert!(!template.template_relations.is_empty());
    }

    #[test]
    fn test_data_graph_operations() {
        let mut graph = DataGraph::new();

        // 创建节点
        let node1 = DataNode {
            id: "node1".to_string(),
            entity_instance_id: "entity1".to_string(),
            node_type: "Product".to_string(),
            properties: {
                let mut props = HashMap::new();
                props.insert("name".to_string(), serde_json::Value::String("Product A".to_string()));
                props
            },
            created_at: Utc::now(),
        };

        let node2 = DataNode {
            id: "node2".to_string(),
            entity_instance_id: "entity2".to_string(),
            node_type: "Component".to_string(),
            properties: {
                let mut props = HashMap::new();
                props.insert("name".to_string(), serde_json::Value::String("Component B".to_string()));
                props
            },
            created_at: Utc::now(),
        };

        graph.add_node(node1);
        graph.add_node(node2);

        // 创建边
        let edge = DataEdge {
            id: "edge1".to_string(),
            source_node_id: "node1".to_string(),
            target_node_id: "node2".to_string(),
            relation_instance_id: "relation1".to_string(),
            edge_type: "composition".to_string(),
            properties: HashMap::new(),
            created_at: Utc::now(),
        };

        graph.add_edge(edge);

        assert_eq!(graph.nodes.len(), 2);
        assert_eq!(graph.edges.len(), 1);

        // 测试邻居查询
        let neighbors = graph.get_neighbors("node1");
        assert_eq!(neighbors.len(), 1);
        assert_eq!(neighbors[0].id, "node2");
    }

    #[test]
    fn test_graph_trace_path() {
        let mut graph = DataGraph::new();

        // 创建简单的链式图：node1 -> node2 -> node3
        for i in 1..=3 {
            let node = DataNode {
                id: format!("node{}", i),
                entity_instance_id: format!("entity{}", i),
                node_type: "TestNode".to_string(),
                properties: HashMap::new(),
                created_at: Utc::now(),
            };
            graph.add_node(node);
        }

        // 添加边
        for i in 1..=2 {
            let edge = DataEdge {
                id: format!("edge{}", i),
                source_node_id: format!("node{}", i),
                target_node_id: format!("node{}", i + 1),
                relation_instance_id: format!("relation{}", i),
                edge_type: "sequence".to_string(),
                properties: HashMap::new(),
                created_at: Utc::now(),
            };
            graph.add_edge(edge);
        }

        let query_engine = GraphQueryEngine::new(graph);
        let trace_result = query_engine.trace_path("node1", "node3", 10);

        assert!(trace_result.is_some());
        let trace = trace_result.unwrap();
        assert_eq!(trace.nodes.len(), 3);
        assert_eq!(trace.edges.len(), 2);
        assert_eq!(trace.source_node_id, "node1");
        assert_eq!(trace.target_node_id, "node3");
    }

    #[test]
    fn test_model_validation() {
        // 测试有效的实体模型
        let valid_entity = EntityMetaModel::new(
            "ValidEntity".to_string(),
            "test".to_string(),
            Some("A valid entity".to_string()),
        );
        assert!(MetaModelValidator::validate_entity(&valid_entity).is_ok());

        // 测试无效的实体模型（空名称）
        let invalid_entity = EntityMetaModel::new(
            "".to_string(),
            "test".to_string(),
            None,
        );
        let validation_result = MetaModelValidator::validate_entity(&invalid_entity);
        assert!(validation_result.is_err());
        assert!(validation_result.unwrap_err().iter().any(|e| e.contains("name cannot be empty")));
    }

    #[test]
    fn test_business_entity_generation() {
        use super::business_process_extensions::*;

        let mut generator = BusinessEntityGenerator::new();

        // 测试生成销售订单实体
        let sales_order_id = generator.generate_sales_order_entity().unwrap();
        assert!(generator.meta_repo.entities.contains_key(&sales_order_id));

        let sales_order = generator.meta_repo.entities.get(&sales_order_id).unwrap();
        assert_eq!(sales_order.name, "SalesOrder");
        assert_eq!(sales_order.namespace, "business.sales");
        assert!(!sales_order.attributes.is_empty());

        // 验证必需属性存在
        let has_order_number = sales_order.attributes.values()
            .any(|attr| attr.name == "order_number" && attr.is_required);
        assert!(has_order_number);

        // 测试生成销售发票实体
        let invoice_id = generator.generate_sales_invoice_entity().unwrap();
        assert!(generator.meta_repo.entities.contains_key(&invoice_id));

        let invoice = generator.meta_repo.entities.get(&invoice_id).unwrap();
        assert_eq!(invoice.name, "SalesInvoice");
        assert_eq!(invoice.namespace, "business.finance");

        // 测试生成会计凭证实体
        let voucher_id = generator.generate_accounting_voucher_entity().unwrap();
        assert!(generator.meta_repo.entities.contains_key(&voucher_id));

        let voucher = generator.meta_repo.entities.get(&voucher_id).unwrap();
        assert_eq!(voucher.name, "AccountingVoucher");
        assert_eq!(voucher.namespace, "business.accounting");

        println!("✓ 业务实体生成测试通过");
    }

    #[test]
    fn test_business_process_template() {
        use super::business_process_extensions::*;

        let mut generator = BusinessEntityGenerator::new();

        // 生成完整的销售业务流程模板
        let template = generator.generate_sales_business_template().unwrap();

        assert_eq!(template.name, "销售业务流程模板");
        assert_eq!(template.process_type, "sales_process");
        assert_eq!(template.entity_ids.len(), 6); // 6个实体
        assert!(!template.relation_ids.is_empty()); // 有关系
        assert_eq!(template.workflow_steps.len(), 3); // 3个工作流步骤

        // 验证工作流步骤
        let step_names: Vec<&String> = template.workflow_steps.iter()
            .map(|step| &step.step_name)
            .collect();
        assert!(step_names.contains(&&"创建销售订单".to_string()));
        assert!(step_names.contains(&&"生成销售发票".to_string()));
        assert!(step_names.contains(&&"生成会计凭证".to_string()));

        // 验证实体都已生成
        assert_eq!(generator.meta_repo.entities.len(), 6);

        // 验证关系都已生成
        assert!(!generator.meta_repo.relations.is_empty());

        println!("✓ 业务流程模板测试通过");
    }

    #[test]
    fn test_business_relations() {
        use super::business_process_extensions::*;

        let mut generator = BusinessEntityGenerator::new();

        // 先生成实体
        generator.generate_customer_entity().unwrap();
        generator.generate_sales_order_entity().unwrap();
        generator.generate_sales_order_line_entity().unwrap();
        generator.generate_sales_invoice_entity().unwrap();
        generator.generate_accounting_voucher_entity().unwrap();
        generator.generate_voucher_line_entity().unwrap();

        // 生成关系
        let relation_ids = generator.generate_business_relations().unwrap();

        assert!(!relation_ids.is_empty());
        assert_eq!(generator.meta_repo.relations.len(), relation_ids.len());

        // 验证特定关系类型
        let composition_relations: Vec<_> = generator.meta_repo.relations.values()
            .filter(|r| r.relation_type == MetaRelationType::Composition)
            .collect();
        assert!(!composition_relations.is_empty());

        let association_relations: Vec<_> = generator.meta_repo.relations.values()
            .filter(|r| r.relation_type == MetaRelationType::Association)
            .collect();
        assert!(!association_relations.is_empty());

        let dependency_relations: Vec<_> = generator.meta_repo.relations.values()
            .filter(|r| r.relation_type == MetaRelationType::Dependency)
            .collect();
        assert!(!dependency_relations.is_empty());

        println!("✓ 业务关系生成测试通过");
    }
}
