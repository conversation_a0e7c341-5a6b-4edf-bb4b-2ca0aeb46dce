use std::collections::HashMap;
use serde::{Deserialize, Serialize};

use super::{ColumnType, DataSetError};
use crate::data::cell::CellValue;

#[allow(dead_code)]
/// 列定义信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ColumnInfo {
    index: usize,
    column_type: ColumnType,
}

/// 行数据结构，包含值和子数据集
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RowData {
    // 行的列值
    values: Vec<CellValue>,
    // 子数据集映射：名称 -> 数据集
    children: Option<HashMap<String, RowDataSet>>,
}

impl RowData {
    /// 创建新的行数据
    pub fn new(values: Vec<CellValue>) -> Self {
        Self {
            values,
            children: None,
        }
    }

    /// 获取行值
    pub fn values(&self) -> &Vec<CellValue> {
        &self.values
    }

    /// 获取可变行值
    pub fn values_mut(&mut self) -> &mut Vec<CellValue> {
        &mut self.values
    }

    /// 获取指定的子数据集
    pub fn get_child(&self, name: &str) -> Option<&RowDataSet> {
        self.children.as_ref().and_then(|children| children.get(name))
    }

    /// 获取指定的子数据集的可变引用
    pub fn get_child_mut(&mut self, name: &str) -> Option<&mut RowDataSet> {
        self.children.as_mut().and_then(|children| children.get_mut(name))
    }

    /// 添加子数据集
    pub fn add_child(&mut self, name: String, dataset: RowDataSet) {
        if self.children.is_none() {
            self.children = Some(HashMap::new());
        }
        self.children.as_mut().unwrap().insert(name, dataset);
    }

    /// 移除子数据集
    pub fn remove_child(&mut self, name: &str) -> Option<RowDataSet> {
        self.children.as_mut().and_then(|children| children.remove(name))
    }
}

/// 行存储的数据集结构
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RowDataSet {
    // 数据集标识
    dataset_id: String,
    // 列定义映射：列名 -> (索引, 类型)
    schema: HashMap<String, ColumnInfo>,
    // 行数据存储
    rows: Vec<RowData>,
}

impl RowDataSet {
    /// 创建新的数据集
    pub fn new(dataset_id: String) -> Self {
        Self {
            dataset_id,
            schema: HashMap::new(),
            rows: Vec::new(),
        }
    }

    /// 获取数据集ID
    pub fn dataset_id(&self) -> &str {
        &self.dataset_id
    }

    /// 获取列数
    pub fn column_count(&self) -> usize {
        self.schema.len()
    }

    /// 获取行数
    pub fn row_count(&self) -> usize {
        self.rows.len()
    }

    /// 添加列定义
    pub fn add_column(&mut self, name: String, column_type: ColumnType) -> Result<(), DataSetError> {
        if self.schema.contains_key(&name) {
            return Err(DataSetError::ColumnNotFound); // 可以扩展错误类型以更好地表达列已存在
        }

        let index = self.schema.len();
        self.schema.insert(name, ColumnInfo { index, column_type });

        // 为现有行添加 NULL 值
        for row in &mut self.rows {
            row.values_mut().push(CellValue::Null);
        }

        Ok(())
    }

    /// 获取列信息
    pub fn get_column_info(&self, column_name: &str) -> Option<&ColumnInfo> {
        self.schema.get(column_name)
    }

    /// 添加一行数据
    pub fn add_row(&mut self, values: Vec<CellValue>) -> Result<(), DataSetError> {
        if values.len() != self.schema.len() {
            return Err(DataSetError::ColumnCountMismatch);
        }
        self.rows.push(RowData::new(values));
        Ok(())
    }

    /// 在指定位置插入一行数据
    pub fn insert_row(&mut self, index: usize, values: Vec<CellValue>) -> Result<(), DataSetError> {
        if values.len() != self.schema.len() {
            return Err(DataSetError::ColumnCountMismatch);
        }
        if index > self.rows.len() {
            return Err(DataSetError::IndexOutOfBounds);
        }
        self.rows.insert(index, RowData::new(values));
        Ok(())
    }

    /// 获取指定行
    pub fn get_row(&self, index: usize) -> Result<&RowData, DataSetError> {
        self.rows.get(index).ok_or(DataSetError::IndexOutOfBounds)
    }

    /// 获取指定行的可变引用
    pub fn get_row_mut(&mut self, index: usize) -> Result<&mut RowData, DataSetError> {
        self.rows.get_mut(index).ok_or(DataSetError::IndexOutOfBounds)
    }

    /// 删除指定行
    pub fn remove_row(&mut self, index: usize) -> Result<RowData, DataSetError> {
        if index >= self.rows.len() {
            return Err(DataSetError::IndexOutOfBounds);
        }
        Ok(self.rows.remove(index))
    }

    /// 获取指定单元格的值
    pub fn get_cell(&self, row_index: usize, column_name: &str) -> Result<&CellValue, DataSetError> {
        let col_info = self.schema.get(column_name)
            .ok_or(DataSetError::ColumnNotFound)?;
        
        let row = self.get_row(row_index)?;
        row.values().get(col_info.index).ok_or(DataSetError::IndexOutOfBounds)
    }

    /// 设置指定单元格的值
    pub fn set_cell(&mut self, row_index: usize, column_name: &str, value: CellValue) -> Result<(), DataSetError> {
        let col_info = self.schema.get(column_name)
            .ok_or(DataSetError::ColumnNotFound)?;
        
        let col_info_index = col_info.index;
        let row = self.get_row_mut(row_index)?;
        if col_info_index >= row.values().len() {
            return Err(DataSetError::IndexOutOfBounds);
        }
        row.values_mut()[col_info_index] = value;
        Ok(())
    }

    /// 获取指定列的所有值
    pub fn get_column_values(&self, column_name: &str) -> Result<Vec<&CellValue>, DataSetError> {
        let col_info = self.schema.get(column_name)
            .ok_or(DataSetError::ColumnNotFound)?;

        self.rows.iter()
            .map(|row| row.values().get(col_info.index).ok_or(DataSetError::IndexOutOfBounds))
            .collect()
    }

    /// 为指定行添加子数据集
    pub fn add_child_dataset(&mut self, row_index: usize, name: String, dataset: RowDataSet) -> Result<(), DataSetError> {
        let row = self.get_row_mut(row_index)?;
        row.add_child(name, dataset);
        Ok(())
    }

    /// 获取指定行的子数据集
    pub fn get_child_dataset(&self, row_index: usize, name: &str) -> Result<Option<&RowDataSet>, DataSetError> {
        let row = self.get_row(row_index)?;
        Ok(row.get_child(name))
    }

    /// 移除指定行的子数据集
    pub fn remove_child_dataset(&mut self, row_index: usize, name: &str) -> Result<Option<RowDataSet>, DataSetError> {
        let row = self.get_row_mut(row_index)?;
        Ok(row.remove_child(name))
    }

    /// 清空所有数据
    pub fn clear(&mut self) {
        self.rows.clear();
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    fn create_test_dataset() -> RowDataSet {
        let mut dataset = RowDataSet::new("test".to_string());
        dataset.add_column("id".to_string(), ColumnType::I32).unwrap();
        dataset.add_column("name".to_string(), ColumnType::String).unwrap();
        dataset.add_column("active".to_string(), ColumnType::Bool).unwrap();
        dataset
    }

    #[test]
    fn test_child_datasets() {
        let mut parent_dataset = create_test_dataset();

        // 添加父数据集的行
        let parent_row = vec![
            CellValue::I32(1),
            CellValue::String("Parent".to_string()),
            CellValue::Bool(true),
        ];
        parent_dataset.add_row(parent_row).unwrap();

        // 创建子数据集
        let mut child_dataset = RowDataSet::new("child".to_string());
        child_dataset.add_column("item_id".to_string(), ColumnType::I32).unwrap();
        child_dataset.add_column("quantity".to_string(), ColumnType::I32).unwrap();

        // 添加子数据集的行
        let child_row = vec![
            CellValue::I32(1),
            CellValue::I32(5),
        ];
        child_dataset.add_row(child_row).unwrap();

        // 将子数据集添加到父数据集的行
        parent_dataset.add_child_dataset(0, "items".to_string(), child_dataset).unwrap();

        // 验证子数据集
        let child = parent_dataset.get_child_dataset(0, "items").unwrap().unwrap();
        assert_eq!(child.column_count(), 2);
        assert_eq!(child.row_count(), 1);
        
        // 验证子数据集的值
        let quantity = child.get_cell(0, "quantity").unwrap();
        assert!(matches!(quantity, CellValue::I32(5)));

        // 测试移除子数据集
        let removed_child = parent_dataset.remove_child_dataset(0, "items").unwrap().unwrap();
        assert_eq!(removed_child.dataset_id(), "child");
        assert!(parent_dataset.get_child_dataset(0, "items").unwrap().is_none());
    }

    #[test]
    fn test_multiple_child_datasets() {
        let mut parent_dataset = create_test_dataset();
        parent_dataset.add_row(vec![
            CellValue::I32(1),
            CellValue::String("Parent".to_string()),
            CellValue::Bool(true),
        ]).unwrap();

        // 添加多个子数据集
        let child1 = RowDataSet::new("child1".to_string());
        let child2 = RowDataSet::new("child2".to_string());

        parent_dataset.add_child_dataset(0, "child1".to_string(), child1).unwrap();
        parent_dataset.add_child_dataset(0, "child2".to_string(), child2).unwrap();

        // 验证多个子数据集
        assert!(parent_dataset.get_child_dataset(0, "child1").unwrap().is_some());
        assert!(parent_dataset.get_child_dataset(0, "child2").unwrap().is_some());

        // 移除一个子数据集
        parent_dataset.remove_child_dataset(0, "child1").unwrap();
        assert!(parent_dataset.get_child_dataset(0, "child1").unwrap().is_none());
        assert!(parent_dataset.get_child_dataset(0, "child2").unwrap().is_some());
    }
}
