use std::collections::HashMap;

use crate::data::cell::CellValue;
use crate::data::dataset::ColumnType;

// 修改 RowData 定义，增加子数据集支持
#[derive(Debug, Clone)]
pub struct RowData {
    values: HashMap<String, CellValue>,
    children: Option<HashMap<String, ColDataSet>>,
}

impl RowData {
    pub fn new() -> Self {
        Self {
            values: HashMap::new(),
            children: None,
        }
    }

    pub fn get(&self, key: &str) -> Option<&CellValue> {
        self.values.get(key)
    }

    pub fn insert(&mut self, key: String, value: CellValue) {
        self.values.insert(key, value);
    }

    pub fn get_child(&self, key: &str) -> Option<&ColDataSet> {
        self.children.as_ref().and_then(|children| children.get(key))
    }

    pub fn get_child_mut(&mut self, key: &str) -> Option<&mut ColDataSet> {
        self.children.as_mut().and_then(|children| children.get_mut(key))
    }

    pub fn add_child(&mut self, key: String, dataset: ColDataSet) {
        if self.children.is_none() {
            self.children = Some(HashMap::new());
        }
        self.children.as_mut().unwrap().insert(key, dataset);
    }

    /// 移除指定的子数据集并返回它，如果不存在则返回 None
    pub fn remove_child(&mut self, key: &str) -> Option<ColDataSet> {
        let result = self.children.as_mut().and_then(|children| children.remove(key));
        
        // 如果 HashMap 为空，将 children 设置为 None
        if let Some(children) = &self.children {
            if children.is_empty() {
                self.children = None;
            }
        }
        
        result
    }
}

impl Default for RowData {
    fn default() -> Self {
        Self::new()
    }
}

/// 列数据存储 - 针对 NULL 值优化
#[derive(Debug, Clone)]
pub enum ColumnDataArray {
    // 仅保留 Bool 和 String 类型的 Option 包装
    Bool(Vec<Option<bool>>),
    String(Vec<Option<String>>),
    
    // 数值类型直接使用基础类型，NULL 将被设置为 0
    U32(Vec<u32>),
    U8(Vec<u8>),
    U16(Vec<u16>),
    U64(Vec<u64>),
    I8(Vec<i8>),
    I16(Vec<i16>),
    I32(Vec<i32>),
    I64(Vec<i64>),
    F32(Vec<f32>),
    F64(Vec<f64>),
    // CHILD(Vec<Option<HashMap<String, ColDataSet>>>),
}

impl ColumnDataArray {
    /// 获取值是否为 NULL
    pub fn is_null(&self, index: usize) -> bool {
        match self {
            ColumnDataArray::Bool(vec) => vec[index].is_none(),
            ColumnDataArray::String(vec) => vec[index].is_none(),
            // 数值类型返回 false，因为 0 是有效值
            _ => false,
        }
    }

    /// 设置 NULL 值
    pub fn set_null(&mut self, index: usize) {
        match self {
            ColumnDataArray::Bool(vec) => vec[index] = None,
            ColumnDataArray::String(vec) => vec[index] = None,
            // 数值类型设置为 0
            ColumnDataArray::U32(vec) => vec[index] = 0,
            ColumnDataArray::U8(vec) => vec[index] = 0,
            ColumnDataArray::U16(vec) => vec[index] = 0,
            ColumnDataArray::U64(vec) => vec[index] = 0,
            ColumnDataArray::I8(vec) => vec[index] = 0,
            ColumnDataArray::I16(vec) => vec[index] = 0,
            ColumnDataArray::I32(vec) => vec[index] = 0,
            ColumnDataArray::I64(vec) => vec[index] = 0,
            ColumnDataArray::F32(vec) => vec[index] = 0.0,
            ColumnDataArray::F64(vec) => vec[index] = 0.0,
            // ColumnDataArray::CHILD(vec) => vec[index] = None,
        }
    }
}

/// 列式存储的数据集结构
#[derive(Debug,Clone)]
pub struct ColDataSet {
    dataset_id: String,
    // 列定义：列名 -> 列类型
    schema: HashMap<String, ColumnType>,
    // 列数据：列名 -> 列数据
    columns: HashMap<String, ColumnDataArray>,
    // 子数据集：列名 -> 子数据集
    children: Vec<Option<HashMap<String, ColDataSet>>>,
}

impl ColDataSet {
    pub fn new(dataset_id: String) -> Self {
        Self {
            dataset_id,
            schema: HashMap::new(),
            columns: HashMap::new(),
            children: Vec::new(),
        }
    }

    /// 获取行数
    pub fn row_count(&self) -> usize {
        // 从任意一列获取长度即可，因为所有列的长度都是相同的
        self.columns.values().next()
            .map(|col| match col {
                ColumnDataArray::Bool(vec) => vec.len(),
                ColumnDataArray::String(vec) => vec.len(),
                ColumnDataArray::I32(vec) => vec.len(),
                ColumnDataArray::I64(vec) => vec.len(),
                ColumnDataArray::F32(vec) => vec.len(),
                ColumnDataArray::F64(vec) => vec.len(),
                ColumnDataArray::U8(vec) => vec.len(),
                ColumnDataArray::U16(vec) => vec.len(),
                ColumnDataArray::U32(vec) => vec.len(),
                ColumnDataArray::U64(vec) => vec.len(),
                ColumnDataArray::I8(vec) => vec.len(),
                ColumnDataArray::I16(vec) => vec.len(),
                // ColumnDataArray::CHILD(vec) => vec.len(),
            })
            .unwrap_or(0) // 如果没有列，返回 0
    }

    /// 验证所有列的长度是否一致
    fn validate_row_counts(&self) -> Result<(), ColDataSetError> {
        if self.columns.is_empty() {
            return Ok(());
        }

        let expected_len = self.row_count();
        
        // Also validate children length
        if self.children.len() != expected_len {
            return Err(ColDataSetError::InconsistentLength {
                column: "children".to_string(),
                expected: expected_len,
                actual: self.children.len(),
            });
        }

        for (col_name, col_data) in &self.columns {
            let actual_len = match col_data {
                ColumnDataArray::Bool(vec) => vec.len(),
                ColumnDataArray::String(vec) => vec.len(),
                ColumnDataArray::I32(vec) => vec.len(),
                ColumnDataArray::I64(vec) => vec.len(),
                ColumnDataArray::F32(vec) => vec.len(),
                ColumnDataArray::F64(vec) => vec.len(),
                ColumnDataArray::U8(vec) => vec.len(),
                ColumnDataArray::U16(vec) => vec.len(),
                ColumnDataArray::U32(vec) => vec.len(),
                ColumnDataArray::U64(vec) => vec.len(),
                ColumnDataArray::I8(vec) => vec.len(),
                ColumnDataArray::I16(vec) => vec.len(),
                // ColumnDataArray::CHILD(vec) => vec.len(),
            };
            if actual_len != expected_len {
                return Err(ColDataSetError::InconsistentLength {
                    column: col_name.clone(),
                    expected: expected_len,
                    actual: actual_len,
                });
            }
        }
        Ok(())
    }

    /// 添加列定义
    pub fn add_column(&mut self, name: String, col_type: ColumnType) -> Result<(), ColDataSetError> {
        let current_row_count = self.row_count();
        
        // 根据类型创建对应的空向量，并填充到当前行数
        let column_data = match col_type {
            ColumnType::Bool => ColumnDataArray::Bool(vec![None; current_row_count]),
            ColumnType::String => ColumnDataArray::String(vec![None; current_row_count]),
            ColumnType::I8 => ColumnDataArray::I8(vec![0; current_row_count]),
            ColumnType::I16 => ColumnDataArray::I16(vec![0; current_row_count]),
            ColumnType::I32 => ColumnDataArray::I32(vec![0; current_row_count]),
            ColumnType::I64 => ColumnDataArray::I64(vec![0; current_row_count]),
            ColumnType::U8 => ColumnDataArray::U8(vec![0; current_row_count]),
            ColumnType::U16 => ColumnDataArray::U16(vec![0; current_row_count]),
            ColumnType::U32 => ColumnDataArray::U32(vec![0; current_row_count]),
            ColumnType::U64 => ColumnDataArray::U64(vec![0; current_row_count]),
            ColumnType::F32 => ColumnDataArray::F32(vec![0.0; current_row_count]),
            ColumnType::F64 => ColumnDataArray::F64(vec![0.0; current_row_count]),
        };
        
        self.schema.insert(name.clone(), col_type);
        self.columns.insert(name, column_data);
        
        self.validate_row_counts()
    }

    /// 添加一行数据
    pub fn append_row(&mut self, row: &RowData) -> Result<(), ColDataSetError> {
        self.validate_row_counts()?;
        
        // 执行添加操作
        for (col_name, col_data) in &mut self.columns {
            let value = row.get(col_name).cloned().unwrap_or(CellValue::Null);
            
            match (col_data, value) {
                // Bool 和 String 保持不变
                (ColumnDataArray::Bool(vec), CellValue::Bool(v)) => {
                    vec.push(Some(v));
                }
                (ColumnDataArray::Bool(vec), CellValue::Null) => {
                    vec.push(None);
                }
                (ColumnDataArray::String(vec), CellValue::String(v)) => {
                    vec.push(Some(v));
                }
                (ColumnDataArray::String(vec), CellValue::Null) => {
                    vec.push(None);
                }
                
                // 数值类型的 NULL 处理改为 0
                (ColumnDataArray::I32(vec), CellValue::I32(v)) => {
                    vec.push(v);
                }
                (ColumnDataArray::I32(vec), CellValue::Null) => {
                    vec.push(0);
                }
                (ColumnDataArray::F64(vec), CellValue::F64(v)) => {
                    vec.push(v);
                }
                (ColumnDataArray::F64(vec), CellValue::Null) => {
                    vec.push(0.0);
                }
                
                // ... 其他数值类型类似处理
                
                // 类型不匹配的情况
                (col_data, value) => {
                    return Err(ColDataSetError::TypeMismatch {
                        column: col_name.clone(),
                        expected: format!("{:?}", col_data),
                        actual: format!("{:?}", value),
                    });
                }
            }
        }
        
        // Add children data
        if let Some(row_children) = &row.children {
            self.children.push(Some(row_children.clone()));
        } else {
            self.children.push(None);
        }
        
        self.validate_row_counts()
    }

    /// 获取列数据
    pub fn get_column(&self, name: &str) -> Option<&ColumnDataArray> {
        self.columns.get(name)
    }

    /// 在指定位置插入一行数据
    pub fn insert_row(&mut self, index: usize, row: &RowData) -> Result<(), ColDataSetError> {
        self.validate_row_counts()?;
        
        if index > self.row_count() {
            return Err(ColDataSetError::IndexOutOfBounds {
                index: index,
                row_count: self.row_count(),
            });
        }

        // 为每一列插入数据
        for (col_name, col_data) in &mut self.columns {
            let value = row.values.get(col_name).cloned().unwrap_or(CellValue::Null);
            
            match (col_data, value) {
                // Bool 类型处理
                (ColumnDataArray::Bool(vec), CellValue::Bool(v)) => {
                    vec.insert(index, Some(v));
                }
                (ColumnDataArray::Bool(vec), CellValue::Null) => {
                    vec.insert(index, None);
                }
                
                // String 类型处理
                (ColumnDataArray::String(vec), CellValue::String(v)) => {
                    vec.insert(index, Some(v));
                }
                (ColumnDataArray::String(vec), CellValue::Null) => {
                    vec.insert(index, None);
                }
                
                // 数值类型处理
                (ColumnDataArray::I32(vec), CellValue::I32(v)) => {
                    vec.insert(index, v);
                }
                (ColumnDataArray::I32(vec), CellValue::Null) => {
                    vec.insert(index, 0);
                }
                (ColumnDataArray::F64(vec), CellValue::F64(v)) => {
                    vec.insert(index, v);
                }
                (ColumnDataArray::F64(vec), CellValue::Null) => {
                    vec.insert(index, 0.0);
                }
                // ... 其他数值类型类似处理
                
                // 类型不匹配的情况
                (col_data, value) => {
                    return Err(ColDataSetError::TypeMismatch {
                        column: col_name.clone(),
                        expected: format!("{:?}", col_data),
                        actual: format!("{:?}", value),
                    });
                }
            }
        }
        
        // Insert children data
        if let Some(row_children) = &row.children {
            self.children.insert(index, Some(row_children.clone()));
        } else {
            self.children.insert(index, None);
        }
        
        self.validate_row_counts()
    }

    /// 删除指定行并返回被删除的行数据
    pub fn remove_row(&mut self, index: usize) -> Result<RowData, ColDataSetError> {
        self.validate_row_counts()?;
        
        if index >= self.row_count() {
            return Err(ColDataSetError::IndexOutOfBounds {
                index: index,
                row_count: self.row_count(),
            });
        }

        let mut removed_row = RowData::new();

        // 从每一列中删除对应行的数据并收集被删除的值
        for (col_name, col_data) in &mut self.columns {
            let value = match col_data {
                ColumnDataArray::Bool(vec) => {
                    let v = vec.remove(index);
                    v.map_or(CellValue::Null, CellValue::Bool)
                }
                ColumnDataArray::String(vec) => {
                    let v = vec.remove(index);
                    v.map_or(CellValue::Null, CellValue::String)
                }
                ColumnDataArray::I32(vec) => {
                    let v = vec.remove(index);
                    if v == 0 {
                        CellValue::Null
                    } else {
                        CellValue::I32(v)
                    }
                }
                ColumnDataArray::I64(vec) => {
                    let v = vec.remove(index);
                    if v == 0 {
                        CellValue::Null
                    } else {
                        CellValue::I64(v)
                    }
                }
                ColumnDataArray::F32(vec) => {
                    let v = vec.remove(index);
                    if v == 0.0 {
                        CellValue::Null
                    } else {
                        CellValue::F32(v)
                    }
                }
                ColumnDataArray::F64(vec) => {
                    let v = vec.remove(index);
                    if v == 0.0 {
                        CellValue::Null
                    } else {
                        CellValue::F64(v)
                    }
                }
                ColumnDataArray::U8(vec) => {
                    let v = vec.remove(index);
                    if v == 0 {
                        CellValue::Null
                    } else {
                        CellValue::U8(v)
                    }
                }
                ColumnDataArray::U16(vec) => {
                    let v = vec.remove(index);
                    if v == 0 {
                        CellValue::Null
                    } else {
                        CellValue::U16(v)
                    }
                }
                ColumnDataArray::U32(vec) => {
                    let v = vec.remove(index);
                    if v == 0 {
                        CellValue::Null
                    } else {
                        CellValue::U32(v)
                    }
                }
                ColumnDataArray::U64(vec) => {
                    let v = vec.remove(index);
                    if v == 0 {
                        CellValue::Null
                    } else {
                        CellValue::U64(v)
                    }
                }
                ColumnDataArray::I8(vec) => {
                    let v = vec.remove(index);
                    if v == 0 {
                        CellValue::Null
                    } else {
                        CellValue::I8(v)
                    }
                }
                ColumnDataArray::I16(vec) => {
                    let v = vec.remove(index);
                    if v == 0 {
                        CellValue::Null
                    } else {
                        CellValue::I16(v)
                    }
                }
                // ColumnDataArray::CHILD(vec) => {
                //     let v = vec.remove(index);
                //     v.map_or(CellValue::Null, CellValue::Child)
                // }
            };
            removed_row.insert(col_name.clone(), value);
        }

        // Remove and set children data
        if let Some(children) = self.children.remove(index) {
            removed_row.children = Some(children);
        }

        self.validate_row_counts()?;
        Ok(removed_row)
    }

    /// 获取数据集ID
    pub fn dataset_id(&self) -> &str {
        &self.dataset_id
    }
}

#[derive(Debug)]
pub enum ColDataSetError {
    InconsistentLength {
        column: String,
        expected: usize,
        actual: usize,
    },
    IndexOutOfBounds {
        index: usize,
        row_count: usize,
    },
    TypeMismatch {
        column: String,
        expected: String,
        actual: String,
    },
}

impl std::fmt::Display for ColDataSetError {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            ColDataSetError::InconsistentLength { column, expected, actual } => {
                write!(f, "Column '{}' has inconsistent length: expected {}, got {}", 
                    column, expected, actual)
            }
            ColDataSetError::IndexOutOfBounds { index, row_count } => {
                write!(f, "Index {} out of bounds (row count: {})", index, row_count)
            }
            ColDataSetError::TypeMismatch { column, expected, actual } => {
                write!(f, "Type mismatch for column {}: expected {}, got {}", 
                    column, expected, actual)
            }
        }
    }
}

impl std::error::Error for ColDataSetError {}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_dataset() {
        let mut dataset = ColDataSet::new("test".into());

        // 添加列定义
        dataset.add_column("id".into(), ColumnType::I32).unwrap();
        dataset.add_column("name".into(), ColumnType::String).unwrap();
        dataset.add_column("score".into(), ColumnType::F64).unwrap();
        dataset.add_column("active".into(), ColumnType::Bool).unwrap();

        // 添加行数据
        let mut row1 = RowData::new();
        row1.insert("id".into(), CellValue::I32(1));
        row1.insert("name".into(), CellValue::String("Alice".into()));
        row1.insert("score".into(), CellValue::F64(95.5));
        row1.insert("active".into(), CellValue::Bool(true));
        dataset.append_row(&row1).unwrap();

        // 添加包含空值的行
        let mut row2 = RowData::new();
        row2.insert("id".into(), CellValue::I32(2));
        row2.insert("name".into(), CellValue::String("Bob".into()));
        row2.insert("score".into(), CellValue::Null);
        row2.insert("active".into(), CellValue::Bool(false));
        dataset.append_row(&row2).unwrap();

        // 验证数据
        match dataset.get_column("id").unwrap() {
            ColumnDataArray::I32(vec) => {
                assert_eq!(vec[0], 1);
                assert_eq!(vec[1], 2);
            }
            _ => panic!("Wrong type"),
        }

        match dataset.get_column("score").unwrap() {
            ColumnDataArray::F64(vec) => {
                assert_eq!(vec[0], 95.5);
                assert_eq!(vec[1], 0.0);
            }
            _ => panic!("Wrong type"),
        }
    }

    #[test]
    fn test_type_mismatch() {
        let mut dataset = ColDataSet::new("test".into());
        dataset.add_column("id".into(), ColumnType::I32).unwrap();

        let mut row = RowData::new();
        row.insert("id".into(), CellValue::String("not a number".into()));
        
        // 应该返回错误
        assert!(dataset.append_row(&row).is_err());
    }

    #[test]
    fn test_insert_and_remove_row() {
        let mut dataset = ColDataSet::new("test".into());

        // 添加列定义
        dataset.add_column("id".into(), ColumnType::I32).unwrap();
        dataset.add_column("name".into(), ColumnType::String).unwrap();

        // 添加初始行
        let mut row1 = RowData::new();
        row1.insert("id".into(), CellValue::I32(1));
        row1.insert("name".into(), CellValue::String("Alice".into()));
        dataset.append_row(&row1).unwrap();

        let mut row2 = RowData::new();
        row2.insert("id".into(), CellValue::I32(3));
        row2.insert("name".into(), CellValue::String("Charlie".into()));
        dataset.append_row(&row2).unwrap();

        // 在中间插入一行
        let mut row_insert = RowData::new();
        row_insert.insert("id".into(), CellValue::I32(2));
        row_insert.insert("name".into(), CellValue::String("Bob".into()));
        dataset.insert_row(1, &row_insert).unwrap();

        // 验证插入结果
        match dataset.get_column("id").unwrap() {
            ColumnDataArray::I32(vec) => {
                assert_eq!(vec[0], 1);
                assert_eq!(vec[1], 2);
                assert_eq!(vec[2], 3);
            }
            _ => panic!("Wrong type"),
        }

        // 删除中间的行
        dataset.remove_row(1).unwrap();

        // 验证删除结果
        match dataset.get_column("id").unwrap() {
            ColumnDataArray::I32(vec) => {
                assert_eq!(vec[0], 1);
                assert_eq!(vec[1], 3);
                assert_eq!(vec.len(), 2);
            }
            _ => panic!("Wrong type"),
        }

        // 验证行数
        assert_eq!(dataset.row_count(), 2);
    }

    #[test]
    fn test_invalid_operations() {
        let mut dataset = ColDataSet::new("test".into());
        dataset.add_column("id".into(), ColumnType::I32).unwrap();

        // 测试在无效位置插入
        let row = RowData::new();
        assert!(dataset.insert_row(1, &row).is_err());

        // 测试删除无效行
        assert!(dataset.remove_row(0).is_err());
    }

    #[test]
    fn test_remove_row_return_value() {
        let mut dataset = ColDataSet::new("test".into());

        // 添加列定义
        dataset.add_column("id".into(), ColumnType::I32).unwrap();
        dataset.add_column("name".into(), ColumnType::String).unwrap();
        dataset.add_column("score".into(), ColumnType::F64).unwrap();

        // 添加测试数据
        let mut row1 = RowData::new();
        row1.insert("id".into(), CellValue::I32(1));
        row1.insert("name".into(), CellValue::String("Alice".into()));
        row1.insert("score".into(), CellValue::F64(95.5));
        dataset.append_row(&row1).unwrap();

        let mut row2 = RowData::new();
        row2.insert("id".into(), CellValue::I32(2));
        row2.insert("name".into(), CellValue::String("Bob".into()));
        row2.insert("score".into(), CellValue::Null);  // NULL value
        dataset.append_row(&row2).unwrap();

        // 删除
        let removed_row = dataset.remove_row(1).unwrap();

        // 验证删除结果
        match removed_row.get("id").unwrap() {
            CellValue::I32(v) => assert_eq!(*v, 2),
            _ => panic!("Wrong type"),
        }

        match removed_row.get("name").unwrap() {
            CellValue::String(v) => assert_eq!(v, "Bob"),
            _ => panic!("Wrong type"),
        }

        match removed_row.get("score").unwrap() {
            CellValue::Null => (),
            _ => panic!("Wrong type"),
        }
    }

    #[test]
    fn test_row_count() {
        let mut dataset = ColDataSet::new("test".into());
        assert_eq!(dataset.row_count(), 0);

        // 添加列定义
        dataset.add_column("id".into(), ColumnType::I32).unwrap();
        dataset.add_column("name".into(), ColumnType::String).unwrap();

        // 添加行数据
        let mut row1 = RowData::new();
        row1.insert("id".into(), CellValue::I32(1));
        row1.insert("name".into(), CellValue::String("Alice".into()));
        dataset.append_row(&row1).unwrap();
        assert_eq!(dataset.row_count(), 1);

        // 添加第二行
        let mut row2 = RowData::new();
        row2.insert("id".into(), CellValue::I32(2));
        row2.insert("name".into(), CellValue::String("Bob".into()));
        dataset.append_row(&row2).unwrap();
        assert_eq!(dataset.row_count(), 2);

        // 删除一行
        dataset.remove_row(0).unwrap();
        assert_eq!(dataset.row_count(), 1);
    }

    #[test]
    fn test_column_consistency() {
        let mut dataset = ColDataSet::new("test".into());

        // 添加初始列
        dataset.add_column("id".into(), ColumnType::I32).unwrap();
        
        // 添加一行数据
        let mut row = RowData::new();
        row.insert("id".into(), CellValue::I32(1));
        dataset.append_row(&row).unwrap();

        // 添加新列，应该自动填充现有行数的空值
        dataset.add_column("name".into(), ColumnType::String).unwrap();

        // 验证所有列长度一致
        assert_eq!(dataset.row_count(), 1);
        
        match dataset.get_column("name").unwrap() {
            ColumnDataArray::String(vec) => {
                assert_eq!(vec.len(), 1);
                assert_eq!(vec[0], None);
            }
            _ => panic!("Wrong type"),
        }
    }

    #[test]
    fn test_invalid_operations2() {
        let mut dataset = ColDataSet::new("test".into());
        dataset.add_column("id".into(), ColumnType::I32).unwrap();
        dataset.add_column("name".into(), ColumnType::String).unwrap();

        // 模拟长度不一致的情况
        if let ColumnDataArray::I32(vec) = dataset.columns.get_mut("id").unwrap() {
            vec.push(1); // 人为制造不一致
        }

        // 所有操作都应该失败
        let row = RowData::new();
        assert!(dataset.append_row(&row).is_err());
        assert!(dataset.insert_row(0, &row).is_err());
        assert!(dataset.remove_row(0).is_err());
        assert!(dataset.add_column("new_col".into(), ColumnType::Bool).is_err());
    }

    #[test]
    fn test_nested_dataset() {
        let mut parent_dataset = ColDataSet::new("parent".into());
        parent_dataset.add_column("id".into(), ColumnType::I32).unwrap();
        parent_dataset.add_column("name".into(), ColumnType::String).unwrap();

        // 创建子数据集
        let mut child_dataset = ColDataSet::new("child".into());
        child_dataset.add_column("item_id".into(), ColumnType::I32).unwrap();
        child_dataset.add_column("quantity".into(), ColumnType::I32).unwrap();

        // 添加子数据集的数据
        let mut child_row = RowData::new();
        child_row.insert("item_id".into(), CellValue::I32(1));
        child_row.insert("quantity".into(), CellValue::I32(5));
        child_dataset.append_row(&child_row).unwrap();

        // 创建父数据集的行，并包含子数据集
        let mut parent_row = RowData::new();
        parent_row.insert("id".into(), CellValue::I32(1));
        parent_row.insert("name".into(), CellValue::String("Parent 1".into()));
        parent_row.add_child("items".into(), child_dataset);

        // 添加到父数据集
        parent_dataset.append_row(&parent_row).unwrap();

        // 验证数据
        assert_eq!(parent_dataset.row_count(), 1);
        
        // 获取并验证子数据集
        if let Some(removed_row) = parent_dataset.remove_row(0).ok() {
            if let Some(child_ds) = removed_row.get_child("items") {
                assert_eq!(child_ds.row_count(), 1);
                if let Some(ColumnDataArray::I32(quantities)) = child_ds.get_column("quantity") {
                    assert_eq!(quantities[0], 5);
                } else {
                    panic!("Wrong type for quantity column");
                }
            } else {
                panic!("Child dataset not found");
            }
        } else {
            panic!("Failed to remove row");
        }
    }

    #[test]
    fn test_dataset_id() {
        let dataset = ColDataSet::new("test_dataset".into());
        assert_eq!(dataset.dataset_id(), "test_dataset");
    }

    #[test]
    fn test_add_child() {
        let mut row = RowData::new();
        let child_dataset = ColDataSet::new("child".into());
        
        // 初始状态应该是 None
        assert!(row.get_child("child1").is_none());
        
        // 添加第一个子数据集
        row.add_child("child1".into(), child_dataset.clone());
        assert!(row.get_child("child1").is_some());
        
        // 添加第二个子数据集
        let child_dataset2 = ColDataSet::new("child2".into());
        row.add_child("child2".into(), child_dataset2);
        assert!(row.get_child("child2").is_some());
        
        // 验证数据集ID
        assert_eq!(row.get_child("child1").unwrap().dataset_id(), "child");
    }

    #[test]
    fn test_child_dataset_operations() {
        let mut row = RowData::new();
        let child_dataset = ColDataSet::new("child".into());
        
        // 初始状态应该是 None
        assert!(row.get_child("child1").is_none());
        
        // 添加子数据集
        row.add_child("child1".into(), child_dataset.clone());
        assert!(row.get_child("child1").is_some());
        
        // 移除子数据集
        let removed = row.remove_child("child1");
        assert!(removed.is_some());
        assert_eq!(removed.unwrap().dataset_id(), "child");
        
        // 验证移除后状态
        assert!(row.get_child("child1").is_none());
        // children 应该被设置为 None，因为 HashMap 为空
        assert!(matches!(row.children, None));
        
        // 测试移除不存在的子数据集
        assert!(row.remove_child("nonexistent").is_none());
    }

    #[test]
    fn test_multiple_children() {
        let mut row = RowData::new();
        
        // 添加多个子数据集
        row.add_child("child1".into(), ColDataSet::new("child1".into()));
        row.add_child("child2".into(), ColDataSet::new("child2".into()));
        
        // 移除一个子数据集，children 应该仍然是 Some
        row.remove_child("child1");
        assert!(matches!(row.children, Some(_)));
        
        // 移除最后一个子数据集，children 应该变为 None
        row.remove_child("child2");
        assert!(matches!(row.children, None));
    }
}
