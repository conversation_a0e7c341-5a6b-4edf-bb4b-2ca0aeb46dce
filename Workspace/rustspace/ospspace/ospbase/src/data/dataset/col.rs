use std::collections::HashMap;

use crate::{data::cell::CellValue, meta::fields::SYS_OBJCOLS};
use serde::{Deserialize, Serialize};
// use thiserror::Error;



#[derive(Debug, Serialize, Deserialize,<PERSON><PERSON>,Default)]
pub struct ColumnDef {
    data: HashMap<SYS_OBJCOLS, CellValue>,
}
impl ColumnDef {
    pub fn get(&self, field: &SYS_OBJCOLS) -> Option<&CellValue> {
        self.data.get(field)
    }
    pub fn set(&mut self, field: SYS_OBJCOLS, value: CellValue) {
        self.data.insert(field, value);
    }
    pub fn col_id(&self) -> String {
        self.get(&SYS_OBJCOLS::COL_ID)
            .map(|v| v.to_string().unwrap())
            .unwrap_or_default()
    }
    pub fn col_isfkey(&self) -> Option<bool> {
        self.get(&SYS_OBJCOLS::COL_ISFKEY)
            .map(|v| v.to_boolean().unwrap())
    }
}


// #[derive(Debug)]
// pub struct ColumnValue {
//     values: Vec<CellValue>,
// }

// impl ColumnValue {
//     pub fn new() -> Self {
//         ColumnValue {
//             values: Vec::new(),
//         }
//     }

//     pub fn with_capacity(capacity: usize) -> Self {
//         ColumnValue {
//             values: Vec::with_capacity(capacity),
//         }
//     }

//     pub fn add_value(&mut self, value: CellValue) {
//         self.values.push(value);
//     }

//     pub fn get_value(&self, index: usize) -> Result<&CellValue, ColumnError> {
//         self.values.get(index)
//             .ok_or(ColumnError::IndexOutOfBounds)
//     }

//     pub fn set_value(&mut self, index: usize, value: CellValue) -> Result<(), ColumnError> {
//         if index < self.values.len() {
//             self.values[index] = value;
//             Ok(())
//         } else {
//             Err(ColumnError::IndexOutOfBounds)
//         }
//     }

//     pub fn len(&self) -> usize {
//         self.values.len()
//     }

//     pub fn is_empty(&self) -> bool {
//         self.values.is_empty()
//     }

//     pub fn clear(&mut self) {
//         self.values.clear();
//     }
// }

// #[derive(Error, Debug)]
// pub enum ColumnError {
//     #[error("Values not initialized")]
//     ValuesNotInitialized,
//     #[error("Columns not initialized")]
//     ColumnsNotInitialized,
//     #[error("Index out of bounds")]
//     IndexOutOfBounds,
// }
