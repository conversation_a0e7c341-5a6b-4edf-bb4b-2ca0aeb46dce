// use std::sync::Arc;

// use std::collections::HashMap;

// use chrono::NaiveDateTime;
// use rust_decimal::Decimal;
// use chrono::{NaiveDate, NaiveDateTime, NaiveTime};
use serde::{Deserialize, Serialize};

// pub type ColValue = serde_json::Value;

#[derive(Debug, Clone, Serialize, Deserialize,PartialEq)]
pub enum CellValue {
    Bool(bool),
    I8(i8),
    I16(i16),
    I32(i32),
    I64(i64),
    U8(u8),
    U16(u16),
    U32(u32),
    U64(u64),
    F32(f32),
    F64(f64),
    Char(char),
    String(String),
    OptString(Option<String>),
    Null,
}

#[allow(dead_code)]
pub struct CellFormat {
    font_name: String,
    font_size: f32,
    font_color: String, // 可以使用十六进制颜色代码
    background_color: String, // 可以使用十六进制颜色代码
    bold: bool,
    italic: bool,
    underline: bool,
    horizontal_alignment: HorizontalAlignment,
    vertical_alignment: VerticalAlignment,
}
#[allow(dead_code)]
enum HorizontalAlignment {
    Left,
    Center,
    Right,
}
#[allow(dead_code)]
enum VerticalAlignment {
    Top,
    Middle,
    Bottom,
}

impl CellValue {
    /// 将 CellValue 转换为字符串表示
    pub fn to_string(&self) -> Option<String> {
        match self {
            CellValue::Bool(b) => Some(b.to_string()),
            CellValue::I8(n) => Some(n.to_string()),
            CellValue::I16(n) => Some(n.to_string()),
            CellValue::I32(n) => Some(n.to_string()),
            CellValue::I64(n) => Some(n.to_string()),
            CellValue::U8(n) => Some(n.to_string()),
            CellValue::U16(n) => Some(n.to_string()),
            CellValue::U32(n) => Some(n.to_string()),
            CellValue::U64(n) => Some(n.to_string()),
            CellValue::F32(n) => Some(n.to_string()),
            CellValue::F64(n) => Some(n.to_string()),
            CellValue::Char(c) => Some(c.to_string()),
            CellValue::String(s) => Some(s.clone()),
            CellValue::OptString(opt) => opt.clone(),
            CellValue::Null => None,
        }
    }

    /// 将 CellValue 转换为布尔值
    /// - 数值类型：非 0 为 true
    /// - 字符串类型：非空为 true
    /// - 字符类型：'1' 为 true，其他为 false
    /// - Null：返回 None
    pub fn to_boolean(&self) -> Option<bool> {
        match self {
            CellValue::Bool(b) => Some(*b),
            CellValue::I8(n) => Some(*n != 0),
            CellValue::I16(n) => Some(*n != 0),
            CellValue::I32(n) => Some(*n != 0),
            CellValue::I64(n) => Some(*n != 0),
            CellValue::U8(n) => Some(*n != 0),
            CellValue::U16(n) => Some(*n != 0),
            CellValue::U32(n) => Some(*n != 0),
            CellValue::U64(n) => Some(*n != 0),
            CellValue::F32(n) => Some(*n != 0.0),
            CellValue::F64(n) => Some(*n != 0.0),
            CellValue::Char(c) => Some(*c == '1'),
            CellValue::String(s) => Some(!s.is_empty()),
            CellValue::OptString(opt) => opt.as_ref().map(|s| !s.is_empty()),
            CellValue::Null => None,
        }
    }

    /// 将 CellValue 转换为整数
    /// - 数值类型：直接转换
    /// - 布尔值：true -> 1, false -> 0
    /// - 字符串：尝试解析为数字
    /// - 字符：尝试解析为数字
    /// - Null：返回 None
    pub fn to_integer(&self) -> Option<i64> {
        match self {
            // 布尔值转换
            CellValue::Bool(b) => Some(if *b { 1 } else { 0 }),
            
            // 整数类型转换
            CellValue::I8(n) => Some(*n as i64),
            CellValue::I16(n) => Some(*n as i64),
            CellValue::I32(n) => Some(*n as i64),
            CellValue::I64(n) => Some(*n),
            CellValue::U8(n) => Some(*n as i64),
            CellValue::U16(n) => Some(*n as i64),
            CellValue::U32(n) => Some(*n as i64),
            CellValue::U64(n) => {
                if *n <= i64::MAX as u64 {
                    Some(*n as i64)
                } else {
                    None
                }
            },
            
            // 浮点数转换（仅当可以精确表示为整数时）
            CellValue::F32(n) => {
                if n.fract() == 0.0 && *n >= i64::MIN as f32 && *n <= i64::MAX as f32 {
                    Some(*n as i64)
                } else {
                    None
                }
            },
            CellValue::F64(n) => {
                if n.fract() == 0.0 && *n >= i64::MIN as f64 && *n <= i64::MAX as f64 {
                    Some(*n as i64)
                } else {
                    None
                }
            },
            
            // 字符转换（仅数字字符）
            CellValue::Char(c) => {
                if c.is_ascii_digit() {
                    c.to_digit(10).map(|n| n as i64)
                } else {
                    None
                }
            },
            
            // 字符串转换
            CellValue::String(s) => s.parse::<i64>().ok(),
            CellValue::OptString(opt) => opt.as_ref().and_then(|s| s.parse::<i64>().ok()),
            
            // Null 值
            CellValue::Null => None,
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_cell_value_to_string() {
        // 测试布尔值
        assert_eq!(CellValue::Bool(true).to_string(), Some("true".to_string()));
        assert_eq!(CellValue::Bool(false).to_string(), Some("false".to_string()));

        // 测试数值类型
        assert_eq!(CellValue::I32(42).to_string(), Some("42".to_string()));
        assert_eq!(CellValue::F64(3.14).to_string(), Some("3.14".to_string()));
        assert_eq!(CellValue::U8(255).to_string(), Some("255".to_string()));

        // 测试字符和字符串
        assert_eq!(CellValue::Char('A').to_string(), Some("A".to_string()));
        assert_eq!(CellValue::String("Hello".into()).to_string(), Some("Hello".to_string()));
        
        // 测试可选字符串
        assert_eq!(CellValue::OptString(Some("World".into())).to_string(), Some("World".to_string()));
        assert_eq!(CellValue::OptString(None).to_string(), None);

        // 测试 Null
        assert_eq!(CellValue::Null.to_string(), None);
    }

    #[test]
    fn test_cell_value_to_boolean() {
        // 测试布尔值
        assert_eq!(CellValue::Bool(true).to_boolean(), Some(true));
        assert_eq!(CellValue::Bool(false).to_boolean(), Some(false));

        // 测试数值类型
        assert_eq!(CellValue::I32(1).to_boolean(), Some(true));
        assert_eq!(CellValue::I32(42).to_boolean(), Some(true));
        assert_eq!(CellValue::I32(0).to_boolean(), Some(false));
        assert_eq!(CellValue::I32(-1).to_boolean(), Some(true));
        
        assert_eq!(CellValue::F64(1.0).to_boolean(), Some(true));
        assert_eq!(CellValue::F64(0.0).to_boolean(), Some(false));
        assert_eq!(CellValue::F64(-1.0).to_boolean(), Some(true));

        assert_eq!(CellValue::U8(1).to_boolean(), Some(true));
        assert_eq!(CellValue::U8(0).to_boolean(), Some(false));
        assert_eq!(CellValue::U8(255).to_boolean(), Some(true));

        // 测试字符
        assert_eq!(CellValue::Char('1').to_boolean(), Some(true));
        assert_eq!(CellValue::Char('0').to_boolean(), Some(false));
        assert_eq!(CellValue::Char('a').to_boolean(), Some(false));

        // 测试字符串
        assert_eq!(CellValue::String("true".into()).to_boolean(), Some(true));
        assert_eq!(CellValue::String("false".into()).to_boolean(), Some(true));
        assert_eq!(CellValue::String("".into()).to_boolean(), Some(false));
        assert_eq!(CellValue::String("0".into()).to_boolean(), Some(true));
        assert_eq!(CellValue::String("1".into()).to_boolean(), Some(true));
        
        // 测试可选字符串
        assert_eq!(CellValue::OptString(Some("hello".into())).to_boolean(), Some(true));
        assert_eq!(CellValue::OptString(Some("".into())).to_boolean(), Some(false));
        assert_eq!(CellValue::OptString(None).to_boolean(), None);

        // 测试 Null
        assert_eq!(CellValue::Null.to_boolean(), None);
    }

    #[test]
    fn test_cell_value_to_integer() {
        // 测试布尔值转换
        assert_eq!(CellValue::Bool(true).to_integer(), Some(1));
        assert_eq!(CellValue::Bool(false).to_integer(), Some(0));

        // 测试整数类型转换
        assert_eq!(CellValue::I8(-128).to_integer(), Some(-128));
        assert_eq!(CellValue::I16(-32768).to_integer(), Some(-32768));
        assert_eq!(CellValue::I32(-2147483648).to_integer(), Some(-2147483648));
        assert_eq!(CellValue::I64(-9223372036854775808).to_integer(), Some(-9223372036854775808));
        
        assert_eq!(CellValue::U8(255).to_integer(), Some(255));
        assert_eq!(CellValue::U16(65535).to_integer(), Some(65535));
        assert_eq!(CellValue::U32(4294967295).to_integer(), Some(4294967295));
        // U64 超出 i64 范围的情况
        assert_eq!(CellValue::U64(u64::MAX).to_integer(), None);

        // 测试浮点数转换
        assert_eq!(CellValue::F32(42.0).to_integer(), Some(42));
        assert_eq!(CellValue::F32(42.5).to_integer(), None);  // 有小数部分
        assert_eq!(CellValue::F64(42.0).to_integer(), Some(42));
        assert_eq!(CellValue::F64(42.5).to_integer(), None);  // 有小数部分

        // 测试字符转换
        assert_eq!(CellValue::Char('0').to_integer(), Some(0));
        assert_eq!(CellValue::Char('9').to_integer(), Some(9));
        assert_eq!(CellValue::Char('a').to_integer(), None);  // 非数字字符

        // 测试字符串转换
        assert_eq!(CellValue::String("42".into()).to_integer(), Some(42));
        assert_eq!(CellValue::String("-42".into()).to_integer(), Some(-42));
        assert_eq!(CellValue::String("abc".into()).to_integer(), None);  // 非数字字符串
        assert_eq!(CellValue::String("42.5".into()).to_integer(), None); // 小数
        
        // 测试可选字符串
        assert_eq!(CellValue::OptString(Some("42".into())).to_integer(), Some(42));
        assert_eq!(CellValue::OptString(Some("abc".into())).to_integer(), None);
        assert_eq!(CellValue::OptString(None).to_integer(), None);

        // 测试 Null
        assert_eq!(CellValue::Null.to_integer(), None);
    }
}
