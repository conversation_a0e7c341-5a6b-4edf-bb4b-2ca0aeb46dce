/*!
 * console-ban v2.1.0
 * (c) 2020-2021 fz6m
 * Released under the MIT License.
 */ !function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports):"function"==typeof define&&define.amd?define(["exports"],t):t((e="undefined"!=typeof globalThis?globalThis:e||self).ConsoleBan={})}(this,(function(e){"use strict";var t=function(){return(t=Object.assign||function(e){for(var t,i=1,r=arguments.length;i<r;i++)for(var n in t=arguments[i])Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n]);return e}).apply(this,arguments)},i={clear:!0,debug:!0,debugTime:3e3};var r=function(){function e(e){var r=t(t({},i),e),n=r.clear,o=r.debug,c=r.debugTime,s=r.callback,a=r.redirect,l=r.write;this._debug=o,this._debugTime=c,this._clear=n,this._callback=s,this._redirect=a,this._write=l,this._evalCounts=0,this._isOpenedEver=!1}return e.prototype.clear=function(){this._clear&&(console.clear=function(){})},e.prototype.debug=function(){if(this._debug){var e=new Function("debugger");setInterval(e,this._debugTime)}},e.prototype.redirect=function(){if(this._redirect)if(~this._redirect.indexOf("http"))location.href!==this._redirect&&(location.href=this._redirect);else{var e,t=location.pathname+location.search;if(((e=this._redirect)?"/"!==e[0]?"/"+e:e:"/")!==t)location.href=this._redirect}},e.prototype.callback=function(){var e=this;if((this._callback||this._redirect||this._write)&&window){var t="[WARNING] fire in the hole";if(window.chrome){var i=new Function;i.toString=function(){return e._evalCounts++,e._evalCounts===(e._isOpenedEver?1:2)&&(e._isOpenedEver=!0,e._evalCounts=0,e.fire()),t},console.log&&console.log("%c",i)}if(~navigator.userAgent.toLowerCase().indexOf("firefox")){var r=/./;r.toString=function(){return e.fire(),t},console.log&&console.log(r)}}},e.prototype.write=function(){this._write&&(document.body.innerHTML="string"==typeof this._write?this._write:this._write.innerHTML)},e.prototype.fire=function(){this._callback?this._callback.call(null):(this.redirect(),this._redirect||this.write())},e.prototype.ban=function(){this.callback(),this.clear(),this.debug()},e}();function n(e){new r(e).ban()}e.default=n,e.init=n,Object.defineProperty(e,"__esModule",{value:!0})}));
var div = document.createElement('div')
div.innerHTML = ''

ConsoleBan.init({
    write: '',
    write: div
})
