<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>JSON-RPC Demo</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container">
        <h1>JSON-RPC Demo</h1>
        <div class="row">
            <div class="col-md-4">
                <label for="inputA" class="form-label">Value A:</label>
                <input type="number" class="form-control" id="inputA">
            </div>
            <div class="col-md-4">
                <label for="inputB" class="form-label">Value B:</label>
                <input type="number" class="form-control" id="inputB">
            </div>
            <div class="col-md-4">
                <button class="btn btn-primary" onclick="calculateSum()">Calculate Sum</button>
            </div>
        </div>
        <div class="row mt-3">
            <div class="col">
                <h3>Result:</h3>
                <p id="result"></p>
            </div>
        </div>
    </div>
    <script>
        async function calculateSum() {
            const a = document.getElementById('inputA').value;
            const b = document.getElementById('inputB').value;

            const payload = {
                jsonrpc: '2.0',
                method: 'add',
                params: {
                    a: parseInt(a),
                    b: parseInt(b)
                },
                jwt_token: 'caslfounder',
                id: 1
            };

            const response = await fetch('/jsonrpc', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(payload)
            });

            const jsonResponse = await response.json();
            if (jsonResponse.result) {
                document.getElementById('result').innerText = jsonResponse.result;
            } else {
                document.getElementById('result').innerText = 'Error: ' + jsonResponse.error.message;
            }
        }
    </script>
</body>
</html>
