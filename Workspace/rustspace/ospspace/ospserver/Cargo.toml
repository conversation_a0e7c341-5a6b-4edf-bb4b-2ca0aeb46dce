[package]
name = "ospserver"
version = "0.0.11"
edition = "2021"
[[bin]]
name = "ospserver"
path = "src/bin/ospserver.rs"
# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

# [target.x86_64-unknown-linux-musl]
# linker = "x86_64-linux-musl-gcc"

[dependencies]
rustc_version = "0.4.1"
# s2n-quic = { version = "1.32.0", features = ["provider-event-tracing"] }
tokio = { version = "1.41.1", features = ["full"] }
ospwebservice = {version = "0.1.0", path = "../ospwebservice" }
osptools = {version = "0.1.0", path = "../osptools" }
# btcmnetwork = {version = "0.1.0", path = "../btcmnetwork" }
# btcmweb = {version = "0.1.0", path = "../btcmweb" }
# btcmtools = {version = "0.1.0", path = "../btcmtools" }
slog = "2.7.0"
# once_cell = "1.19.0"
structopt = "0.3.26"
# lazy_static = "1.4.0"
ctrlc = {version = "3.4.5",features = ["termination"]}
tracing = "0.1"
tracing-subscriber = { version = "0.3", features = ["env-filter"] }
colored = "2.0"


[profile.release]
opt-level = "z"  # "z" 表示进行最大程度的优化
debug = false     # 禁用调试信息
rpath = false     # 在Linux和Unix系统上禁用RPATH，可以减小可执行文件的大小

[build]
# 指定 build.rs 为构建脚本
build = "build.rs"
