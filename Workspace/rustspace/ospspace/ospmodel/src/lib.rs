use rhai::{Dynamic, Engine, EvalAltResult, Position, Scope, AST};

/// 脚本执行器结构体
pub struct ScriptExecutor {
    engine: Engine,
    scope: Scope<'static>,
    ast: Option<AST>,
}

impl ScriptExecutor {
    /// 创建新的脚本执行器实例
    pub fn new() -> Self {
        let engine = Engine::new();
        
        // 启用调试功能
        // #[cfg(feature = "debugging")]
        // engine.on_debug(|context, source| {
        //     println!("Debug at line {}: {}", 
        //         context.position().line().unwrap_or(0),
        //         source
        //     );
        //     true  // 继续执行
        // });

        Self {
            engine,
            scope: Scope::new(),
            ast: None,
        }
    }
    /// 编译脚本
    pub fn compile(&mut self, script: &str) -> Result<(), Box<EvalAltResult>> {
        self.ast = Some(self.engine.compile(script)?);
        Ok(())
    }

    /// 注册函数到执行环境
    pub fn register_fn<F>(&mut self, name: &str, f: F)
    where
        F: 'static + Fn() -> Dynamic + Clone,
    {
        self.engine.register_fn(name, f);
    }

    /// 设置变量到执行环境
    pub fn set_var<T: Clone + Into<Dynamic> + 'static>(&mut self, name: &str, value: T) {
        self.scope.push(name, value);
    }

    /// 获取变量值
    pub fn get_var(&self, name: &str) -> Option<Dynamic> {
        self.scope.get_value(name)
    }
    /// 执行已编译的脚本
    pub fn run(&mut self) -> Result<Dynamic, Box<EvalAltResult>> {
        if let Some(ast) = self.ast.clone() {
            Ok(self.engine.run_ast_with_scope(&mut self.scope, &ast)?.into())
        } else {
            Err(EvalAltResult::ErrorRuntime("No script compiled".into(), Position::NONE).into())
        }
    }
    /// 直接执行脚本字符串
    pub fn eval(&mut self, script: &str) -> Result<Dynamic, Box<EvalAltResult>> {
        self.engine.eval_with_scope(&mut self.scope, script)
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_script_executor() {
        let mut executor = ScriptExecutor::new();
        
        // 编译并执行脚本
        executor.compile(r#"
            let x = 40;
            let y = 2;
            x + y
        "#).unwrap();
        
        let result = executor.run().unwrap();
        assert_eq!(result.as_int().unwrap(), 42);

        // 测试变量设置和获取
        executor.set_var("test_var", 100);
        let var = executor.get_var("test_var").unwrap();
        assert_eq!(var.as_int().unwrap(), 100);

        // 测试直接执行
        let result = executor.eval("test_var + 50").unwrap();
        assert_eq!(result.as_int().unwrap(), 150);
    }
}
