use flatbuffers::{<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, WIP<PERSON><PERSON><PERSON>, Vector};
use crate::data::dataset::rds::{RowDataSet, RowData, ColumnInfo, CellValue};
use crate::generated::ospbase::fb::{self, RowDataSet as FbRowDataSet};

impl RowDataSet {
    /// Serialize the RowDataSet to FlatBuffers format
    pub fn serialize(&self) -> Vec<u8> {
        let mut builder = FlatBufferBuilder::new();
        
        // Serialize schema
        let schema_vec: Vec<_> = self.schema.iter().map(|(name, info)| {
            let name = builder.create_string(name);
            fb::ColumnInfo::create(&mut builder, &fb::ColumnInfoArgs {
                name: Some(name),
                column_type: convert_column_type_to_fb(info.column_type),
            })
        }).collect();
        let schema = builder.create_vector(&schema_vec);

        // Serialize rows
        let rows_vec: Vec<_> = self.rows.iter().map(|row| {
            self.serialize_row(&mut builder, row)
        }).collect();
        let rows = builder.create_vector(&rows_vec);

        // Create dataset
        let dataset_id = builder.create_string(&self.dataset_id);
        let dataset = fb::RowDataSet::create(&mut builder, &fb::RowDataSetArgs {
            dataset_id: Some(dataset_id),
            schema: Some(schema),
            rows: Some(rows),
        });

        builder.finish(dataset, None);
        builder.finished_data().to_vec()
    }

    fn serialize_row<'a>(&self, builder: &mut FlatBufferBuilder<'a>, row: &RowData) -> WIPOffset<fb::RowData<'a>> {
        // Serialize values
        let values_vec: Vec<_> = row.values().iter().map(|value| {
            self.serialize_cell_value(builder, value)
        }).collect();
        let values = builder.create_vector(&values_vec);

        // Serialize children if any
        let children = if let Some(children) = &row.children {
            let children_vec: Vec<_> = children.iter().map(|(name, dataset)| {
                let name = builder.create_string(name);
                let serialized_dataset = dataset.serialize_nested(builder);
                fb::ChildDataSet::create(builder, &fb::ChildDataSetArgs {
                    name: Some(name),
                    dataset: Some(serialized_dataset),
                })
            }).collect();
            Some(builder.create_vector(&children_vec))
        } else {
            None
        };

        fb::RowData::create(builder, &fb::RowDataArgs {
            values: Some(values),
            children,
        })
    }

    fn serialize_nested<'a>(&self, builder: &mut FlatBufferBuilder<'a>) -> WIPOffset<fb::RowDataSet<'a>> {
        // Similar to serialize() but works with an existing builder
        // Implementation here...
    }

    fn serialize_cell_value<'a>(&self, builder: &mut FlatBufferBuilder<'a>, value: &CellValue) -> WIPOffset<fb::CellValue<'a>> {
        // Implementation for serializing individual cell values
        // Implementation here...
    }

    /// Deserialize from FlatBuffers format
    pub fn deserialize(bytes: &[u8]) -> Result<Self, Box<dyn std::error::Error>> {
        let fb_dataset = flatbuffers::root::<fb::RowDataSet>(bytes)?;
        // Implementation for deserialization
        // Implementation here...
        todo!()
    }
}

// Helper functions for type conversion
fn convert_column_type_to_fb(ct: ColumnType) -> fb::ColumnType {
    match ct {
        ColumnType::I32 => fb::ColumnType::I32,
        ColumnType::I64 => fb::ColumnType::I64,
        ColumnType::F32 => fb::ColumnType::F32,
        ColumnType::F64 => fb::ColumnType::F64,
        ColumnType::String => fb::ColumnType::String,
        ColumnType::Bool => fb::ColumnType::Bool,
        ColumnType::DateTime => fb::ColumnType::DateTime,
        ColumnType::Binary => fb::ColumnType::Binary,
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_serialization() {
        let mut dataset = RowDataSet::new("test".to_string());
        dataset.add_column("id".to_string(), ColumnType::I32).unwrap();
        dataset.add_column("name".to_string(), ColumnType::String).unwrap();
        
        dataset.add_row(vec![
            CellValue::I32(1),
            CellValue::String("Test".to_string()),
        ]).unwrap();

        let serialized = dataset.serialize();
        let deserialized = RowDataSet::deserialize(&serialized).unwrap();

        assert_eq!(dataset.dataset_id(), deserialized.dataset_id());
        assert_eq!(dataset.column_count(), deserialized.column_count());
        assert_eq!(dataset.row_count(), deserialized.row_count());
    }
} 