namespace ospbase.fb;

enum ColumnType : byte {
  I32,
  I64,
  F32,
  F64,
  String,
  Bool,
  DateTime,
  Binary
}

table CellValue {
  i32_value: int32;
  i64_value: int64;
  f32_value: float32;
  f64_value: float64;
  string_value: string;
  bool_value: bool;
  datetime_value: int64;  // Store as unix timestamp
  binary_value: [ubyte];
  value_type: ColumnType;
}

table ColumnInfo {
  name: string;
  column_type: ColumnType;
}

table RowData {
  values: [CellValue];
  children: [ChildDataSet];
}

table ChildDataSet {
  name: string;
  dataset: RowDataSet;
}

table RowDataSet {
  dataset_id: string;
  schema: [ColumnInfo];
  rows: [RowData];
}

root_type RowDataSet; 