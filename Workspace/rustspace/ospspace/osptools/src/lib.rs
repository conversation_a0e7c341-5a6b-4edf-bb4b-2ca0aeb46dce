pub mod ospcmd;
pub mod pid;

// use once_cell::sync::Lazy;
// use slog::{o, <PERSON><PERSON>,<PERSON><PERSON>};
// use sloggers::{Build, terminal::TerminalLoggerBuilder};

// 定义全局 Logger 变量
// pub static LOGGER: Lazy<Logger> = Lazy::new(build_logger);

// 
// fn build_logger() -> Logger {
//     let decorator = slog_term::TermDecorator::new().build();
//     let drain = slog_term::FullFormat::new(decorator).build().fuse();
//     let drain = slog_async::Async::new(drain).build().fuse();
//     let logger = slog::Logger::root(drain, o!());
//     logger
// }

use structopt::StructOpt;

#[derive(StructOpt, Debug)]
#[structopt(name = "ospserver")]
/// bitcomm instant message server
pub enum OSPServerOpt {
    /// Start open service platform server
    #[structopt(name = "start", help = "Start open service platform server")]
    StartServer,
    /// Stop open service platform server
    #[structopt(name = "stop",  help = "Stop open service platform server")]
    StopServer,
}




