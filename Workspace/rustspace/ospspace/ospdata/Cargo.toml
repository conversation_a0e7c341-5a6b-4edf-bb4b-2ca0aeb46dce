[package]
name = "ospdata"
version = "0.1.0"
edition = "2021"


[dependencies]
    ospbase = { path = "../ospbase" }
    sqlx = { version = "0.8", features = [ "runtime-tokio-rustls", "postgres", "any" ] }
    tokio = { version = "1.0", features = ["full"] }
    once_cell = "1"
    async-trait = "0.1"
    serde = "1.0"
    serde_json = "1.0"
    serde_yaml = "0.9"
    chrono = { version = "0.4", features = ["serde"] }
    log = "0.4"
    sea-orm = { version = "1", features = [
        "sqlx-postgres",    # PostgreSQL
        "sqlx-mysql",       # MySQL
        "sqlx-sqlite",      # SQLite
    ]}
    tokio-postgres = "0.7"
    thiserror = "2"