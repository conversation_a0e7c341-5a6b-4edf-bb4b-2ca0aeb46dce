async fn example_usage() -> DbResult<()> {
    // 创建数据库连接
    let sea_conn = // ... 创建 sea-orm 连接
    let db_conn = SeaDbConnection::new(sea_conn);
    
    // 创建用户仓储
    let user_repo = UserRepositoryImpl::new(&db_conn);
    
    // 创建新用户
    let new_user = User {
        id: 0,
        name: "<PERSON>".to_string(),
        email: "<EMAIL>".to_string(),
        created_at: chrono::Utc::now(),
    };
    
    // 保存用户
    let created_user = user_repo.create(&new_user).await?;
    println!("Created user: {:?}", created_user);
    
    // 查找用户
    if let Some(user) = user_repo.find_by_email("<EMAIL>").await? {
        println!("Found user: {:?}", user);
    }
    
    // 使用事务
    let txn = db_conn.begin().await?;
    // ... 执行事务操作
    txn.commit().await?;
    
    Ok(())
} 