use super::super::core::{<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>b<PERSON><PERSON><PERSON>, <PERSON>b<PERSON><PERSON>ult, DbValue, Row, Transaction};
use async_trait::async_trait;
use sea_orm::{DatabaseConnection, Statement, TransactionTrait};

pub struct SeaDbConnection {
    conn: DatabaseConnection,
}

impl SeaDbConnection {
    pub fn new(conn: DatabaseConnection) -> Self {
        Self { conn }
    }

    // 将我们的 DbValue 转换为 sea-orm 的 Value
    fn convert_param(value: DbValue) -> sea_orm::Value {
        match value {
            DbValue::Null => sea_orm::Value::Null,
            DbValue::Bool(b) => sea_orm::Value::Bool(Some(b)),
            DbValue::Int(i) => sea_orm::Value::BigInt(Some(i)),
            DbValue::Float(f) => sea_orm::Value::Double(Some(f)),
            DbValue::String(s) => sea_orm::Value::String(Some(Box::new(s))),
            DbValue::Bytes(b) => sea_orm::Value::Bytes(Some(b)),
        }
    }
}

#[async_trait]
impl DbConnection for SeaDbConnection {
    async fn query(&self, sql: &str, params: Vec<DbValue>) -> DbResult<Vec<Row>> {
        let sea_params: Vec<sea_orm::Value> = params.into_iter()
            .map(Self::convert_param)
            .collect();

        let stmt = Statement::from_sql_and_values(
            sea_orm::DbBackend::Postgres,
            sql,
            sea_params,
        );

        let result = self.conn.query_all(stmt).await
            .map_err(|e| DbError::QueryError(e.to_string()))?;

        // 转换结果
        result.into_iter()
            .map(|qr| {
                let mut values = HashMap::new();
                for (idx, col) in qr.columns().iter().enumerate() {
                    if let Some(value) = qr.try_get_by_index(idx).ok().flatten() {
                        values.insert(col.to_string(), value);
                    }
                }
                Ok(Row { values })
            })
            .collect()
    }

    async fn execute(&self, sql: &str, params: Vec<DbValue>) -> DbResult<u64> {
        let sea_params: Vec<sea_orm::Value> = params.into_iter()
            .map(Self::convert_param)
            .collect();

        let stmt = Statement::from_sql_and_values(
            sea_orm::DbBackend::Postgres,
            sql,
            sea_params,
        );

        let result = self.conn.execute(stmt).await
            .map_err(|e| DbError::QueryError(e.to_string()))?;

        Ok(result.rows_affected())
    }

    async fn begin(&self) -> DbResult<Box<dyn Transaction>> {
        let txn = self.conn.begin().await
            .map_err(|e| DbError::TransactionError(e.to_string()))?;
        Ok(Box::new(SeaDbTransaction { txn }))
    }
}

// Sea-ORM 事务实现
pub struct SeaDbTransaction {
    txn: sea_orm::DatabaseTransaction,
}

#[async_trait]
impl Transaction for SeaDbTransaction {
    async fn commit(self: Box<Self>) -> DbResult<()> {
        self.txn.commit().await
            .map_err(|e| DbError::TransactionError(e.to_string()))
    }

    async fn rollback(self: Box<Self>) -> DbResult<()> {
        self.txn.rollback().await
            .map_err(|e| DbError::TransactionError(e.to_string()))
    }
}