pub mod seadb;
pub mod sqlxdb;

use async_trait::async_trait;
use std::collections::HashMap;
// use std::error::Error;

/// 数据库值类型
#[derive(Debug, Clone)]
pub enum DbValue {
    Null,
    <PERSON><PERSON>(bool),
    Int(i64),
    Float(f64),
    String(String),
    Bytes(Vec<u8>),
    // 可以根据需要添加更多类型
}

/// 查询结果行
#[derive(Debug)]
pub struct Row {
    pub values: HashMap<String, DbValue>,
}

/// 数据库错误
#[derive(Debug, thiserror::Error)]
pub enum DbError {
    #[error("Connection error: {0}")]
    ConnectionError(String),
    #[error("Query error: {0}")]
    QueryError(String),
    #[error("Transaction error: {0}")]
    TransactionError(String),
    #[error("Value conversion error: {0}")]
    ValueConversionError(String),
}

/// 数据库结果类型
pub type DbResult<T> = Result<T, DbError>;

/// 数据库事务接口
#[async_trait]
pub trait Transaction: Send + Sync {
    async fn commit(self: Box<Self>) -> DbResult<()>;
    async fn rollback(self: Box<Self>) -> DbResult<()>;
}

/// 数据库连接接口
#[async_trait]
pub trait DbConnection: Send + Sync {
    /// 执行查询并返回结果集
    async fn query(&self, sql: &str, params: Vec<DbValue>) -> DbResult<Vec<Row>>;
    
    /// 执行命令并返回影响的行数
    async fn execute(&self, sql: &str, params: Vec<DbValue>) -> DbResult<u64>;
    
    /// 开始事务
    async fn begin(&self) -> DbResult<Box<dyn Transaction>>;
}