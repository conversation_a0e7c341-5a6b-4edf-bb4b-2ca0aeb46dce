use sqlx::any::AnyPoolOptions;

use super::*;
    
pub struct SQLiteDatabase {
    pool: Arc<AnyPool>,  // 直接存储 AnyPool
    _config: String,
}
impl OSPDatabase for SQLiteDatabase {
    fn pool(&self) -> Arc<AnyPool> {
        self.pool.clone()
    }
    fn db_id(&self) -> String {
        self._config.clone()
    }
    fn kind(&self) -> DatabaseType {
        DatabaseType::SQLite
    }
}
impl SQLiteDatabase {
    pub async fn new(url: &str, config: &str) -> Result<Self, Box<dyn Error>> {
        // 直接使用 AnyPoolOptions 创建 AnyPool
        let any_pool = AnyPoolOptions::new()
            .max_connections(5)
            .connect(url)
            .await?;

        Ok(Self {
            pool: Arc::new(any_pool),
            _config: config.to_string(),
        })
    }
}