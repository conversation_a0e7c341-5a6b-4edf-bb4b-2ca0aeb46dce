use sqlx::any::AnyPoolOptions;

use super::*;
    
pub struct PostgresDatabase {
    pool: Arc<AnyPool>,  // 直接存储 AnyPool
    _config: String,
}
impl OSPDatabase for PostgresDatabase {
    fn pool(&self) -> Arc<AnyPool> {
        self.pool.clone()
    }
    fn db_id(&self) -> String {
        self._config.clone()
    }
    fn kind(&self) -> DatabaseType {
        DatabaseType::Postgresql
    }
}
impl PostgresDatabase {
    pub async fn new(url: &str, config: &str) -> Result<Self, Box<dyn Error>> {
        // 直接使用 AnyPoolOptions 创建 AnyPool
        let any_pool = AnyPoolOptions::new()
            .max_connections(5)
            .connect(url)
            .await?;

        Ok(Self {
            pool: Arc::new(any_pool),
            _config: config.to_string(),
        })
    }
}

use sqlx::{Any, AnyPool, Pool, Row};
// use sqlx::postgres::{PgListener, PgNotification};
// use sqlx::types::JsonValue;
// use std::time::Duration;

trait PostgresExt {
    // 获取 PostgreSQL 连接池引用
    fn as_postgres_pool(&self) -> Result<&Pool<Any>, Box<dyn Error>>;
    
    // JSON 操作
    async fn json_operation(&self) -> Result<(), Box<dyn Error>>;
    
    // 数组操作
    async fn array_operation(&self) -> Result<(), Box<dyn Error>>;
    
    // LISTEN/NOTIFY 操作
    async fn listen_notifications(&self) -> Result<(), Box<dyn Error>>;
    
    // 使用 RETURNING 子句
    async fn insert_with_returning(&self) -> Result<i32, Box<dyn Error>>;
}

impl PostgresExt for PostgresDatabase {
    fn as_postgres_pool(&self) -> Result<&Pool<Any>, Box<dyn Error>> {
        if let DatabaseType::Postgresql = self.kind() {
            Ok(&self.pool)
        } else {
            Err("Not a PostgreSQL database".into())
        }
    }

    // JSON 操作示例
    async fn json_operation(&self) -> Result<(), Box<dyn Error>> {
        let pool = self.as_postgres_pool()?;
    
        // 将 JSON 转换为字符串
        let json_data = serde_json::json!({
            "name": "John",
            "age": 30,
            "tags": ["developer", "rust"]
        });
        let json_str = json_data.to_string();  // 转换为字符串
        
        // 使用 PostgreSQL 的 JSON 转换函数
        sqlx::query(
            "INSERT INTO users (data) VALUES ($1::jsonb)"
        )
        .bind(json_str)  // 绑定字符串
        .execute(pool)
        .await?;
        
        // 查询 JSON 数据
        let result = sqlx::query(
            "SELECT data->>'name' as name FROM users WHERE data->>'age' = $1"
        )
        .bind("30")
        .fetch_one(pool)
        .await?;
        
        let name: String = result.get("name");
        println!("Found user: {}", name);
        
        Ok(())
    }

    // 数组操作示例
    async fn array_operation(&self) -> Result<(), Box<dyn Error>> {
        let pool = self.as_postgres_pool()?;
    
        // 将数组转换为 PostgreSQL 数组语法的字符串
        let tags = "{rust,programming}";  // PostgreSQL 文本数组格式
        
        // 插入数组
        sqlx::query(
            "INSERT INTO posts (tags) VALUES ($1::text[])"
        )
        .bind(tags)
        .execute(pool)
        .await?;
        
        // 数组查询
        let _posts = sqlx::query(
            "SELECT * FROM posts WHERE $1 = ANY(tags::text[])"
        )
        .bind("rust")
        .fetch_all(pool)
        .await?;
        
        Ok(())
    }

    // LISTEN/NOTIFY 示例
    async fn listen_notifications(&self) -> Result<(), Box<dyn Error>> {
        let pool = self.as_postgres_pool()?;
        
        // 使用普通查询来实现 NOTIFY
        sqlx::query("LISTEN channel_name")
        .execute(pool)
        .await?;

        // 发送通知
        tokio::spawn({
        let pool = self.pool.clone();
        async move {
            sqlx::query("NOTIFY channel_name, 'Hello!'")
                .execute(&*pool)
                .await
                .unwrap();
        }
        });

        // 使用普通查询来检查通知
        let notifications = sqlx::query(
        "SELECT pid, channel, payload FROM pg_notification_queue_usage"
        )
        .fetch_all(pool)
        .await?;

        for row in notifications {
        let channel: String = row.get("channel");
        let payload: Option<String> = row.get("payload");
        println!(
            "Received notification: channel={}, payload={:?}",
            channel,
            payload
        );
        }

        Ok(())
    }

    // RETURNING 子句示例
    async fn insert_with_returning(&self) -> Result<i32, Box<dyn Error>> {
        let pool = self.as_postgres_pool()?;
        
        // 插入并返回生成的 ID
        let row = sqlx::query(
            "INSERT INTO users (name, email) VALUES ($1, $2) RETURNING id"
        )
        .bind("Alice")
        .bind("<EMAIL>")
        .fetch_one(pool)
        .await?;
        
        let id: i32 = row.get("id");
        Ok(id)
    }
}

// 使用示例
impl PostgresDatabase {
    pub async fn example_usage(&self) -> Result<(), Box<dyn Error>> {
        // JSON 操作
        self.json_operation().await?;
        
        // 数组操作
        self.array_operation().await?;
        
        // 监听通知
        self.listen_notifications().await?;
        
        // 插入并获取返回值
        let new_id = self.insert_with_returning().await?;
        println!("Inserted new record with ID: {}", new_id);
        
        Ok(())
    }
}