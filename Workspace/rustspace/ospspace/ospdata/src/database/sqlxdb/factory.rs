use super::*;
use tokio::sync::RwLock;
use std::collections::HashMap;
use std::sync::Arc;
use once_cell::sync::OnceCell;

// 使用 Box 包装 trait 对象
static INSTANCES: OnceCell<RwLock<HashMap<String, Arc<Box<dyn OSPDatabase>>>>> = OnceCell::new();

pub struct OSPDatabaseFactory;

impl OSPDatabaseFactory {
    fn init_instances() -> &'static RwLock<HashMap<String, Arc<Box<dyn OSPDatabase>>>> {
        INSTANCES.get_or_init(|| RwLock::new(HashMap::new()))
    }

    pub async fn create(
        db_type: DatabaseType,
        db_id: &str,
        url: &str,
    ) -> Result<Arc<Box<dyn OSPDatabase>>, Box<dyn Error>> {
        let instances = Self::init_instances();
        
        // 检查是否已存在
        if let Some(instance) = instances.read().await.get(db_id) {
            return Ok(instance.clone());
        }

        // Box 包装具体类型，使其成为固定大小
        let db: Box<dyn OSPDatabase> = match db_type {
            DatabaseType::Postgresql => {
                Box::new(postgresql::PostgresDatabase::new(url, db_id).await?)
            }
            DatabaseType::MySQL => {
                Box::new(mysql::MySQLDatabase::new(url, db_id).await?)
            }
            DatabaseType::SQLite => {
                Box::new(sqlite::SQLiteDatabase::new(url, db_id).await?)
            }
        };

        // Arc 提供共享所有权
        let instance = Arc::new(db);

        let mut instances = instances.write().await;
        if let Some(existing) = instances.get(db_id) {
            Ok(existing.clone())
        } else {
            instances.insert(db_id.to_string(), instance.clone());
            Ok(instance)
        }
    }

    pub async fn get_instance(db_id: &str) -> Result<Arc<Box<dyn OSPDatabase>>, Box<dyn Error>> {
        let instances = Self::init_instances();
        let instances = instances.read().await;
        
        instances.get(db_id)
            .cloned()
            .ok_or_else(|| format!("Database not initialized for db_id: {}", db_id).into())
    }

    pub async fn close(db_id: &str) -> Result<(), Box<dyn Error>> {
        let instances = Self::init_instances();
        let mut instances = instances.write().await;
        
        if let Some(instance) = instances.remove(db_id) {
            if Arc::strong_count(&instance) == 1 {
                if let Some(boxed_db) = Arc::into_inner(instance) {
                    let pool = boxed_db.pool();
                    pool.close().await;
                }
                Ok(())
            } else {
                Err("Database pool still has active references".into())
            }
        } else {
            Err(format!("No database found for db_id: {}", db_id).into())
        }
    }

    pub async fn close_all() -> Result<(), Box<dyn Error>> {
        let instances = Self::init_instances();
        let mut instances = instances.write().await;
        
        let mut errors = Vec::new();
        let db_ids: Vec<String> = instances.keys().cloned().collect();
        
        for db_id in db_ids {
            if let Some(instance) = instances.remove(&db_id) {
                if Arc::strong_count(&instance) == 1 {
                    if let Some(boxed_db) = Arc::into_inner(instance) {
                        let pool = boxed_db.pool();
                        pool.close().await;
                    }
                } else {
                    errors.push(format!("Pool {} still has active references", db_id));
                }
            }
        }
        
        if errors.is_empty() {
            Ok(())
        } else {
            Err(errors.join("; ").into())
        }
    }

    pub async fn check_pool_status(db_id: &str) -> Result<PoolStatus, Box<dyn Error>> {
        let instances = Self::init_instances();
        let instances = instances.read().await;
        
        if let Some(instance) = instances.get(db_id) {
            Ok(PoolStatus {
                reference_count: Arc::strong_count(instance),
                is_closed: false,
            })
        } else {
            Err(format!("No database found for db_id: {}", db_id).into())
        }
    }
}

// 连接池状态结构体
#[derive(Debug)]
pub struct PoolStatus {
    pub reference_count: usize,
    pub is_closed: bool,
}

// 使用示例
impl OSPDatabaseFactory {
    pub async fn example_usage() -> Result<(), Box<dyn Error>> {
        // 创建数据库连接
        let _db = Self::create(
            DatabaseType::MySQL,
            "db_id1",
            "mysql://user:pass@localhost/db"
        ).await?;
        
        // 使用数据库
        // ...
        
        // 检查连接池状态
        let status = Self::check_pool_status("db_id1").await?;
        println!("Pool status: {:?}", status);
        
        // 关闭特定的连接池
        Self::close("db_id1").await?;
        
        // 或者关闭所有连接池
        Self::close_all().await?;
        
        Ok(())
    }
} 