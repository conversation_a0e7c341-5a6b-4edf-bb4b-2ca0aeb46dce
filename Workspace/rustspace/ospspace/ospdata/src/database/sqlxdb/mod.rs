
use sqlx::AnyPool;
use std::{error::Error, sync::Arc};

// 数据库类型枚举
#[derive(Debug, <PERSON><PERSON>, Copy)]
pub enum DatabaseType {
    Postgresql,
    MySQL,
    SQLite,
    // MSSQL,
    // 可以添加更多数据库类型
}

// 通用数据库接口
// #[async_trait]
pub trait OSPDatabase: Send + Sync {
    fn pool(&self) -> Arc<AnyPool>;
    fn db_id(&self) -> String;
    fn kind(&self) -> DatabaseType;
}

// 工厂模块
pub mod factory;
// PostgreSQL 实现
pub mod postgresql;
// MySQL 实现
pub mod mysql;   
// SQLite 实现
pub mod sqlite;

pub use factory::OSPDatabaseFactory;


