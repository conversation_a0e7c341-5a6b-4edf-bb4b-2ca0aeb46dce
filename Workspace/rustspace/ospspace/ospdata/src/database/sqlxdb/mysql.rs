use async_trait::async_trait;
use sqlx::any::AnyPoolOptions;

use super::*;
// use std::error::Error;
    
pub struct MySQLDatabase {
    pool: Arc<AnyPool>,  // 直接存储 AnyPool
    _config: String,
}
impl OSPDatabase for MySQLDatabase {
    fn pool(&self) -> Arc<AnyPool> {
        self.pool.clone()
    }
    fn db_id(&self) -> String {
        self._config.clone()
    }
    fn kind(&self) -> DatabaseType {
        DatabaseType::MySQL
    }
}
impl MySQLDatabase {
    pub async fn new(url: &str, config: &str) -> Result<Self, Box<dyn std::error::Error>> {
        // 直接使用 AnyPoolOptions 创建 AnyPool
        let any_pool = AnyPoolOptions::new()
            .max_connections(5)
            .connect(url)
            .await?;

        Ok(Self {
            pool: Arc::new(any_pool),
            _config: config.to_string(),
        })
    }
}


use sqlx::{ Any<PERSON><PERSON>, <PERSON>, <PERSON>rro<PERSON>};
use std::sync::Arc;

// 1. MySQL 特定操作的 trait
#[async_trait]
pub trait MySQLExt {
    // 批量插入
    async fn batch_insert(&self, table: &str, rows: Vec<serde_json::Value>) 
        -> Result<u64, Box<dyn std::error::Error>>;
    
    // 使用 ON DUPLICATE KEY UPDATE
    async fn upsert(&self, table: &str, data: serde_json::Value) 
        -> Result<u64, Box<dyn std::error::Error>>;
    
    // 使用 LOCK IN SHARE MODE
    async fn select_with_share_lock(&self, query: &str) 
        -> Result<Vec<sqlx::any::AnyRow>, Box<dyn std::error::Error>>;
        
    // 使用 FOR UPDATE
    async fn select_for_update(&self, query: &str) 
        -> Result<Vec<sqlx::any::AnyRow>, Box<dyn std::error::Error>>;
}

// 2. 为数据库结构体实现 trait
#[async_trait]
impl MySQLExt for MySQLDatabase {
    async fn batch_insert(&self, table: &str, rows: Vec<serde_json::Value>) 
        -> Result<u64, Box<dyn std::error::Error>> 
    {
        let pool = &*self.pool;
        
        if rows.is_empty() {
            return Ok(0);
        }
        
        // 构建批量插入语句
        let first_row = &rows[0];
        let columns: Vec<String> = first_row.as_object()
            .ok_or("Invalid row format")?
            .keys()
            .map(|k| k.to_string())
            .collect();
            
        let values: Vec<String> = rows.iter()
            .map(|row| {
                let values: Vec<String> = columns.iter()
                    .map(|col| {
                        let value = &row[col];
                        if value.is_string() {
                            format!("'{}'", value.as_str().unwrap())
                        } else if value.is_null() {
                            "NULL".to_string()
                        } else {
                            value.to_string()
                        }
                    })
                    .collect();
                format!("({})", values.join(","))
            })
            .collect();
            
        let sql = format!(
            "INSERT INTO {} ({}) VALUES {}",
            table,
            columns.join(","),
            values.join(",")
        );
        
        let result = sqlx::query(&sql)
            .execute(pool)
            .await?;
            
        Ok(result.rows_affected())
    }
    
    async fn upsert(&self, table: &str, data: serde_json::Value) 
        -> Result<u64, Box<dyn std::error::Error>> 
    {
        let pool = &*self.pool;
        
        let obj = data.as_object()
            .ok_or("Invalid data format")?;
            
        let columns: Vec<String> = obj.keys()
            .map(|k| k.to_string())
            .collect();
            
        let values: Vec<String> = obj.values()
            .map(|v| {
                if v.is_string() {
                    format!("'{}'", v.as_str().unwrap())
                } else if v.is_null() {
                    "NULL".to_string()
                } else {
                    v.to_string()
                }
            })
            .collect();
            
        let updates: Vec<String> = columns.iter()
            .map(|col| format!("{0}=VALUES({0})", col))
            .collect();
            
        let sql = format!(
            "INSERT INTO {} ({}) VALUES ({}) ON DUPLICATE KEY UPDATE {}",
            table,
            columns.join(","),
            values.join(","),
            updates.join(",")
        );
        
        let result = sqlx::query(&sql)
            .execute(pool)
            .await?;
            
        Ok(result.rows_affected())
    }
    
    async fn select_with_share_lock(&self, query: &str) 
        -> Result<Vec<sqlx::any::AnyRow>, Box<dyn std::error::Error>> 
    {
        let pool = &*self.pool;
        
        let sql = format!("{} LOCK IN SHARE MODE", query);
        let rows = sqlx::query(&sql)
            .fetch_all(pool)
            .await?;
            
        Ok(rows)
    }
    
    async fn select_for_update(&self, query: &str) 
        -> Result<Vec<sqlx::any::AnyRow>, Box<dyn std::error::Error>> 
    {
        let pool = &*self.pool;
        
        let sql = format!("{} FOR UPDATE", query);
        let rows = sqlx::query(&sql)
            .fetch_all(pool)
            .await?;
            
        Ok(rows)
    }
}

// 3. 为查询结果实现 trait
trait ResultExt {
    fn _get_decimal(&self, index: &str) -> Result<f64, Error>;
    fn get_json(&self, index: &str) -> Result<serde_json::Value, Error>;
}

impl ResultExt for sqlx::any::AnyRow {
    fn _get_decimal(&self, index: &str) -> Result<f64, Error> {
        let value: String = self.get(index);
        value.parse::<f64>().map_err(|e| Error::ColumnDecode {
            index: index.into(),
            source: Box::new(e),
        })
    }
    
    fn get_json(&self, index: &str) -> Result<serde_json::Value, Error> {
        let value: String = self.get(index);
        serde_json::from_str(&value).map_err(|e| Error::ColumnDecode {
            index: index.into(),
            source: Box::new(e),
        })
    }
}

// 4. 使用示例
impl MySQLDatabase {
    pub async fn example_mysql_operations(&self) -> Result<(), Box<dyn std::error::Error>> {
        // 批量插入
        let rows = vec![
            serde_json::json!({
                "name": "John",
                "age": 30
            }),
            serde_json::json!({
                "name": "Jane",
                "age": 25
            })
        ];
        
        let affected = self.batch_insert("users", rows).await?;
        println!("Inserted {} rows", affected);
        
        // Upsert
        let data = serde_json::json!({
            "id": 1,
            "name": "John Updated",
            "age": 31
        });
        
        let affected = self.upsert("users", data).await?;
        println!("Upserted {} rows", affected);
        
        // 使用共享锁查询
        let rows = self.select_with_share_lock("SELECT * FROM users")
            .await?;
            
        for row in rows {
            let name: String = row.get("name");
            let age: i32 = row.get("age");
            println!("User: {} ({})", name, age);
            
            // 使用扩展方法
            if let Ok(json_data) = row.get_json("extra_data") {
                println!("Extra data: {:?}", json_data);
            }
        }
        
        Ok(())
    }
}