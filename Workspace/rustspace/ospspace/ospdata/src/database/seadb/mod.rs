use sea_orm::{ConnectionTrait, Database, DatabaseConnection, DbBackend, DbErr, QueryResult, Statement};
use std::collections::HashMap;
use std::sync::Arc;
use tokio::sync::RwLock;
use once_cell::sync::OnceCell;

// 数据库类型枚举
#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash)]
pub enum DatabaseType {
    Postgresql,
    MySQL,
    SQLite,
}

// 数据库配置
#[derive(Debug, <PERSON>lone)]
pub struct DatabaseConfig {
    pub db_type: DatabaseType,
    pub url: String,
    pub min_connections: u32,
    pub max_connections: u32,
    pub connect_timeout: u64,  // 秒
    pub idle_timeout: u64,     // 秒
}

// 数据库管理器
pub struct DatabaseManager {
    connections: Arc<RwLock<HashMap<String, DatabaseConnection>>>,
}

// 全局实例
static INSTANCE: OnceCell<DatabaseManager> = OnceCell::new();

impl DatabaseManager {
    // 获取单例实例
    pub fn instance() -> &'static DatabaseManager {
        INSTANCE.get_or_init(|| DatabaseManager {
            connections: Arc::new(RwLock::new(HashMap::new())),
        })
    }

    // 创建新的数据库连接
    pub async fn create_connection(
        &self,
        db_id: &str,
        config: DatabaseConfig,
    ) -> Result<DatabaseConnection, DbErr> {
        // 检查是否已存在
        if let Some(conn) = self.get_connection(db_id).await {
            return Ok(conn);
        }

        let mut opt = sea_orm::ConnectOptions::new(config.url);
        opt.max_connections(config.max_connections)
            .min_connections(config.min_connections)
            .connect_timeout(std::time::Duration::from_secs(config.connect_timeout))
            .idle_timeout(std::time::Duration::from_secs(config.idle_timeout))
            .sqlx_logging(true);

        let conn = Database::connect(opt).await?;
        self.connections.write().await.insert(db_id.to_string(), conn.clone());
        
        Ok(conn)
    }

    // 获取已存在的连接
    pub async fn get_connection(&self, db_id: &str) -> Option<DatabaseConnection> {
        self.connections.read().await.get(db_id).cloned()
    }

    // 关闭指定连接
    pub async fn close_connection(&self, db_id: &str) -> Result<(), DbErr> {
        if self.connections.write().await.remove(db_id).is_some() {
            Ok(())
        } else {
            Err(DbErr::Custom(format!("No connection found for id: {}", db_id)))
        }
    }

    // 关闭所有连接
    pub async fn close_all(&self) -> Result<(), DbErr> {
        self.connections.write().await.clear();
        Ok(())
    }

    // 检查连接状态
    pub async fn check_connection(&self, db_id: &str) -> Result<bool, DbErr> {
        if let Some(conn) = self.get_connection(db_id).await {
            // 执行简单查询测试连接
            match conn.execute_unprepared("SELECT 1").await {
                Ok(_) => Ok(true),
                Err(e) => Err(e),
            }
        } else {
            Ok(false)
        }
    }
}
#[allow(dead_code)]
async fn query_and_print(db: &DatabaseConnection) -> Result<(), DbErr> {
    // 执行查询
    let result = db
        .query_all(Statement::from_string(
            DbBackend::Postgres,
            "SELECT * FROM SYS_OBJECTS".to_owned()
        ))
        .await?;

    println!("query result: {:?}", result);
    // 打印结果
     print_query_results(&result).await;
    
    Ok(())
}
#[allow(dead_code)]
async fn print_query_results(results: &Vec<QueryResult>) {
    if results.is_empty() {
        println!("No results found");
        return;
    }

    // 获取列名
    // Get column names
    let columns: Vec<String> = if let Some(first_row) = results.first() {
        first_row.column_names()
            .iter()
            .map(|col| col.to_string())
            .collect()
    } else {
        return;
    };

    // 打印表头
    // print_header(&columns);

    // 打印数据行
    for row in results {
        print_row(row, &columns);
    }

    // 打印表尾
    // print_footer(&columns);
}
#[allow(dead_code)]
fn print_header(columns: &[String]) {
    // 计算每列宽度
    let widths: Vec<usize> = columns
        .iter()
        .map(|col| col.len().max(15))
        .collect();

    // 打印顶部边框
    print!("┌");
    for width in &widths {
        print!("{:─^width$}┬", "", width = width + 2);
    }
    println!("\\b│");

    // 打印列名
    print!("│");
    for (i, col) in columns.iter().enumerate() {
        print!(" {:<width$} │", col, width = widths[i]);
    }
    println!();

    // 打印分隔线
    print!("├");
    for width in &widths {
        print!("{:─^width$}┼", "", width = width + 2);
    }
    println!("\\b┤");
}

fn print_row(row: &QueryResult, columns: &[String]) {
    let widths: Vec<usize> = columns
        .iter()
        .map(|col| col.len().max(15))
        .collect();

    print!("│");
    for (i, _) in columns.iter().enumerate() {
        let value = if let Some(v) = row.try_get_by_index::<Option<String>>(i).ok().flatten() {
            v
        } else if let Some(v) = row.try_get_by_index::<Option<i32>>(i).ok().flatten() {
            v.to_string()
        } else if let Some(v) = row.try_get_by_index::<Option<f64>>(i).ok().flatten() {
            v.to_string()
        } else {
            "NULL".to_string()
        };
        print!(" {:<width$} │", value, width = widths[i]);
    }
    println!();
}
#[allow(dead_code)]
fn print_footer(columns: &[String]) {
    let widths: Vec<usize> = columns
        .iter()
        .map(|col| col.len().max(15))
        .collect();

    print!("└");
    for width in &widths {
        print!("{:─^width$}┴", "", width = width + 2);
    }
    println!("\\b┘");
}

// 使用示例
#[tokio::test]
async fn test_database_manager() -> Result<(), Box<dyn std::error::Error>> {
    // PostgreSQL 配置
    let pg_config = DatabaseConfig {
        db_type: DatabaseType::Postgresql,
        url: "postgres://bitcomm:caslfounder@localhost:5432/postgres".to_string(),
        min_connections: 5,
        max_connections: 10,
        connect_timeout: 10,
        idle_timeout: 300,
    };

    // // MySQL 配置
    // let mysql_config = DatabaseConfig {
    //     db_type: DatabaseType::MySQL,
    //     url: "mysql://user:pass@localhost/db".to_string(),
    //     min_connections: 5,
    //     max_connections: 10,
    //     connect_timeout: 10,
    //     idle_timeout: 300,
    // };

    // // SQLite 配置
    // let sqlite_config = DatabaseConfig {
    //     db_type: DatabaseType::SQLite,
    //     url: "sqlite::memory:".to_string(),
    //     min_connections: 1,
    //     max_connections: 5,
    //     connect_timeout: 5,
    //     idle_timeout: 300,
    // };

    let manager = DatabaseManager::instance();

    // 创建连接
    let pg_conn = manager.create_connection("pg_db", pg_config).await?;
    // let mysql_conn = manager.create_connection("mysql_db", mysql_config).await?;
    // let sqlite_conn = manager.create_connection("sqlite_db", sqlite_config).await?;

    query_and_print(&pg_conn).await?;
    // 执行查询
    let result = pg_conn
        .execute_unprepared("SELECT * FROM SYS_OBJECTS")
        .await?;
    println!("Query result: {:?}", result);

    // 关闭连接
    manager.close_connection("pg_db").await?;
    // manager.close_connection("mysql_db").await?;
    // manager.close_connection("sqlite_db").await?;

    Ok(())
}
