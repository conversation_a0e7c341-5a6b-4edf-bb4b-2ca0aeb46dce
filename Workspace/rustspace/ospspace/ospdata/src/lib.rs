pub mod database;


#[cfg(test)]
mod tests {
    use super::*;
    use std::error::Error;
    use database::sqlxdb::{DatabaseType, OSPDatabaseFactory};
    use sqlx::Row;

    #[tokio::test]
    async fn test_database_connection() -> Result<(), Box<dyn Error>> {
        // 创建数据库连接
        let db = OSPDatabaseFactory::create(
            DatabaseType::Postgresql,
            "pg_test_001",
            "postgres://bitcomm:caslfounder@localhost:5432/postgres"
        ).await.map_err(|e| {
            eprintln!("Failed to create database connection: {}", e);
            e
        })?;

        // 测试连接
        let pool = db.pool();
        let result = sqlx::query("SELECT 1 as test")
            .fetch_one(&*pool)
            .await
            .map_err(|e| {
                eprintln!("Failed to execute query: {}", e);
                e
            })?;
    
        let value: i32 = result.get("test");
        println!("Connection successful! Test value: {}", value);

        // 关闭连接
        OSPDatabaseFactory::close("pg_test_001").await.map_err(|e| {
            eprintln!("Failed to close connection: {}", e);
            e
        })?;
    
        Ok(())
    }
}