{"rustc": 12610991425282158916, "features": "[\"_rt-tokio\", \"any\", \"bigdecimal\", \"chrono\", \"default\", \"derive\", \"json\", \"macros\", \"migrate\", \"mysql\", \"postgres\", \"runtime-tokio\", \"runtime-tokio-rustls\", \"rust_decimal\", \"sqlite\", \"sqlx-macros\", \"sqlx-mysql\", \"sqlx-postgres\", \"sqlx-sqlite\", \"time\", \"tls-rustls-ring\", \"uuid\"]", "declared_features": "[\"_rt-async-std\", \"_rt-tokio\", \"_unstable-all-types\", \"all-databases\", \"any\", \"bigdecimal\", \"bit-vec\", \"chrono\", \"default\", \"derive\", \"ipnetwork\", \"json\", \"mac_address\", \"macros\", \"migrate\", \"mysql\", \"postgres\", \"regexp\", \"runtime-async-std\", \"runtime-async-std-native-tls\", \"runtime-async-std-rustls\", \"runtime-tokio\", \"runtime-tokio-native-tls\", \"runtime-tokio-rustls\", \"rust_decimal\", \"sqlite\", \"sqlx-macros\", \"sqlx-mysql\", \"sqlx-postgres\", \"sqlx-sqlite\", \"time\", \"tls-native-tls\", \"tls-none\", \"tls-rustls\", \"tls-rustls-aws-lc-rs\", \"tls-rustls-ring\", \"uuid\"]", "target": 3003836824758849296, "profile": 8276155916380437441, "path": 14993937237744864603, "deps": [[4099081089593030972, "sqlx_macros", false, 9611731494572813553], [5165484264156733570, "sqlx_postgres", false, 2741925909248862760], [10891901716061744475, "sqlx_mysql", false, 8560034647022224964], [13258204158141066724, "sqlx_sqlite", false, 5186107891319212208], [14233834141405476209, "sqlx_core", false, 6477920598897270211]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/sqlx-54b50e77986d2c4a/dep-lib-sqlx", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}