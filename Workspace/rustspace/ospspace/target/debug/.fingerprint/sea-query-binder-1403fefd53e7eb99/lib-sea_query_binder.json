{"rustc": 12610991425282158916, "features": "[\"bigdecimal\", \"chrono\", \"postgres-array\", \"rust_decimal\", \"serde_json\", \"sqlx\", \"sqlx-mysql\", \"sqlx-postgres\", \"sqlx-sqlite\", \"time\", \"uuid\", \"with-bigdecimal\", \"with-chrono\", \"with-json\", \"with-rust_decimal\", \"with-time\", \"with-uuid\"]", "declared_features": "[\"bigdecimal\", \"chrono\", \"ipnetwork\", \"mac_address\", \"pgvector\", \"postgres-array\", \"postgres-vector\", \"runtime-actix\", \"runtime-actix-native-tls\", \"runtime-actix-rustls\", \"runtime-async-std\", \"runtime-async-std-native-tls\", \"runtime-async-std-rustls\", \"runtime-tokio\", \"runtime-tokio-native-tls\", \"runtime-tokio-rustls\", \"rust_decimal\", \"serde_json\", \"sqlx\", \"sqlx-any\", \"sqlx-mysql\", \"sqlx-postgres\", \"sqlx-sqlite\", \"time\", \"uuid\", \"with-bigdecimal\", \"with-chrono\", \"with-ipnetwork\", \"with-json\", \"with-mac_address\", \"with-rust_decimal\", \"with-time\", \"with-uuid\"]", "target": 2357794343378131723, "profile": 8276155916380437441, "path": 2588011351661860796, "deps": [[1831276852971939893, "sea_query", false, 17330690931455958967], [5085991824766922639, "bigdecimal", false, 11262115939825226465], [8324636962323428845, "serde_json", false, 12048726571608547278], [12571872060290106065, "sqlx", false, 9099615232727075944], [13799876851028335573, "chrono", false, 13825866730077229452], [14950883590652370704, "time", false, 11775247088109979934], [16774096516800128208, "rust_decimal", false, 555642231021747211], [16829695972291065638, "uuid", false, 1967591514920217773]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/sea-query-binder-1403fefd53e7eb99/dep-lib-sea_query_binder", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}