{"rustc": 12610991425282158916, "features": "[]", "declared_features": "[]", "target": 14511142063029072275, "profile": 619605765252926426, "path": 4913780452107220577, "deps": [[65234016722529558, "bincode", false, 11573194324090343450], [89447989096775169, "metrics_exporter_prometheus", false, 14949595469310663774], [1109932657095357179, "tower_http", false, 10768009988029694757], [1930423065882823501, "redis", false, 1531162961547646971], [2337077568557944517, "slog", false, 6894067813239298079], [3107892209185275978, "tokio", false, 755566411496404598], [4891297352905791595, "axum", false, 10316541320056730375], [5070769681332304831, "once_cell", false, 7901721800869074658], [6606131838865521726, "ctor", false, 4579685969153884867], [6722490998346977199, "r2d2", false, 11830011745402246883], [7243058894955796457, "axum_extra", false, 6559664414711065300], [8008191657135824715, "thiserror", false, 4049992755599759053], [8324636962323428845, "serde_json", false, 12048726571608547278], [10967960060725374459, "serde", false, 18246721990676251481], [11594979262886006466, "tracing_appender", false, 14927962522415986410], [11881373857579626986, "claims", false, 15204589935855416808], [12382237672615274180, "config", false, 5977720697688254888], [12506636082296698907, "jsonwebtoken", false, 2615180792092364324], [12571872060290106065, "sqlx", false, 9099615232727075944], [12763872976636226336, "async_trait", false, 13195515707158192966], [13799876851028335573, "chrono", false, 13825866730077229452], [13936095911511827585, "metrics", false, 16752857677207318587], [14626413149905853098, "tracing", false, 2527770700792196429], [16201618127934729580, "tower", false, 7891851504726574547], [16691292891898454663, "tracing_subscriber", false, 6962531005990719156], [16727320580018311824, "prometheus", false, 5423103331859647512], [17917672826516349275, "lazy_static", false, 8142131422613413082], [18112009879309521262, "argon2", false, 8380801914105610248], [18171404909646082437, "r2d2_redis2", false, 1571618575574828747]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/ospwebservice-c8b0e6acfe529724/dep-test-lib-ospwebservice", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}