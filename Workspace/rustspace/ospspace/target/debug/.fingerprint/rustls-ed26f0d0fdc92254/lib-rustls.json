{"rustc": 12610991425282158916, "features": "[\"aws_lc_rs\", \"ring\", \"std\", \"tls12\"]", "declared_features": "[\"aws-lc-rs\", \"aws_lc_rs\", \"brotli\", \"custom-provider\", \"default\", \"fips\", \"hashbrown\", \"log\", \"logging\", \"read_buf\", \"ring\", \"rustversion\", \"std\", \"tls12\", \"zlib\"]", "target": 4618819951246003698, "profile": 8276155916380437441, "path": 15637698015451293500, "deps": [[507245148068099358, "ring", false, 6710565054215200435], [4583563751859662683, "aws_lc_rs", false, 7291726752117468502], [5070769681332304831, "once_cell", false, 7901721800869074658], [5501164552881223878, "pki_types", false, 9608380809180839343], [6528079939221783635, "zeroize", false, 6008038891487209571], [10227447102144303643, "build_script_build", false, 15704456525701294268], [12989347533245466967, "<PERSON><PERSON><PERSON>", false, 11170180463586899280], [17003143334332120809, "subtle", false, 786796352266990767]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/rustls-ed26f0d0fdc92254/dep-lib-rustls", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}