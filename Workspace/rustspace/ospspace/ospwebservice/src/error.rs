use thiserror::Error;
use serde::{Serialize, Deserialize};

#[derive(Error, Debug, Serialize, Deserialize)]
pub enum ServiceError {
    #[error("Authentication error: {0}")]
    AuthError(String),
    
    #[error("Database error: {0}")]
    DbError(String),
    
    #[error("Invalid request: {0}")]
    InvalidRequest(String),
    
    #[error("Internal server error: {0}")]
    InternalError(String),
    
    #[error("Rate limit exceeded")]
    RateLimitExceeded,
    
    #[error("Resource not found: {0}")]
    NotFound(String),
}

#[derive(Serialize, Deserialize)]
pub struct ErrorResponse {
    pub code: i32,
    pub message: String,
    pub details: Option<String>,
}

impl From<ServiceError> for ErrorResponse {
    fn from(error: ServiceError) -> Self {
        match error {
            ServiceError::AuthError(msg) => ErrorResponse {
                code: 401,
                message: "Authentication failed".to_string(),
                details: Some(msg),
            },
            ServiceError::DbError(msg) => ErrorResponse {
                code: 500,
                message: "Database error".to_string(),
                details: Some(msg),
            },
            ServiceError::InvalidRequest(_) => todo!(),
            ServiceError::InternalError(_) => todo!(),
            ServiceError::RateLimitExceeded => todo!(),
            ServiceError::NotFound(_) => todo!(),
            // ... 其他错误类型的映射
        }
    }
} 