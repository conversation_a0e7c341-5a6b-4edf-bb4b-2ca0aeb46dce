#[cfg(test)]
mod tests {
    use super::*;
    use crate::jsonrpc::{JsonRequest, JsonResponseWrapper};
    use crate::usermanager::{register_user, login_user};
    use mockall::predicate::*;
    use mockall::mock;

    mock! {
        Redis {}
        impl AsyncCommands for Redis {
            async fn exists(&self, key: &str) -> RedisResult<bool>;
            async fn hset(&self, key: &str, field: &str, value: &str) -> RedisResult<()>;
            async fn hget(&self, key: &str, field: &str) -> RedisResult<String>;
        }
    }

    #[tokio::test]
    async fn test_user_registration() {
        let mut mock_redis = MockRedis::new();
        mock_redis
            .expect_exists()
            .with(eq("testuser"))
            .returning(|_| Ok(false));
        
        mock_redis
            .expect_hset()
            .returning(|_, _, _| Ok(()));

        let result = register_user(&mock_redis, "testuser", "password123").await;
        assert!(result.is_ok());
    }

    #[tokio::test]
    async fn test_json_rpc_add() {
        let request = JsonRequest {
            jsonrpc: "2.0".to_string(),
            method: "add".to_string(),
            params: Some(HashMap::from([
                ("a".to_string(), json!(5)),
                ("b".to_string(), json!(3)),
            ])),
            id: json!(1),
            jwt_token: "test_token".to_string(),
        };

        let handler = AddJsonRpcHandler;
        let response = handler.json_rpc_handle(Json(request)).await;
        
        assert_eq!(response.0.result, json!(8));
    }
} 