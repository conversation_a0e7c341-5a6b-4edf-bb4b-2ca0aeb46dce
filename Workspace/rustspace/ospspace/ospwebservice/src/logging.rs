use tracing::{Level, Subscriber};
use tracing_subscriber::{FmtSubscriber, EnvFilter};
use tracing_appender::rolling::{RollingFileAppender, Rotation};
use metrics::{counter, gauge, histogram};
use std::time::Instant;

pub fn setup_logging() -> Box<dyn Subscriber> {
    let file_appender = RollingFileAppender::new(
        Rotation::DAILY,
        "logs",
        "ospwebservice.log",
    );

    Box::new(FmtSubscriber::builder()
        .with_env_filter(EnvFilter::from_default_env()
            .add_directive(Level::INFO.into())
            .add_directive("ospwebservice=debug".parse().unwrap()))
        .with_writer(file_appender)
        .with_thread_ids(true)
        .with_target(true)
        .with_file(true)
        .with_line_number(true)
        .pretty()
        .build())
}

pub struct RequestMetrics {
    start_time: Instant,
    path: String,
}

impl RequestMetrics {
    pub fn new(path: String) -> Self {
        counter!("http_requests_total", "path" => path.clone()).increment(1);
        Self {
            start_time: Instant::now(),
            path,
        }
    }

    pub fn record_response(&self, status: u16) {
        let duration = self.start_time.elapsed();
        histogram!("http_request_duration_seconds", "path" => self.path.clone())
            .record(duration.as_secs_f64());
        counter!("http_responses_total", 
            "path" => self.path.clone(),
            "status" => status.to_string()
        ).increment(1);
    }
} 