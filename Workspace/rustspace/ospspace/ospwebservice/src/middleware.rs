use axum::{
    middleware::Next,
    response::Response,
    http::Request,
};
use tracing::{info, error};
use crate::logging::RequestMetrics;
use crate::error::ServiceError;

pub async fn logging_middleware(
    request: Request<axum::body::Body>,
    next: Next<axum::body::Body>,
) -> Result<Response, ServiceError> {
    let path = request.uri().path().to_string();
    let metrics = RequestMetrics::new(path.clone());

    info!("Incoming request: {}", path);

    let response = next.run(request).await;
    let status = response.status().as_u16();

    metrics.record_response(status);
    info!("Request completed: {} with status {}", path, status);

    Ok(response)
}

pub async fn error_handling_middleware<B>(
    request: Request<B>,
    next: Next<B>,
) -> Result<Response, ServiceError> {
    match next.run(request).await {
        Ok(response) => Ok(response),
        Err(err) => {
            error!("Request failed: {}", err);
            Err(ServiceError::InternalError(err.to_string()))
        }
    }
} 