CREATE TABLE SYS_MODEL (
  MDL_ID varchar(30) NOT NULL,
  MDL_MC varchar(60),
  MDL_TYPE char(4) NOT NULL DEFAULT '',
  MDL_KEYDCT varchar(30),
  MDL_UNITDCT varchar(30),
  KEY_SET char(1) NOT NULL DEFAULT '0',
  SYS_ID varchar(30) NOT NULL,
  F_STAU numeric(30) DEFAULT 0,
  F_CRDATE timestamp without time zone,
  F_CHDATE timestamp without time zone,
  MDL_SJLX char(1) NOT NULL DEFAULT 'Y',
  MDL_NDCT varchar(30),
  MDL_YDCT varchar(30),
  MDL_RDCT varchar(30),
  MDL_BHDCT varchar(30),
  MDL_TYDCT varchar(30),
  CVT_UNITSET char(1) NOT NULL DEFAULT '0',
  F_BM_STAU numeric(30) DEFAULT 0,
  F_DF_STAU numeric(30) DEFAULT 0,
  F_CRUSER varchar(30),
  F_CHUSER varchar(30),
  F_ENABLE char(1),
  F_MODELFKEY varchar(30) DEFAULT '0',
  OBJ_GROUP varchar(100),
  CONSTRAINT SYS_MODEL_KEY UNIQUE (MDL_ID)
);

CREATE TABLE SYS_MDL_CTN (
  MDL_ID varchar(30) NOT NULL,
  CTN_ID varchar(30) NOT NULL,
  CTN_TYPE char(4) NOT NULL,
  CTN_MC varchar(255),
  CTN_FCT1 varchar(30) NOT NULL,
  CTN_FCT2 varchar(30),
  CTN_FCT3 varchar(30),
  CTN_FCT4 varchar(30),
  CTN_FCT5 varchar(30),
  CTN_FCT6 varchar(30),
  CTN_FCT7 varchar(30),
  CTN_FCT8 varchar(30),
  CTN_FCT9 varchar(30),
  CTN_FCT10 varchar(30),
  CTN_FCT11 varchar(30),
  CTN_FCT12 varchar(30),
  CTN_FCT13 varchar(30),
  CTN_FCT14 varchar(30),
  CTN_FCT15 varchar(30),
  CTN_FCT16 varchar(30),
  PCTN_ID varchar(30),
  F_CRDATE timestamp without time zone,
  F_CHDATE timestamp without time zone,
  F_CRUSER varchar(30),
  F_CHUSER varchar(30),
  CONSTRAINT SYS_MDL_CTN_KEY UNIQUE (MDL_ID, CTN_ID)
);

CREATE TABLE SYS_MDL_VAL (
  MDL_ID varchar(30) NOT NULL,
  MDL_KEY varchar(30) NOT NULL,
  UNIT_ID varchar(30),
  MDL_VALUE varchar(255),
  MDL_NOTE varchar(100),
  CONSTRAINT SYS_MDL_VAL_KEY UNIQUE (MDL_ID, MDL_KEY, UNIT_ID)
);

CREATE TABLE SYS_RLGL (
  RLGL_ID varchar(30) NOT NULL,
  RLGL_MC varchar(60) NOT NULL,
  DCT_ID1 varchar(30) NOT NULL,
  DCT_ID2 varchar(30) NOT NULL,
  RLGL_OBJID varchar(30),
  DCT_BMCOL1 varchar(30),
  DCT_BMCOL2 varchar(30),
  RLGL_YEAR char(2) DEFAULT '00',
  RLGL_UNIT char(2) DEFAULT '00',
  RLGL_USCST char(2) DEFAULT '00',
  RLGL_TYPE char(2) DEFAULT '00',
  SYS_ID varchar(30),
  ISCREATEDDW varchar(1) DEFAULT '0',
  ISSORTYEAR varchar(1) DEFAULT '0',
  ISWH varchar(1) DEFAULT '1',
  ISORDER char(1) NOT NULL DEFAULT '0',
  ISMUSTMX_SUBDCT char(1) NOT NULL DEFAULT '0',
  ISCHECK_SAMEGRADE char(1) DEFAULT '0',
  RLT_OBJID varchar(30),
  F_CRDATE timestamp without time zone,
  F_CHDATE timestamp without time zone,
  F_CRUSER varchar(30),
  F_CHUSER varchar(30),
  CONSTRAINT SYS_RLGL_KEY UNIQUE (RLGL_ID)
);

CREATE TABLE SYS_DICTS (
  DCT_ID varchar(30) NOT NULL,
  OBJ_ID varchar(100) NOT NULL,
  DCT_MC varchar(255) NOT NULL,
  DCT_NAME varchar(255),
  DCT_DES varchar(255),
  DCT_QXOBJID varchar(30),
  DCT_TYPE varchar(2) NOT NULL,
  DCT_BMCOLID varchar(30),
  DCT_BZCOLID varchar(30),
  DCT_MCCOLID varchar(30),
  DCT_JSCOLID varchar(30),
  DCT_MXCOLID varchar(30),
  DCT_BMSTRU varchar(30),
  DCT_PTCOLID varchar(30),
  DCT_KZCOLID varchar(30),
  SYS_ID varchar(30) NOT NULL,
  DCT_FIXLEN char(1) DEFAULT '0',
  DCT_STEP numeric(30) DEFAULT 1,
  DCT_KPI char(1) DEFAULT '0',
  DCT_SELECT char(1) DEFAULT '0',
  DCT_MUNIT char(1) DEFAULT '0',
  DCT_CTLONE char(1) DEFAULT '0',
  DCT_EXTAUTO char(1) DEFAULT '1',
  DCT_CREATE char(1) DEFAULT '1',
  DCT_CHANGE char(1) DEFAULT '1',
  DCT_NOTUSE char(1) DEFAULT '1',
  DCT_SUBJECT varchar(30),
  DCT_UNIT varchar(30),
  DCT_CST char(2) DEFAULT '00',
  DCT_FKEY1 varchar(30),
  DCT_FKEYDCT1 varchar(30),
  DCT_FKEY2 varchar(30),
  DCT_FKEYDCT2 varchar(30),
  DCT_FKEY3 varchar(30),
  DCT_FKEYDCT3 varchar(30),
  DCT_FKEY4 varchar(30),
  DCT_FKEYDCT4 varchar(30),
  DCT_FKEY5 varchar(30),
  DCT_FKEYDCT5 varchar(30),
  DCT_FKEY6 varchar(30),
  DCT_FKEYDCT6 varchar(30),
  DCT_FKEY7 varchar(30),
  DCT_FKEYDCT7 varchar(30),
  DCT_FKEY8 varchar(30),
  DCT_FKEYDCT8 varchar(30),
  DCT_SYNCDATE timestamp without time zone,
  F_STAU numeric(30) NOT NULL DEFAULT 0,
  F_CRDATE timestamp without time zone,
  F_CHDATE timestamp without time zone,
  DCT_QXSTAT varchar(1) DEFAULT '0',
  DCT_AFFIXDCT varchar(70),
  F_GUID varchar(36),
  F_CRUSER varchar(30),
  F_CHUSER varchar(30),
  F_ENABLE varchar(1),
  F_DELETED varchar(1),
  F_DICTSFKEY varchar(30) DEFAULT '0',
  CONSTRAINT SYS_DICTS_KEY UNIQUE (DCT_ID)
);

CREATE TABLE SYS_DCT_CST (
  DCT_ID varchar(30) NOT NULL,
  UNIT_ID varchar(30) NOT NULL DEFAULT ' ',
  DCT_KEY varchar(30) NOT NULL,
  DCT_VALUE varchar(255),
  F_NOTE varchar(255),
  F_CRDATE timestamp without time zone NOT NULL DEFAULT CURRENT_TIMESTAMP,
  F_CHDATE timestamp without time zone DEFAULT NULL,
  F_CRUSER varchar(30),
  F_CHUSER varchar(30),
  CONSTRAINT SYS_DCT_CST_KEY UNIQUE (DCT_ID, UNIT_ID, DCT_KEY)
);

CREATE TABLE SYS_KEYS (
  KEY_ID varchar(60) NOT NULL,
  OBJ_ID varchar(30) NOT NULL,
  KEY_TYPE varchar(30) NOT NULL,
  OBJ_DEPID varchar(30) NOT NULL,
  KEY_CNT numeric(30,0) NOT NULL,
  KEY_PINDEX1 varchar(30),
  KEY_PINDEX2 varchar(30),
  KEY_PINDEX3 varchar(30),
  KEY_PINDEX4 varchar(30),
  KEY_PINDEX5 varchar(30),
  KEY_PINDEX6 varchar(30),
  KEY_PINDEX7 varchar(30),
  KEY_PINDEX8 varchar(30),
  KEY_PINDEX9 varchar(30),
  KEY_PINDEX10 varchar(30),
  KEY_PINDEX11 varchar(30),
  KEY_PINDEX12 varchar(30),
  KEY_PINDEX13 varchar(30),
  KEY_PINDEX14 varchar(30),
  KEY_PINDEX15 varchar(30),
  KEY_PINDEX16 varchar(30),
  KEY_FINDEX1 varchar(30),
  KEY_FINDEX2 varchar(30),
  KEY_FINDEX3 varchar(30),
  KEY_FINDEX4 varchar(30),
  KEY_FINDEX5 varchar(30),
  KEY_FINDEX6 varchar(30),
  KEY_FINDEX7 varchar(30),
  KEY_FINDEX8 varchar(30),
  F_STAU numeric(30,0) NOT NULL DEFAULT 0,
  F_CRDATE timestamp without time zone NOT NULL DEFAULT CURRENT_TIMESTAMP,
  F_CHDATE timestamp without time zone NOT NULL DEFAULT CURRENT_TIMESTAMP,
  KEY_INDEX varchar(40),
  CONSTRAINT SYS_KEYS_KEY UNIQUE (KEY_ID)
);

CREATE TABLE SYS_INDEXS (
  INX_ID varchar(60) NOT NULL,
  INX_NAME varchar(255) NOT NULL,
  INX_TYPE char(1) NOT NULL,
  INX_CLT char(1) NOT NULL,
  OBJ_ID varchar(30) NOT NULL,
  INX_COLS varchar(255) NOT NULL,
  F_STAU numeric(30,0) NOT NULL DEFAULT 0,
  F_CRDATE timestamp without time zone NOT NULL DEFAULT CURRENT_TIMESTAMP,
  F_CHDATE timestamp without time zone NOT NULL DEFAULT CURRENT_TIMESTAMP,
  F_CRUSER varchar(30),
  F_CHUSER varchar(30),
  CONSTRAINT SYS_INDEXS_KEY UNIQUE (INX_ID)
);

CREATE TABLE SYS_OBJCOLS (
  OBJ_ID varchar(100) NOT NULL,
  COL_BASE varchar(30),
  COL_ID varchar(30) NOT NULL,
  COL_MC varchar(255) NOT NULL,
  COL_DES varchar(255),
  COL_TYPE varchar(2) NOT NULL,
  COL_APPTYPE1 char(1) NOT NULL,
  COL_APPTYPE2 char(2) NOT NULL,
  COL_CSTATT char(2) NOT NULL,
  COL_LEN int NOT NULL,
  COL_PREC int NOT NULL,
  COL_SCALE int NOT NULL,
  COL_ISKEY char(1) NOT NULL DEFAULT '0',
  COL_ISNULL char(1) NOT NULL DEFAULT '1',
  COL_VISIBLE char(1) NOT NULL DEFAULT '1',
  COL_EDITABLE char(1) NOT NULL DEFAULT '1',
  COL_EDIT char(4),
  COL_VIEW varchar(1000),
  COL_DEFAULT varchar(255),
  COL_DISP int NOT NULL,
  COL_ORDER char(1),
  COL_MUNIT char(1) NOT NULL DEFAULT '0',
  COL_USE char(1) NOT NULL DEFAULT '1',
  COL_VALUE char(1),
  COL_ISFKEY char(1) DEFAULT '0',
  COL_FOBJ varchar(30),
  COL_LANG char(1) DEFAULT '0',
  COL_ISCALC char(1) NOT NULL DEFAULT '0',
  COL_COLGS varchar(255),
  COL_CHECK char(1) DEFAULT '0',
  COL_REGEXREF varchar(30),
  COL_ALIAS varchar(30),
  COL_COLJY varchar(255),
  COL_CONT varchar(2),
  COL_EXT_ID varchar(30),
  COL_OPT varchar(1000),
  COL_REF varchar(30),
  F_LANG varchar(30),
  F_GUID varchar(30),
  F_STAU int NOT NULL DEFAULT 0,
  F_CRDATE timestamp without time zone,
  F_CHDATE timestamp without time zone,
  COL_FKEY varchar(30),
  SYS_ID varchar(30),
  COL_ADD_LOG varchar(20),
  F_CRUSER varchar(30),
  F_CHUSER varchar(30),
  CONSTRAINT SYS_OBJCOLS_KEY UNIQUE (OBJ_ID, COL_ID)
);

CREATE TABLE SYS_OBJECTS (
  OBJ_ID varchar(30) NOT NULL,
  OBJ_MC varchar(255) NOT NULL,
  OBJ_NAME varchar(255),
  OBJ_DES varchar(255),
  OBJ_TYPE char(1) NOT NULL,
  OBJ_APPTYPE char(3) NOT NULL,
  OBJ_BUILDNO char(4) NOT NULL DEFAULT '0000',
  SYS_ID varchar(30) NOT NULL,
  OBJ_PKEYS varchar(255),
  OBJ_LANG char(1) NOT NULL DEFAULT '0',
  OBJ_MUNIT char(1) NOT NULL DEFAULT '0',
  OBJ_CSTCOL char(1) NOT NULL DEFAULT '1',
  OBJ_UTRG varchar(30),
  OBJ_ITRG varchar(30),
  OBJ_DTRG varchar(30),
  OBJ_STRG varchar(30),
  OBJ_TEMP varchar(30),
  OBJ_MLID varchar(30),
  F_GUID varchar(30) DEFAULT '',
  OBJ_REF varchar(30),
  F_STAU numeric(30,0) NOT NULL DEFAULT 0,
  F_CRDATE timestamp without time zone,
  F_CHDATE timestamp without time zone,
  OBJ_UNIT char(1) NOT NULL DEFAULT '0',
  UNIT_ID varchar(30),
  TABLE_SPACE varchar(30) DEFAULT ' ',
  INDEX_SPACE varchar(30) DEFAULT ' ',
  F_CRUSER varchar(30),
  F_CHUSER varchar(30),
  OBJ_LANGINIT char(1),
  F_ENABLE varchar(1),
  OBJ_GROUP varchar(100),
  OBJ_TEMPTYPE varchar(1),
  CONSTRAINT SYS_OBJECTS_KEY UNIQUE (OBJ_ID)
);

CREATE TABLE SYS_OBJ_VAL (
  OBJ_ID varchar(30) NOT NULL,
  OBJ_KEY varchar(30) NOT NULL,
  OBJ_VALUE varchar(255),
  F_NOTE varchar(255),
  CONSTRAINT SYS_OBJ_VAL_KEY UNIQUE (OBJ_ID, OBJ_KEY)
);

CREATE TABLE SYS_FACTS (
  FCT_ID varchar(30) NOT NULL,
  FCT_MC varchar(255) NOT NULL,
  FCT_NAME varchar(255),
  FCT_DES varchar(255),
  FCT_TYPE char(2) DEFAULT '00',
  FCT_TMTYPE char(2) DEFAULT '00',
  OBJ_ID varchar(30) NOT NULL,
  BILL_BH_COL varchar(30),
  BLFL_BH_COL varchar(30),
  BLMX_BH_COL varchar(30),
  GRP_ID1 varchar(30),
  GRP_ID2 varchar(30),
  GRP_ID3 varchar(30),
  GRP_ID4 varchar(30),
  GRP_ID5 varchar(30),
  GRP_ID6 varchar(30),
  GRP_ID7 varchar(30),
  GRP_ID8 varchar(30),
  GRP_ID9 varchar(30),
  GRP_ID10 varchar(30),
  GRP_ID11 varchar(30),
  GRP_ID12 varchar(30),
  GRP_ID13 varchar(30),
  GRP_ID14 varchar(30),
  GRP_ID15 varchar(30),
  GRP_ID16 varchar(30),
  SYS_ID varchar(30) NOT NULL,
  F_STAU char(1) DEFAULT '0',
  F_CRDATE timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
  F_CHDATE timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
  CONSTRAINT SYS_FACTS_KEY UNIQUE (FCT_ID)
);

CREATE TABLE SYS_OPLOG (
  LOG_ID varchar(255) NOT NULL,
  F_GNBH varchar(200) NOT NULL,
  F_GNMC varchar(255),
  F_SJ varchar(255),
  F_STIME varchar(30),
  F_ETIME varchar(30),
  F_USER varchar(30),
  F_NAME varchar(30),
  F_CLIENT varchar(30),
  F_IP varchar(30),
  F_STR01 varchar(30),
  F_STR03 varchar(30),
  F_STR04 varchar(30),
  F_STR05 varchar(30),
  F_STR02 varchar(30),
  F_PRODUCT varchar(30),
  F_XTBH varchar(30),
  F_GNMK varchar(30),
  F_FORMS varchar(100),
  F_PREPAREVIEW varchar(50),
  F_SERVERID varchar(30),
  F_NODEID varchar(50),
  F_CONTENTVIEW varchar(50),
  F_MAC varchar(30),
  F_URL varchar(100),
  F_TYPE varchar(30),
  F_REQUEST bytea,
  F_RESPONSE bytea,
  CONSTRAINT SYS_OPLOG_INX UNIQUE (LOG_ID, F_GNBH)
);

CREATE TABLE BSCONF (
  F_VKEY varchar(20) NOT NULL DEFAULT ' ',
  F_VAL varchar(250) NOT NULL DEFAULT ' ',
  F_NOTE varchar(100),
  F_TYPE varchar(1),
  UNIT_ID varchar(30) NOT NULL DEFAULT ' ',
  F_SYS varchar(20),
  CONSTRAINT BSCONF_KEY UNIQUE (F_VKEY)
);

-- 插入基础数据



INSERT INTO SYS_OBJECTS 
(OBJ_ID, OBJ_MC, OBJ_NAME, OBJ_DES, OBJ_TYPE, OBJ_APPTYPE, OBJ_BUILDNO, SYS_ID, OBJ_PKEYS, OBJ_LANG, OBJ_MUNIT, OBJ_CSTCOL, OBJ_UTRG, OBJ_ITRG, OBJ_DTRG, OBJ_STRG, OBJ_TEMP, OBJ_MLID, F_GUID, OBJ_REF, F_STAU, F_CRDATE, F_CHDATE, OBJ_UNIT, UNIT_ID, TABLE_SPACE, INDEX_SPACE, F_CRUSER, F_CHUSER, OBJ_LANGINIT, F_ENABLE, OBJ_GROUP, OBJ_TEMPTYPE)
VALUES
  ('BSCONF', '系统配置表', NULL, ' ', 'T', 'SYS', '0000', 'ESPBASE', ' ', '0', '0', '0', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1, '2013-03-27 15:16:15', '2013-03-27 15:16:15', '0', NULL, ' ', ' ', NULL, NULL, NULL, NULL, NULL, NULL),
  ('SYS_DCT_CST', '自定义数据字典设置', NULL, ' ', 'T', 'SYS', '0000', 'ESPBASE', ' ', '0', '0', '0', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1, '2013-03-27 15:11:30', '2013-03-27 15:11:30', '0', NULL, ' ', ' ', NULL, NULL, NULL, NULL, NULL, NULL),
  ('SYS_DICTS', '数据字典', NULL, ' ', 'T', 'SYS', '0000', 'ESPBASE', ' ', '0', '0', '0', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1, '2013-03-27 15:11:30', '2013-03-27 15:11:30', '0', NULL, ' ', ' ', NULL, NULL, NULL, NULL, NULL, NULL),
  ('SYS_FACTS', '交易管理表', NULL, ' ', 'T', 'SYS', '0000', 'ESPBASE', ' ', '0', '0', '0', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1, '2013-03-27 15:11:34', '2013-03-27 15:11:34', '0', NULL, ' ', ' ', NULL, NULL, NULL, NULL, NULL, NULL),
  ('SYS_INDEXS', '索引信息', NULL, ' ', 'T', 'SYS', '0000', 'ESPBASE', ' ', '0', '0', '0', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1, '2013-03-27 15:11:28', '2016-10-25 14:49:35', '0', NULL, ' ', ' ', NULL, NULL, NULL, NULL, NULL, NULL),
  ('SYS_KEYS', '主键管理表', NULL, ' ', 'T', 'SYS', '0000', 'ESPBASE', ' ', '0', '0', '0', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1, '2013-03-27 15:11:29', '2013-03-27 15:11:29', '0', NULL, ' ', ' ', NULL, NULL, NULL, NULL, NULL, NULL),
  ('SYS_MDL_CTN', '模型内容定义', NULL, ' ', 'T', 'SYS', '0000', 'ESPBASE', ' ', '0', '0', '0', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1, '2013-03-27 15:11:38', '2013-03-27 15:11:38', '0', NULL, ' ', ' ', NULL, NULL, NULL, NULL, NULL, NULL);


 INSERT INTO SYS_OBJECTS 
(OBJ_ID, OBJ_MC, OBJ_NAME, OBJ_DES, OBJ_TYPE, OBJ_APPTYPE, OBJ_BUILDNO, SYS_ID, OBJ_PKEYS, OBJ_LANG, OBJ_MUNIT, OBJ_CSTCOL, OBJ_UTRG, OBJ_ITRG, OBJ_DTRG, OBJ_STRG, OBJ_TEMP, OBJ_MLID, F_GUID, OBJ_REF, F_STAU, F_CRDATE, F_CHDATE, OBJ_UNIT, UNIT_ID, TABLE_SPACE, INDEX_SPACE, F_CRUSER, F_CHUSER, OBJ_LANGINIT, F_ENABLE, OBJ_GROUP, OBJ_TEMPTYPE) 
VALUES
  ('SYS_MDL_VAL', '模型关键值定义', NULL, ' ', 'T', 'SYS', '0000', 'ESPBASE', ' ', '0', '0', '0', ' ', ' ', ' ', ' ', ' ', ' ', ' ', ' ', 1, '2013-03-27 15:11:38', '2013-03-27 15:11:38', '0', NULL, ' ', ' ', NULL, NULL, NULL, NULL, NULL, NULL),
  ('SYS_MODEL', '模型定义', NULL, ' ', 'T', 'SYS', '0000', 'ESPBASE', ' ', '0', '0', '0', ' ', ' ', ' ', ' ', ' ', ' ', ' ', ' ', 1, '2013-03-27 15:11:37', '2013-04-02 11:26:11', '0', NULL, ' ', ' ', NULL, NULL, NULL, NULL, NULL, NULL),
  ('SYS_OBJCOLS', '对象列管理表', NULL, ' ', 'T', 'SYS', '0000', 'ESPBASE', ' ', '0', '0', '0', ' ', ' ', ' ', ' ', ' ', ' ', ' ', ' ', 1, '2013-03-27 15:11:28', '2013-03-27 15:11:28', '0', NULL, ' ', ' ', NULL, NULL, NULL, NULL, NULL, NULL),
  ('SYS_OBJECTS', '对象管理表', NULL, ' ', 'T', 'SYS', '0000', 'ESPBASE', ' ', '0', '0', '0', ' ', ' ', ' ', ' ', ' ', ' ', ' ', ' ', 1, '2013-03-27 15:11:25', '2013-03-27 15:11:25', '0', NULL, ' ', ' ', NULL, NULL, NULL, NULL, NULL, NULL),
  ('SYS_OBJ_VAL', 'SYS_OBJ_VAL', NULL, ' ', 'T', 'SYS', '0000', 'ESPBASE', ' ', '0', '0', '0', ' ', ' ', ' ', ' ', ' ', ' ', ' ', ' ', 1, '2013-03-29 13:59:14', '2013-03-29 13:59:14', '0', NULL, ' ', ' ', NULL, NULL, NULL, NULL, NULL, NULL),
  ('SYS_OPLOG', '用户操作日志', ' ', ' ', 'T', 'SYS', '0000', 'ESPBASE', ' ', '0', '0', '0', ' ', ' ', ' ', ' ', ' ', ' ', ' ', ' ', 1, '2018-07-13 20:36:24', '2018-07-13 20:36:24', '0', ' ', ' ', ' ', NULL, NULL, NULL, NULL, NULL, NULL),
  ('SYS_RLGL', '字典关系表', NULL, ' ', 'T', 'SYS', '0000', 'ESPBASE', ' ', '0', '0', '0', ' ', ' ', ' ', ' ', ' ', ' ', ' ', ' ', 1, '2013-03-27 15:11:34', '2013-03-27 15:11:34', '0', NULL, ' ', ' ', NULL, NULL, NULL, NULL, NULL, NULL);


INSERT INTO SYS_OBJCOLS (OBJ_ID,COL_BASE,COL_ID,COL_MC,COL_DES,COL_TYPE,COL_APPTYPE1,COL_APPTYPE2,COL_CSTATT,COL_LEN,COL_PREC,COL_SCALE,COL_ISKEY,COL_ISNULL,COL_VISIBLE,COL_EDITABLE,COL_EDIT,COL_VIEW,COL_DEFAULT,COL_DISP,COL_ORDER,COL_MUNIT,COL_USE,COL_VALUE,COL_ISFKEY,COL_FOBJ,COL_LANG,COL_ISCALC,COL_COLGS,COL_CHECK,COL_REGEXREF,COL_ALIAS,COL_COLJY,COL_CONT,COL_EXT_ID,COL_OPT,COL_REF,F_LANG,F_GUID,F_STAU,F_CRDATE,F_CHDATE,COL_FKEY,SYS_ID,COL_ADD_LOG,F_CRUSER,F_CHUSER) VALUES
	 ('BSCONF',NULL,'F_NOTE','F_NOTE',NULL,'C','A','NH','DW',100,0,0,'0','1','1','1','1',NULL,NULL,3,NULL,'0','1',NULL,'0',NULL,'0','0',NULL,'0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,'2013-04-01 09:39:59','2013-04-01 09:39:59',NULL,NULL,NULL,NULL,NULL),
	 ('BSCONF',NULL,'F_SYS','F_SYS',NULL,'C','A','NH','DW',20,0,0,'0','1','1','1','1',NULL,NULL,6,NULL,'0','1',NULL,'0',NULL,'0','0',NULL,'0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,'2013-04-01 09:39:59','2013-04-01 09:39:59',NULL,NULL,NULL,NULL,NULL),
	 ('BSCONF',NULL,'F_TYPE','F_TYPE',NULL,'C','A','NH','DW',1,0,0,'0','1','1','1','1',NULL,NULL,4,NULL,'0','1',NULL,'0',NULL,'0','0',NULL,'0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,'2013-04-01 09:39:59','2013-04-01 09:39:59',NULL,NULL,NULL,NULL,NULL),
	 ('BSCONF',NULL,'F_VAL','F_VAL',NULL,'C','A','NH','DW',250,0,0,'0','0','1','1','1',NULL,NULL,2,NULL,'0','1',NULL,'0',NULL,'0','0',NULL,'0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,'2013-04-01 09:39:59','2013-04-01 09:39:59',NULL,NULL,NULL,NULL,NULL),
	 ('BSCONF',NULL,'F_VKEY','F_VKEY',NULL,'C','A','NH','DW',20,0,0,'1','0','1','1','1',NULL,NULL,1,NULL,'0','1',NULL,'0',NULL,'0','0',NULL,'0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,'2013-04-01 09:39:59','2013-04-01 09:39:59',NULL,NULL,NULL,NULL,NULL),
	 ('BSCONF',NULL,'UNIT_ID','UNIT_ID',NULL,'C','A','NH','DW',30,0,0,'0','0','1','1','1',NULL,NULL,5,NULL,'0','1',NULL,'0',NULL,'0','0',NULL,'0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,'2013-04-01 09:39:59','2013-04-01 09:39:59',NULL,NULL,NULL,NULL,NULL);
	 
INSERT INTO SYS_OBJCOLS (OBJ_ID,COL_BASE,COL_ID,COL_MC,COL_DES,COL_TYPE,COL_APPTYPE1,COL_APPTYPE2,COL_CSTATT,COL_LEN,COL_PREC,COL_SCALE,COL_ISKEY,COL_ISNULL,COL_VISIBLE,COL_EDITABLE,COL_EDIT,COL_VIEW,COL_DEFAULT,COL_DISP,COL_ORDER,COL_MUNIT,COL_USE,COL_VALUE,COL_ISFKEY,COL_FOBJ,COL_LANG,COL_ISCALC,COL_COLGS,COL_CHECK,COL_REGEXREF,COL_ALIAS,COL_COLJY,COL_CONT,COL_EXT_ID,COL_OPT,COL_REF,F_LANG,F_GUID,F_STAU,F_CRDATE,F_CHDATE,COL_FKEY,SYS_ID,COL_ADD_LOG,F_CRUSER,F_CHUSER) VALUES
	 ('SYS_DCT_CST',NULL,'DCT_ID','DCT_ID',NULL,'C','A','NH','DW',30,0,0,'1','0','1','1','1',NULL,NULL,1,NULL,'0','1',NULL,'0',NULL,'0','0',NULL,'0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,'2013-04-01 11:19:18','2013-04-01 11:19:18',NULL,NULL,NULL,NULL,NULL),
	 ('SYS_DCT_CST',NULL,'DCT_KEY','DCT_KEY',NULL,'C','A','NH','DW',30,0,0,'1','0','1','1','1',NULL,NULL,3,NULL,'0','1',NULL,'0',NULL,'0','0',NULL,'0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,'2013-04-01 11:19:18','2013-04-01 11:19:18',NULL,NULL,NULL,NULL,NULL),
	 ('SYS_DCT_CST',NULL,'DCT_VALUE','DCT_VALUE',NULL,'C','A','NH','DW',255,0,0,'0','1','1','1','1',NULL,NULL,4,NULL,'0','1',NULL,'0',NULL,'0','0',NULL,'0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,'2013-04-01 11:19:18','2013-04-01 11:19:18',NULL,NULL,NULL,NULL,NULL),
	 ('SYS_DCT_CST',NULL,'F_CHDATE','F_CHDATE',NULL,'T','A','NH','DW',11,0,6,'0','0','1','1','1',NULL,'sysdate',7,NULL,'0','1',NULL,'0',NULL,'0','0',NULL,'0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,'2013-04-01 11:19:18','2013-04-01 11:19:18',NULL,NULL,NULL,NULL,NULL),
	 ('SYS_DCT_CST',' ','F_CHUSER','修改人',' ','C','A','NH','DW',30,200,1,'0','1','1','1','1',' ',' ',9,'','0','1','','0',' ','0','0',' ','0',' ',' ',' ',' ',' ',' ',' ',' ',' ',1,'2023-04-06 11:17:02','2023-04-06 11:17:02',' ',' ',' ','9999',' '),
	 ('SYS_DCT_CST',NULL,'F_CRDATE','F_CRDATE',NULL,'T','A','NH','DW',11,0,6,'0','0','1','1','1',NULL,'sysdate',6,NULL,'0','1',NULL,'0',NULL,'0','0',NULL,'0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,'2013-04-01 11:19:18','2013-04-01 11:19:18',NULL,NULL,NULL,NULL,NULL),
	 ('SYS_DCT_CST',' ','F_CRUSER','创建人',' ','C','A','NH','DW',30,200,1,'0','1','1','1','1',' ',' ',7,'','0','1','','0',' ','0','0',' ','0',' ',' ',' ',' ',' ',' ',' ',' ',' ',1,'2023-04-06 11:17:02','2023-04-06 11:17:02',' ',' ',' ','9999',' '),
	 ('SYS_DCT_CST',NULL,'F_NOTE','F_NOTE',NULL,'C','A','NH','DW',255,0,0,'0','1','1','1','1',NULL,NULL,5,NULL,'0','1',NULL,'0',NULL,'0','0',NULL,'0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,'2013-04-01 11:19:18','2013-04-01 11:19:18',NULL,NULL,NULL,NULL,NULL),
	 ('SYS_DCT_CST',NULL,'UNIT_ID','UNIT_ID',NULL,'C','A','NH','DW',30,0,0,'1','0','1','1','1',NULL,NULL,2,NULL,'0','1',NULL,'0',NULL,'0','0',NULL,'0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,'2013-04-01 11:19:18','2013-04-01 11:19:18',NULL,NULL,NULL,NULL,NULL);


INSERT INTO SYS_OBJCOLS (OBJ_ID,COL_BASE,COL_ID,COL_MC,COL_DES,COL_TYPE,COL_APPTYPE1,COL_APPTYPE2,COL_CSTATT,COL_LEN,COL_PREC,COL_SCALE,COL_ISKEY,COL_ISNULL,COL_VISIBLE,COL_EDITABLE,COL_EDIT,COL_VIEW,COL_DEFAULT,COL_DISP,COL_ORDER,COL_MUNIT,COL_USE,COL_VALUE,COL_ISFKEY,COL_FOBJ,COL_LANG,COL_ISCALC,COL_COLGS,COL_CHECK,COL_REGEXREF,COL_ALIAS,COL_COLJY,COL_CONT,COL_EXT_ID,COL_OPT,COL_REF,F_LANG,F_GUID,F_STAU,F_CRDATE,F_CHDATE,COL_FKEY,SYS_ID,COL_ADD_LOG,F_CRUSER,F_CHUSER) VALUES
	 ('SYS_DICTS',NULL,'DCT_AFFIXDCT','DCT_AFFIXDCT',NULL,'C','A','NH','DW',70,0,0,'0','1','1','1','1',NULL,NULL,51,NULL,'0','1',NULL,'0',NULL,'0','0',NULL,'0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,'2013-04-01 11:20:42','2013-08-13 16:14:02',NULL,NULL,NULL,NULL,NULL),
	 ('SYS_DICTS',NULL,'DCT_BMCOLID','编号列',NULL,'C','A','NH','DW',30,80,0,'0','1','1','1','1',NULL,NULL,8,NULL,'0','1',NULL,'0',NULL,'0','0',NULL,'0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,'2013-04-01 11:20:42','2013-08-13 16:14:02',NULL,NULL,NULL,NULL,NULL),
	 ('SYS_DICTS',NULL,'DCT_BMSTRU','编码结构列',NULL,'C','A','NH','DW',30,100,0,'0','1','1','1','1',NULL,NULL,13,NULL,'0','1',NULL,'0',NULL,'0','0',NULL,'0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,'2013-04-01 11:20:42','2013-08-13 16:14:02',NULL,NULL,NULL,NULL,NULL),
	 ('SYS_DICTS',NULL,'DCT_BZCOLID','DCT_BZCOLID',NULL,'C','A','NH','DW',30,0,0,'0','1','1','1','1',NULL,NULL,9,NULL,'0','1',NULL,'0',NULL,'0','0',NULL,'0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,'2013-04-01 11:20:42','2013-08-13 16:14:02',NULL,NULL,NULL,NULL,NULL),
	 ('SYS_DICTS',NULL,'DCT_CHANGE','DCT_CHANGE',NULL,'C','A','NH','DW',1,0,0,'0','1','1','1','1',NULL,'1',25,NULL,'0','1',NULL,'0',NULL,'0','0',NULL,'0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,'2013-04-01 11:20:42','2013-08-13 16:14:02',NULL,NULL,NULL,NULL,NULL),
	 ('SYS_DICTS',NULL,'DCT_CREATE','DCT_CREATE',NULL,'C','A','NH','DW',1,0,0,'0','1','1','1','1',NULL,'1',24,NULL,'0','1',NULL,'0',NULL,'0','0',NULL,'0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,'2013-04-01 11:20:42','2013-08-13 16:14:02',NULL,NULL,NULL,NULL,NULL),
	 ('SYS_DICTS',NULL,'DCT_CST','DCT_CST',NULL,'C','A','NH','DW',2,0,0,'0','1','1','1','1',NULL,'00',29,NULL,'0','1',NULL,'0',NULL,'0','0',NULL,'0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,'2013-04-01 11:20:42','2013-08-13 16:14:02',NULL,NULL,NULL,NULL,NULL),
	 ('SYS_DICTS',NULL,'DCT_CTLONE','DCT_CTLONE',NULL,'C','A','NH','DW',1,0,0,'0','1','1','1','1',NULL,'0',22,NULL,'0','1',NULL,'0',NULL,'0','0',NULL,'0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,'2013-04-01 11:20:42','2013-08-13 16:14:02',NULL,NULL,NULL,NULL,NULL),
	 ('SYS_DICTS',NULL,'DCT_DES','字典描述',NULL,'C','A','NH','DW',255,180,0,'0','1','1','1','1',NULL,NULL,5,NULL,'0','1',NULL,'0',NULL,'0','0',NULL,'0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,'2013-04-01 11:20:42','2013-08-13 16:14:02',NULL,NULL,NULL,NULL,NULL),
	 ('SYS_DICTS',NULL,'DCT_EXTAUTO','DCT_EXTAUTO',NULL,'C','A','NH','DW',1,0,0,'0','1','1','1','1',NULL,'1',23,NULL,'0','1',NULL,'0',NULL,'0','0',NULL,'0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,'2013-04-01 11:20:42','2013-08-13 16:14:02',NULL,NULL,NULL,NULL,NULL);



INSERT INTO SYS_OBJCOLS (OBJ_ID,COL_BASE,COL_ID,COL_MC,COL_DES,COL_TYPE,COL_APPTYPE1,COL_APPTYPE2,COL_CSTATT,COL_LEN,COL_PREC,COL_SCALE,COL_ISKEY,COL_ISNULL,COL_VISIBLE,COL_EDITABLE,COL_EDIT,COL_VIEW,COL_DEFAULT,COL_DISP,COL_ORDER,COL_MUNIT,COL_USE,COL_VALUE,COL_ISFKEY,COL_FOBJ,COL_LANG,COL_ISCALC,COL_COLGS,COL_CHECK,COL_REGEXREF,COL_ALIAS,COL_COLJY,COL_CONT,COL_EXT_ID,COL_OPT,COL_REF,F_LANG,F_GUID,F_STAU,F_CRDATE,F_CHDATE,COL_FKEY,SYS_ID,COL_ADD_LOG,F_CRUSER,F_CHUSER) VALUES
	 ('SYS_DICTS',NULL,'DCT_FIXLEN','DCT_FIXLEN',NULL,'C','A','NH','DW',1,0,0,'0','1','1','1','1',NULL,'0',17,NULL,'0','1',NULL,'0',NULL,'0','0',NULL,'0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,'2013-04-01 11:20:42','2013-08-13 16:14:02',NULL,NULL,NULL,NULL,NULL),
	 ('SYS_DICTS',NULL,'DCT_FKEY1','DCT_FKEY1',NULL,'C','A','NH','DW',30,0,0,'0','1','1','1','1',NULL,NULL,30,NULL,'0','1',NULL,'0',NULL,'0','0',NULL,'0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,'2013-04-01 11:20:42','2013-08-13 16:14:02',NULL,NULL,NULL,NULL,NULL),
	 ('SYS_DICTS',NULL,'DCT_FKEY2','DCT_FKEY2',NULL,'C','A','NH','DW',30,0,0,'0','1','1','1','1',NULL,NULL,32,NULL,'0','1',NULL,'0',NULL,'0','0',NULL,'0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,'2013-04-01 11:20:42','2013-08-13 16:14:02',NULL,NULL,NULL,NULL,NULL),
	 ('SYS_DICTS',NULL,'DCT_FKEY3','DCT_FKEY3',NULL,'C','A','NH','DW',30,0,0,'0','1','1','1','1',NULL,NULL,34,NULL,'0','1',NULL,'0',NULL,'0','0',NULL,'0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,'2013-04-01 11:20:42','2013-08-13 16:14:02',NULL,NULL,NULL,NULL,NULL),
	 ('SYS_DICTS',NULL,'DCT_FKEY4','DCT_FKEY4',NULL,'C','A','NH','DW',30,0,0,'0','1','1','1','1',NULL,NULL,36,NULL,'0','1',NULL,'0',NULL,'0','0',NULL,'0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,'2013-04-01 11:20:42','2013-08-13 16:14:02',NULL,NULL,NULL,NULL,NULL),
	 ('SYS_DICTS',NULL,'DCT_FKEY5','DCT_FKEY5',NULL,'C','A','NH','DW',30,0,0,'0','1','1','1','1',NULL,NULL,38,NULL,'0','1',NULL,'0',NULL,'0','0',NULL,'0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,'2013-04-01 11:20:42','2013-08-13 16:14:02',NULL,NULL,NULL,NULL,NULL),
	 ('SYS_DICTS',NULL,'DCT_FKEY6','DCT_FKEY6',NULL,'C','A','NH','DW',30,0,0,'0','1','1','1','1',NULL,NULL,40,NULL,'0','1',NULL,'0',NULL,'0','0',NULL,'0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,'2013-04-01 11:20:42','2013-08-13 16:14:02',NULL,NULL,NULL,NULL,NULL),
	 ('SYS_DICTS',NULL,'DCT_FKEY7','DCT_FKEY7',NULL,'C','A','NH','DW',30,0,0,'0','1','1','1','1',NULL,NULL,42,NULL,'0','1',NULL,'0',NULL,'0','0',NULL,'0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,'2013-04-01 11:20:42','2013-08-13 16:14:02',NULL,NULL,NULL,NULL,NULL),
	 ('SYS_DICTS',NULL,'DCT_FKEY8','DCT_FKEY8',NULL,'C','A','NH','DW',30,0,0,'0','1','1','1','1',NULL,NULL,44,NULL,'0','1',NULL,'0',NULL,'0','0',NULL,'0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,'2013-04-01 11:20:42','2013-08-13 16:14:02',NULL,NULL,NULL,NULL,NULL),
	 ('SYS_DICTS',NULL,'DCT_FKEYDCT1','DCT_FKEYDCT1',NULL,'C','A','NH','DW',30,0,0,'0','1','1','1','1',NULL,NULL,31,NULL,'0','1',NULL,'0',NULL,'0','0',NULL,'0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,'2013-04-01 11:20:42','2013-08-13 16:14:02',NULL,NULL,NULL,NULL,NULL);
INSERT INTO SYS_OBJCOLS (OBJ_ID,COL_BASE,COL_ID,COL_MC,COL_DES,COL_TYPE,COL_APPTYPE1,COL_APPTYPE2,COL_CSTATT,COL_LEN,COL_PREC,COL_SCALE,COL_ISKEY,COL_ISNULL,COL_VISIBLE,COL_EDITABLE,COL_EDIT,COL_VIEW,COL_DEFAULT,COL_DISP,COL_ORDER,COL_MUNIT,COL_USE,COL_VALUE,COL_ISFKEY,COL_FOBJ,COL_LANG,COL_ISCALC,COL_COLGS,COL_CHECK,COL_REGEXREF,COL_ALIAS,COL_COLJY,COL_CONT,COL_EXT_ID,COL_OPT,COL_REF,F_LANG,F_GUID,F_STAU,F_CRDATE,F_CHDATE,COL_FKEY,SYS_ID,COL_ADD_LOG,F_CRUSER,F_CHUSER) VALUES
	 ('SYS_DICTS',NULL,'DCT_FKEYDCT2','DCT_FKEYDCT2',NULL,'C','A','NH','DW',30,0,0,'0','1','1','1','1',NULL,NULL,33,NULL,'0','1',NULL,'0',NULL,'0','0',NULL,'0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,'2013-04-01 11:20:42','2013-08-13 16:14:02',NULL,NULL,NULL,NULL,NULL),
	 ('SYS_DICTS',NULL,'DCT_FKEYDCT3','DCT_FKEYDCT3',NULL,'C','A','NH','DW',30,0,0,'0','1','1','1','1',NULL,NULL,35,NULL,'0','1',NULL,'0',NULL,'0','0',NULL,'0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,'2013-04-01 11:20:42','2013-08-13 16:14:02',NULL,NULL,NULL,NULL,NULL),
	 ('SYS_DICTS',NULL,'DCT_FKEYDCT4','DCT_FKEYDCT4',NULL,'C','A','NH','DW',30,0,0,'0','1','1','1','1',NULL,NULL,37,NULL,'0','1',NULL,'0',NULL,'0','0',NULL,'0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,'2013-04-01 11:20:42','2013-08-13 16:14:02',NULL,NULL,NULL,NULL,NULL),
	 ('SYS_DICTS',NULL,'DCT_FKEYDCT5','DCT_FKEYDCT5',NULL,'C','A','NH','DW',30,0,0,'0','1','1','1','1',NULL,NULL,39,NULL,'0','1',NULL,'0',NULL,'0','0',NULL,'0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,'2013-04-01 11:20:42','2013-08-13 16:14:02',NULL,NULL,NULL,NULL,NULL),
	 ('SYS_DICTS',NULL,'DCT_FKEYDCT6','DCT_FKEYDCT6',NULL,'C','A','NH','DW',30,0,0,'0','1','1','1','1',NULL,NULL,41,NULL,'0','1',NULL,'0',NULL,'0','0',NULL,'0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,'2013-04-01 11:20:42','2013-08-13 16:14:02',NULL,NULL,NULL,NULL,NULL),
	 ('SYS_DICTS',NULL,'DCT_FKEYDCT7','DCT_FKEYDCT7',NULL,'C','A','NH','DW',30,0,0,'0','1','1','1','1',NULL,NULL,43,NULL,'0','1',NULL,'0',NULL,'0','0',NULL,'0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,'2013-04-01 11:20:42','2013-08-13 16:14:02',NULL,NULL,NULL,NULL,NULL),
	 ('SYS_DICTS',NULL,'DCT_FKEYDCT8','DCT_FKEYDCT8',NULL,'C','A','NH','DW',30,0,0,'0','1','1','1','1',NULL,NULL,45,NULL,'0','1',NULL,'0',NULL,'0','0',NULL,'0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,'2013-04-01 11:20:42','2013-08-13 16:14:02',NULL,NULL,NULL,NULL,NULL),
	 ('SYS_DICTS',NULL,'DCT_ID','字典编号',NULL,'C','A','NH','DW',30,80,0,'1','0','1','1','1',NULL,NULL,1,NULL,'0','1',NULL,'0',NULL,'0','0',NULL,'0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,'2013-04-01 11:20:42','2013-08-13 16:14:02',NULL,NULL,NULL,NULL,NULL),
	 ('SYS_DICTS',NULL,'DCT_JSCOLID','级数列',NULL,'C','A','NH','DW',30,80,0,'0','1','1','1','1',NULL,NULL,11,NULL,'0','1',NULL,'0',NULL,'0','0',NULL,'0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,'2013-04-01 11:20:42','2013-08-13 16:14:02',NULL,NULL,NULL,NULL,NULL),
	 ('SYS_DICTS',NULL,'DCT_KPI','DCT_KPI',NULL,'C','A','NH','DW',1,0,0,'0','1','1','1','1',NULL,'0',19,NULL,'0','1',NULL,'0',NULL,'0','0',NULL,'0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,'2013-04-01 11:20:42','2013-08-13 16:14:02',NULL,NULL,NULL,NULL,NULL);
INSERT INTO SYS_OBJCOLS (OBJ_ID,COL_BASE,COL_ID,COL_MC,COL_DES,COL_TYPE,COL_APPTYPE1,COL_APPTYPE2,COL_CSTATT,COL_LEN,COL_PREC,COL_SCALE,COL_ISKEY,COL_ISNULL,COL_VISIBLE,COL_EDITABLE,COL_EDIT,COL_VIEW,COL_DEFAULT,COL_DISP,COL_ORDER,COL_MUNIT,COL_USE,COL_VALUE,COL_ISFKEY,COL_FOBJ,COL_LANG,COL_ISCALC,COL_COLGS,COL_CHECK,COL_REGEXREF,COL_ALIAS,COL_COLJY,COL_CONT,COL_EXT_ID,COL_OPT,COL_REF,F_LANG,F_GUID,F_STAU,F_CRDATE,F_CHDATE,COL_FKEY,SYS_ID,COL_ADD_LOG,F_CRUSER,F_CHUSER) VALUES
	 ('SYS_DICTS',NULL,'DCT_KZCOLID','DCT_KZCOLID',NULL,'C','A','NH','DW',30,0,0,'0','1','1','1','1',NULL,NULL,15,NULL,'0','1',NULL,'0',NULL,'0','0',NULL,'0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,'2013-04-01 11:20:42','2013-08-13 16:14:02',NULL,NULL,NULL,NULL,NULL),
	 ('SYS_DICTS',NULL,'DCT_MC','字典名称',NULL,'C','A','NH','DW',255,120,0,'0','0','1','1','1',NULL,NULL,3,NULL,'0','1',NULL,'0',NULL,'0','0',NULL,'0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,'2013-04-01 11:20:42','2013-08-13 16:14:02',NULL,NULL,NULL,NULL,NULL),
	 ('SYS_DICTS',NULL,'DCT_MCCOLID','名称列',NULL,'C','A','NH','DW',30,80,0,'0','1','1','1','1',NULL,NULL,10,NULL,'0','1',NULL,'0',NULL,'0','0',NULL,'0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,'2013-04-01 11:20:42','2013-08-13 16:14:02',NULL,NULL,NULL,NULL,NULL),
	 ('SYS_DICTS',NULL,'DCT_MUNIT','DCT_MUNIT',NULL,'C','A','NH','DW',1,0,0,'0','1','1','1','1',NULL,'0',21,NULL,'0','1',NULL,'0',NULL,'0','0',NULL,'0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,'2013-04-01 11:20:42','2013-08-13 16:14:02',NULL,NULL,NULL,NULL,NULL),
	 ('SYS_DICTS',NULL,'DCT_MXCOLID','明细列',NULL,'C','A','NH','DW',30,80,0,'0','1','1','1','1',NULL,NULL,12,NULL,'0','1',NULL,'0',NULL,'0','0',NULL,'0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,'2013-04-01 11:20:42','2013-08-13 16:14:02',NULL,NULL,NULL,NULL,NULL),
	 ('SYS_DICTS',NULL,'DCT_NAME','字典名称',NULL,'C','A','NH','DW',255,150,0,'0','1','1','1','1',NULL,NULL,4,NULL,'0','1',NULL,'0',NULL,'0','0',NULL,'0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,'2013-04-01 11:20:42','2013-08-13 16:14:02',NULL,NULL,NULL,NULL,NULL),
	 ('SYS_DICTS',NULL,'DCT_NOTUSE','DCT_NOTUSE',NULL,'C','A','NH','DW',1,0,0,'0','1','1','1','1',NULL,'1',26,NULL,'0','1',NULL,'0',NULL,'0','0',NULL,'0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,'2013-04-01 11:20:42','2013-08-13 16:14:02',NULL,NULL,NULL,NULL,NULL),
	 ('SYS_DICTS',NULL,'DCT_PTCOLID','DCT_PTCOLID',NULL,'C','A','NH','DW',30,0,0,'0','1','1','1','1',NULL,NULL,14,NULL,'0','1',NULL,'0',NULL,'0','0',NULL,'0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,'2013-04-01 11:20:42','2013-08-13 16:14:02',NULL,NULL,NULL,NULL,NULL),
	 ('SYS_DICTS',NULL,'DCT_QXOBJID','DCT_QXOBJID',NULL,'C','A','NH','DW',30,0,0,'0','1','1','1','1',NULL,NULL,6,NULL,'0','1',NULL,'0',NULL,'0','0',NULL,'0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,'2013-04-01 11:20:42','2013-08-13 16:14:02',NULL,NULL,NULL,NULL,NULL),
	 ('SYS_DICTS',NULL,'DCT_QXSTAT','DCT_QXSTAT',NULL,'C','A','NH','DW',1,0,0,'0','1','1','1','1',NULL,'0',50,NULL,'0','1',NULL,'0',NULL,'0','0',NULL,'0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,'2013-04-01 11:20:42','2013-08-13 16:14:02',NULL,NULL,NULL,NULL,NULL);
INSERT INTO SYS_OBJCOLS (OBJ_ID,COL_BASE,COL_ID,COL_MC,COL_DES,COL_TYPE,COL_APPTYPE1,COL_APPTYPE2,COL_CSTATT,COL_LEN,COL_PREC,COL_SCALE,COL_ISKEY,COL_ISNULL,COL_VISIBLE,COL_EDITABLE,COL_EDIT,COL_VIEW,COL_DEFAULT,COL_DISP,COL_ORDER,COL_MUNIT,COL_USE,COL_VALUE,COL_ISFKEY,COL_FOBJ,COL_LANG,COL_ISCALC,COL_COLGS,COL_CHECK,COL_REGEXREF,COL_ALIAS,COL_COLJY,COL_CONT,COL_EXT_ID,COL_OPT,COL_REF,F_LANG,F_GUID,F_STAU,F_CRDATE,F_CHDATE,COL_FKEY,SYS_ID,COL_ADD_LOG,F_CRUSER,F_CHUSER) VALUES
	 ('SYS_DICTS',NULL,'DCT_SELECT','DCT_SELECT',NULL,'C','A','NH','DW',1,0,0,'0','1','1','1','1',NULL,'0',20,NULL,'0','1',NULL,'0',NULL,'0','0',NULL,'0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,'2013-04-01 11:20:42','2013-08-13 16:14:02',NULL,NULL,NULL,NULL,NULL),
	 ('SYS_DICTS',NULL,'DCT_STEP','DCT_STEP',NULL,'I','A','NH','DW',22,0,0,'0','1','1','1','1',NULL,'1',18,NULL,'0','1',NULL,'0',NULL,'0','0',NULL,'0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,'2013-04-01 11:20:42','2013-08-13 16:14:02',NULL,NULL,NULL,NULL,NULL),
	 ('SYS_DICTS',NULL,'DCT_SUBJECT','DCT_SUBJECT',NULL,'C','A','NH','DW',30,0,0,'0','1','1','1','1',NULL,NULL,27,NULL,'0','1',NULL,'0',NULL,'0','0',NULL,'0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,'2013-04-01 11:20:42','2013-08-13 16:14:02',NULL,NULL,NULL,NULL,NULL),
	 ('SYS_DICTS',NULL,'DCT_SYNCDATE','DCT_SYNCDATE',NULL,'T','A','NH','DW',11,0,6,'0','1','1','1','1',NULL,NULL,46,NULL,'0','1',NULL,'0',NULL,'0','0',NULL,'0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,'2013-04-01 11:20:42','2013-08-13 16:14:02',NULL,NULL,NULL,NULL,NULL),
	 ('SYS_DICTS',NULL,'DCT_TYPE','DCT_TYPE',NULL,'C','A','NH','DW',2,0,0,'0','0','1','1','1',NULL,NULL,7,NULL,'0','1',NULL,'0',NULL,'0','0',NULL,'0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,'2013-04-01 11:20:42','2013-08-13 16:14:02',NULL,NULL,NULL,NULL,NULL),
	 ('SYS_DICTS',NULL,'DCT_UNIT','DCT_UNIT',NULL,'C','A','NH','DW',30,0,0,'0','1','1','1','1',NULL,NULL,28,NULL,'0','1',NULL,'0',NULL,'0','0',NULL,'0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,'2013-04-01 11:20:42','2013-08-13 16:14:02',NULL,NULL,NULL,NULL,NULL),
	 ('SYS_DICTS','F_CHDATE','F_CHDATE','修改时间','维表、关系表、事实表 都需要的列','T','A','NH','DW',19,100,1,'0','1','1','1','1',' ',' ',7,'','0','1','','0',' ','0','0',' ','0',' ',' ',' ',' ',' ',' ',' ',' ',' ',1,'2021-01-09 12:50:53','2021-09-26 14:36:49',' ',' ',' ',NULL,NULL),
	 ('SYS_DICTS',' ','F_CHUSER','修改人',' ','C','A','NH','DW',30,100,1,'0','1','1','1','1',' ',' ',11,'','0','1','','1','BSUSER','0','0',' ','0',' ',' ',' ',' ',' ',' ',' ',' ',' ',1,'2021-09-22 15:35:28','2021-09-23 15:41:46',' ',NULL,NULL,NULL,NULL),
	 ('SYS_DICTS','F_CRDATE','F_CRDATE','创建时间','维表、关系表、事实表 都需要的列','T','A','NH','DW',19,100,1,'0','1','1','1','1',' ',' ',7,'','0','1','','0',' ','0','0',' ','0',' ',' ',' ',' ',' ',' ',' ',' ',' ',1,'2021-01-09 12:50:53','2021-09-26 14:36:49',' ',' ',' ',NULL,NULL),
	 ('SYS_DICTS',' ','F_CRUSER','创建人',' ','C','A','NH','DW',30,100,1,'0','1','1','1','1',' ',' ',11,'','0','1','','1','BSUSER','0','0',' ','0',' ',' ',' ',' ',' ',' ',' ',' ',' ',1,'2021-09-22 15:35:28','2021-09-23 15:41:46',' ',NULL,NULL,NULL,NULL);
INSERT INTO SYS_OBJCOLS (OBJ_ID,COL_BASE,COL_ID,COL_MC,COL_DES,COL_TYPE,COL_APPTYPE1,COL_APPTYPE2,COL_CSTATT,COL_LEN,COL_PREC,COL_SCALE,COL_ISKEY,COL_ISNULL,COL_VISIBLE,COL_EDITABLE,COL_EDIT,COL_VIEW,COL_DEFAULT,COL_DISP,COL_ORDER,COL_MUNIT,COL_USE,COL_VALUE,COL_ISFKEY,COL_FOBJ,COL_LANG,COL_ISCALC,COL_COLGS,COL_CHECK,COL_REGEXREF,COL_ALIAS,COL_COLJY,COL_CONT,COL_EXT_ID,COL_OPT,COL_REF,F_LANG,F_GUID,F_STAU,F_CRDATE,F_CHDATE,COL_FKEY,SYS_ID,COL_ADD_LOG,F_CRUSER,F_CHUSER) VALUES
	 ('SYS_DICTS','F_DELETED','F_DELETED','是否删除',' ','C','A','NH','DW',1,75,0,'0','1','1','1','4','0:未删除;1:已删除','0',0,'','0','1','','0',' ','0','0',' ','0',' ',' ',' ',' ',' ',' ',' ',' ',' ',0,'2021-09-16 14:10:47','2021-09-16 14:10:47',' ',NULL,NULL,NULL,NULL),
	 ('SYS_DICTS',' ','F_DICTSFKEY','F_DICTSFKEY',' ','C','A','NH','DW',30,200,1,'0','1','1','1','1',' ','0',57,'','0','1','','0',' ','0','0',' ','0',' ',' ',' ',' ',' ',' ',' ',' ',' ',1,'2022-04-19 10:23:42','2022-04-19 10:39:13',' ',' ',' ','wangye',' '),
	 ('SYS_DICTS',' ','F_ENABLE ','是否启用',' ','C','A','NH','DW',1,100,1,'0','1','1','1','1',' ',' ',22,'','0','1','','0',' ','0','0',' ','0',' ',' ',' ',' ',' ',' ',' ',' ',' ',1,'2021-09-22 15:35:28','2021-09-22 15:35:28',' ',NULL,NULL,NULL,NULL),
	 ('SYS_DICTS',NULL,'F_GUID','F_GUID',NULL,'C','A','NH','DW',36,0,0,'0','1','1','1','1',NULL,NULL,52,NULL,'0','1',NULL,'0',NULL,'0','0',NULL,'0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,'2013-04-01 11:20:42','2013-08-13 16:14:02',NULL,NULL,NULL,NULL,NULL),
	 ('SYS_DICTS',NULL,'F_STAU','F_STAU',NULL,'I','A','NH','DW',22,0,0,'0','0','1','1','1',NULL,'0',47,NULL,'0','1',NULL,'0',NULL,'0','0',NULL,'0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,'2013-04-01 11:20:42','2013-08-13 16:14:02',NULL,NULL,NULL,NULL,NULL),
	 ('SYS_DICTS',NULL,'OBJ_ID','字典对象',NULL,'C','A','NH','DW',30,80,0,'0','0','1','1','1',NULL,NULL,2,NULL,'0','1',NULL,'0',NULL,'0','0',NULL,'0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,'2013-04-01 11:20:42','2013-08-13 16:14:02',NULL,NULL,NULL,NULL,NULL),
	 ('SYS_DICTS',' ','SYS_ID','系统ID',' ','C','A','NH','DW',30,80,0,'0','0','1','1','1',' ',' ',16,'','0','1','','1','SYS_LIST','0','0',' ','0',' ',' ',' ',' ',' ',' ',' ',' ',' ',0,'2013-04-01 11:20:42','2016-10-19 21:31:40',' ',NULL,NULL,NULL,NULL),
	 ('SYS_FACTS',' ','BILL_BH_COL','BILL_BH_COL',' ','C','A','NH','DW',30,100,0,'0','1','1','1','1',' ',' ',8,'','0','1','','0',' ','0','0',' ','0',' ',' ',' ',' ',' ',' ',' ',' ',' ',0,'2013-04-01 11:23:19','2014-05-27 00:48:22',' ',NULL,NULL,NULL,NULL),
	 ('SYS_FACTS',' ','BLFL_BH_COL','BLFL_BH_COL',' ','C','A','NH','DW',30,100,0,'0','1','1','1','1',' ',' ',9,'','0','1','','0',' ','0','0',' ','0',' ',' ',' ',' ',' ',' ',' ',' ',' ',0,'2013-04-01 11:23:19','2014-05-27 00:48:22',' ',NULL,NULL,NULL,NULL),
	 ('SYS_FACTS',' ','BLMX_BH_COL','BLMX_BH_COL',' ','C','A','NH','DW',30,100,0,'0','1','1','1','1',' ',' ',10,'','0','1','','0',' ','0','0',' ','0',' ',' ',' ',' ',' ',' ',' ',' ',' ',0,'2013-04-01 11:23:19','2014-05-27 00:48:22',' ',NULL,NULL,NULL,NULL);
INSERT INTO SYS_OBJCOLS (OBJ_ID,COL_BASE,COL_ID,COL_MC,COL_DES,COL_TYPE,COL_APPTYPE1,COL_APPTYPE2,COL_CSTATT,COL_LEN,COL_PREC,COL_SCALE,COL_ISKEY,COL_ISNULL,COL_VISIBLE,COL_EDITABLE,COL_EDIT,COL_VIEW,COL_DEFAULT,COL_DISP,COL_ORDER,COL_MUNIT,COL_USE,COL_VALUE,COL_ISFKEY,COL_FOBJ,COL_LANG,COL_ISCALC,COL_COLGS,COL_CHECK,COL_REGEXREF,COL_ALIAS,COL_COLJY,COL_CONT,COL_EXT_ID,COL_OPT,COL_REF,F_LANG,F_GUID,F_STAU,F_CRDATE,F_CHDATE,COL_FKEY,SYS_ID,COL_ADD_LOG,F_CRUSER,F_CHUSER) VALUES
	 ('SYS_FACTS',' ','FCT_DES','对象描述',' ','C','A','NH','DW',255,100,0,'0','1','1','1','1',' ',' ',3,'','0','1','','0',' ','0','0',' ','0',' ',' ',' ',' ',' ',' ',' ',' ',' ',0,'2013-04-01 11:23:19','2014-05-27 00:48:22',' ',NULL,NULL,NULL,NULL),
	 ('SYS_FACTS',' ','FCT_ID','对象标识',' ','C','A','NH','DW',30,100,0,'1','0','1','1','1',' ',' ',1,'','0','1','','0',' ','0','0',' ','0',' ',' ',' ',' ',' ',' ',' ',' ',' ',0,'2013-04-01 11:23:19','2014-05-27 00:48:22',' ',NULL,NULL,NULL,NULL),
	 ('SYS_FACTS',' ','FCT_MC','对象名称',' ','C','A','NH','DW',255,100,0,'0','0','1','1','1',' ',' ',2,'','0','1','','0',' ','0','0',' ','0',' ',' ',' ',' ',' ',' ',' ',' ',' ',0,'2013-04-01 11:23:19','2014-05-27 00:48:22',' ',NULL,NULL,NULL,NULL),
	 ('SYS_FACTS',' ','FCT_NAME','FCT_NAME',' ','C','A','NH','DW',255,100,0,'0','1','1','1','1',' ',' ',3,'','0','1','','0',' ','0','0',' ','0',' ',' ',' ',' ',' ',' ',' ',' ',' ',0,'2013-04-01 11:23:19','2014-05-27 00:48:22',' ',NULL,NULL,NULL,NULL),
	 ('SYS_FACTS',' ','FCT_TMTYPE','FCT_TMTYPE',' ','C','A','NH','DW',2,100,0,'0','1','1','1','1',' ','00',6,'','0','1','','0',' ','0','0',' ','0',' ',' ',' ',' ',' ',' ',' ',' ',' ',0,'2013-04-01 11:23:19','2014-05-27 00:48:22',' ',NULL,NULL,NULL,NULL),
	 ('SYS_FACTS',' ','FCT_TYPE','对象类型',' ','C','A','NH','DW',2,100,0,'0','1','1','1','1',' ','00',4,'','0','1','','0',' ','0','0',' ','0',' ',' ',' ',' ',' ',' ',' ',' ',' ',0,'2013-04-01 11:23:19','2014-05-27 00:48:22',' ',NULL,NULL,NULL,NULL),
	 ('SYS_FACTS',NULL,'F_CHDATE','F_CHDATE',NULL,'T','A','NH','DW',11,0,6,'0','1','1','1','1',NULL,'sysdate',30,NULL,'0','1',NULL,'0',NULL,'0','0',NULL,'0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,'2013-04-01 11:23:19','2013-04-01 11:23:19',NULL,NULL,NULL,NULL,NULL),
	 ('SYS_FACTS',NULL,'F_CRDATE','F_CRDATE',NULL,'T','A','NH','DW',11,0,6,'0','1','1','1','1',NULL,'sysdate',29,NULL,'0','1',NULL,'0',NULL,'0','0',NULL,'0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,'2013-04-01 11:23:19','2013-04-01 11:23:19',NULL,NULL,NULL,NULL,NULL),
	 ('SYS_FACTS',NULL,'F_STAU','F_STAU',NULL,'C','A','NH','DW',1,0,0,'0','1','1','1','1',NULL,'0',28,NULL,'0','1',NULL,'0',NULL,'0','0',NULL,'0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,'2013-04-01 11:23:19','2013-04-01 11:23:19',NULL,NULL,NULL,NULL,NULL),
	 ('SYS_FACTS',' ','GRP_ID1','GRP_ID1',' ','C','A','NH','DW',30,100,0,'0','1','1','1','1',' ',' ',11,'','0','1','','0',' ','0','0',' ','0',' ',' ',' ',' ',' ',' ',' ',' ',' ',0,'2013-04-01 11:23:19','2014-05-27 00:48:22',' ',NULL,NULL,NULL,NULL);
INSERT INTO SYS_OBJCOLS (OBJ_ID,COL_BASE,COL_ID,COL_MC,COL_DES,COL_TYPE,COL_APPTYPE1,COL_APPTYPE2,COL_CSTATT,COL_LEN,COL_PREC,COL_SCALE,COL_ISKEY,COL_ISNULL,COL_VISIBLE,COL_EDITABLE,COL_EDIT,COL_VIEW,COL_DEFAULT,COL_DISP,COL_ORDER,COL_MUNIT,COL_USE,COL_VALUE,COL_ISFKEY,COL_FOBJ,COL_LANG,COL_ISCALC,COL_COLGS,COL_CHECK,COL_REGEXREF,COL_ALIAS,COL_COLJY,COL_CONT,COL_EXT_ID,COL_OPT,COL_REF,F_LANG,F_GUID,F_STAU,F_CRDATE,F_CHDATE,COL_FKEY,SYS_ID,COL_ADD_LOG,F_CRUSER,F_CHUSER) VALUES
	 ('SYS_FACTS',NULL,'GRP_ID10','GRP_ID10',NULL,'C','A','NH','DW',30,0,0,'0','1','1','1','1',NULL,NULL,20,NULL,'0','1',NULL,'0',NULL,'0','0',NULL,'0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,'2013-04-01 11:23:19','2013-04-01 11:23:19',NULL,NULL,NULL,NULL,NULL),
	 ('SYS_FACTS',NULL,'GRP_ID11','GRP_ID11',NULL,'C','A','NH','DW',30,0,0,'0','1','1','1','1',NULL,NULL,21,NULL,'0','1',NULL,'0',NULL,'0','0',NULL,'0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,'2013-04-01 11:23:19','2013-04-01 11:23:19',NULL,NULL,NULL,NULL,NULL),
	 ('SYS_FACTS',NULL,'GRP_ID12','GRP_ID12',NULL,'C','A','NH','DW',30,0,0,'0','1','1','1','1',NULL,NULL,22,NULL,'0','1',NULL,'0',NULL,'0','0',NULL,'0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,'2013-04-01 11:23:19','2013-04-01 11:23:19',NULL,NULL,NULL,NULL,NULL),
	 ('SYS_FACTS',NULL,'GRP_ID13','GRP_ID13',NULL,'C','A','NH','DW',30,0,0,'0','1','1','1','1',NULL,NULL,23,NULL,'0','1',NULL,'0',NULL,'0','0',NULL,'0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,'2013-04-01 11:23:19','2013-04-01 11:23:19',NULL,NULL,NULL,NULL,NULL),
	 ('SYS_FACTS',NULL,'GRP_ID14','GRP_ID14',NULL,'C','A','NH','DW',30,0,0,'0','1','1','1','1',NULL,NULL,24,NULL,'0','1',NULL,'0',NULL,'0','0',NULL,'0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,'2013-04-01 11:23:19','2013-04-01 11:23:19',NULL,NULL,NULL,NULL,NULL),
	 ('SYS_FACTS',NULL,'GRP_ID15','GRP_ID15',NULL,'C','A','NH','DW',30,0,0,'0','1','1','1','1',NULL,NULL,25,NULL,'0','1',NULL,'0',NULL,'0','0',NULL,'0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,'2013-04-01 11:23:19','2013-04-01 11:23:19',NULL,NULL,NULL,NULL,NULL),
	 ('SYS_FACTS',NULL,'GRP_ID16','GRP_ID16',NULL,'C','A','NH','DW',30,0,0,'0','1','1','1','1',NULL,NULL,26,NULL,'0','1',NULL,'0',NULL,'0','0',NULL,'0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,'2013-04-01 11:23:19','2013-04-01 11:23:19',NULL,NULL,NULL,NULL,NULL),
	 ('SYS_FACTS',' ','GRP_ID2','GRP_ID2',' ','C','A','NH','DW',30,100,0,'0','1','1','1','1',' ',' ',12,'','0','1','','0',' ','0','0',' ','0',' ',' ',' ',' ',' ',' ',' ',' ',' ',0,'2013-04-01 11:23:19','2014-05-27 00:48:22',' ',NULL,NULL,NULL,NULL),
	 ('SYS_FACTS',NULL,'GRP_ID3','GRP_ID3',NULL,'C','A','NH','DW',30,0,0,'0','1','1','1','1',NULL,NULL,13,NULL,'0','1',NULL,'0',NULL,'0','0',NULL,'0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,'2013-04-01 11:23:19','2013-04-01 11:23:19',NULL,NULL,NULL,NULL,NULL),
	 ('SYS_FACTS',NULL,'GRP_ID4','GRP_ID4',NULL,'C','A','NH','DW',30,0,0,'0','1','1','1','1',NULL,NULL,14,NULL,'0','1',NULL,'0',NULL,'0','0',NULL,'0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,'2013-04-01 11:23:19','2013-04-01 11:23:19',NULL,NULL,NULL,NULL,NULL);
INSERT INTO SYS_OBJCOLS (OBJ_ID,COL_BASE,COL_ID,COL_MC,COL_DES,COL_TYPE,COL_APPTYPE1,COL_APPTYPE2,COL_CSTATT,COL_LEN,COL_PREC,COL_SCALE,COL_ISKEY,COL_ISNULL,COL_VISIBLE,COL_EDITABLE,COL_EDIT,COL_VIEW,COL_DEFAULT,COL_DISP,COL_ORDER,COL_MUNIT,COL_USE,COL_VALUE,COL_ISFKEY,COL_FOBJ,COL_LANG,COL_ISCALC,COL_COLGS,COL_CHECK,COL_REGEXREF,COL_ALIAS,COL_COLJY,COL_CONT,COL_EXT_ID,COL_OPT,COL_REF,F_LANG,F_GUID,F_STAU,F_CRDATE,F_CHDATE,COL_FKEY,SYS_ID,COL_ADD_LOG,F_CRUSER,F_CHUSER) VALUES
	 ('SYS_FACTS',NULL,'GRP_ID5','GRP_ID5',NULL,'C','A','NH','DW',30,0,0,'0','1','1','1','1',NULL,NULL,15,NULL,'0','1',NULL,'0',NULL,'0','0',NULL,'0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,'2013-04-01 11:23:19','2013-04-01 11:23:19',NULL,NULL,NULL,NULL,NULL),
	 ('SYS_FACTS',NULL,'GRP_ID6','GRP_ID6',NULL,'C','A','NH','DW',30,0,0,'0','1','1','1','1',NULL,NULL,16,NULL,'0','1',NULL,'0',NULL,'0','0',NULL,'0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,'2013-04-01 11:23:19','2013-04-01 11:23:19',NULL,NULL,NULL,NULL,NULL),
	 ('SYS_FACTS',NULL,'GRP_ID7','GRP_ID7',NULL,'C','A','NH','DW',30,0,0,'0','1','1','1','1',NULL,NULL,17,NULL,'0','1',NULL,'0',NULL,'0','0',NULL,'0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,'2013-04-01 11:23:19','2013-04-01 11:23:19',NULL,NULL,NULL,NULL,NULL),
	 ('SYS_FACTS',NULL,'GRP_ID8','GRP_ID8',NULL,'C','A','NH','DW',30,0,0,'0','1','1','1','1',NULL,NULL,18,NULL,'0','1',NULL,'0',NULL,'0','0',NULL,'0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,'2013-04-01 11:23:19','2013-04-01 11:23:19',NULL,NULL,NULL,NULL,NULL),
	 ('SYS_FACTS',NULL,'GRP_ID9','GRP_ID9',NULL,'C','A','NH','DW',30,0,0,'0','1','1','1','1',NULL,NULL,19,NULL,'0','1',NULL,'0',NULL,'0','0',NULL,'0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,'2013-04-01 11:23:19','2013-04-01 11:23:19',NULL,NULL,NULL,NULL,NULL),
	 ('SYS_FACTS',' ','OBJ_ID','OBJ_ID',' ','C','A','NH','DW',30,100,0,'0','0','1','1','1',' ',' ',7,'','0','1','','0',' ','0','0',' ','0',' ',' ',' ',' ',' ',' ',' ',' ',' ',0,'2013-04-01 11:23:19','2014-05-27 00:48:22',' ',NULL,NULL,NULL,NULL),
	 ('SYS_FACTS',NULL,'SYS_ID','SYS_ID',NULL,'C','A','NH','DW',30,0,0,'0','0','1','1','1',NULL,NULL,27,NULL,'0','1',NULL,'0',NULL,'0','0',NULL,'0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,'2013-04-01 11:23:19','2013-04-01 11:23:19',NULL,NULL,NULL,NULL,NULL),
	 ('SYS_INDEXS',NULL,'F_CHDATE','修改时间',NULL,'T','A','NH','DW',11,0,6,'0','0','1','1','1',NULL,'sysdate',895,NULL,'0','1',NULL,'0',NULL,'0','0',NULL,'0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,'2013-04-01 11:30:58','2013-04-01 11:30:58',NULL,NULL,NULL,NULL,NULL),
	 ('SYS_INDEXS',' ','F_CHUSER','修改人',' ','C','A','NH','DW',30,100,1,'0','1','1','1','1',' ',' ',11,'','0','1','','1','BSUSER','0','0',' ','0',' ',' ',' ',' ',' ',' ',' ',' ',' ',1,'2021-09-22 15:35:28','2021-09-23 15:41:46',' ',NULL,NULL,NULL,NULL),
	 ('SYS_INDEXS',NULL,'F_CRDATE','创建时间',NULL,'T','A','NH','DW',11,0,6,'0','0','1','1','1',NULL,'sysdate',894,NULL,'0','1',NULL,'0',NULL,'0','0',NULL,'0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,'2013-04-01 11:30:58','2013-04-01 11:30:58',NULL,NULL,NULL,NULL,NULL);
INSERT INTO SYS_OBJCOLS (OBJ_ID,COL_BASE,COL_ID,COL_MC,COL_DES,COL_TYPE,COL_APPTYPE1,COL_APPTYPE2,COL_CSTATT,COL_LEN,COL_PREC,COL_SCALE,COL_ISKEY,COL_ISNULL,COL_VISIBLE,COL_EDITABLE,COL_EDIT,COL_VIEW,COL_DEFAULT,COL_DISP,COL_ORDER,COL_MUNIT,COL_USE,COL_VALUE,COL_ISFKEY,COL_FOBJ,COL_LANG,COL_ISCALC,COL_COLGS,COL_CHECK,COL_REGEXREF,COL_ALIAS,COL_COLJY,COL_CONT,COL_EXT_ID,COL_OPT,COL_REF,F_LANG,F_GUID,F_STAU,F_CRDATE,F_CHDATE,COL_FKEY,SYS_ID,COL_ADD_LOG,F_CRUSER,F_CHUSER) VALUES
	 ('SYS_INDEXS',' ','F_CRUSER','创建人',' ','C','A','NH','DW',30,100,1,'0','1','1','1','1',' ',' ',11,'','0','1','','1','BSUSER','0','0',' ','0',' ',' ',' ',' ',' ',' ',' ',' ',' ',1,'2021-09-22 15:35:28','2021-09-23 15:41:46',' ',NULL,NULL,NULL,NULL),
	 ('SYS_INDEXS',NULL,'F_STAU','F_STAU',NULL,'I','A','NH','DW',22,0,0,'0','0','1','1','1',NULL,'0',7,NULL,'0','1',NULL,'0',NULL,'0','0',NULL,'0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,'2013-04-01 11:30:58','2013-04-01 11:30:58',NULL,NULL,NULL,NULL,NULL),
	 ('SYS_INDEXS',NULL,'INX_CLT','簇索引',NULL,'C','A','NH','DW',1,0,0,'0','0','1','1','1',NULL,NULL,4,NULL,'0','1',NULL,'0',NULL,'0','0',NULL,'0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,'2013-04-01 11:30:58','2013-04-01 11:30:58',NULL,NULL,NULL,NULL,NULL),
	 ('SYS_INDEXS',NULL,'INX_COLS','索引列',NULL,'C','A','NH','DW',255,0,0,'0','0','1','1','1',NULL,NULL,6,NULL,'0','1',NULL,'0',NULL,'0','0',NULL,'0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,'2013-04-01 11:30:58','2013-04-01 11:30:58',NULL,NULL,NULL,NULL,NULL),
	 ('SYS_INDEXS',NULL,'INX_ID','索引编号',NULL,'C','A','NH','DW',60,0,0,'1','0','1','1','1',NULL,NULL,1,NULL,'0','1',NULL,'0',NULL,'0','0',NULL,'0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,'2013-04-01 11:30:58','2013-04-01 11:30:58',NULL,NULL,NULL,NULL,NULL),
	 ('SYS_INDEXS',NULL,'INX_NAME','对象名称',NULL,'C','A','NH','DW',255,0,0,'0','0','1','1','1',NULL,NULL,2,NULL,'0','1',NULL,'0',NULL,'0','0',NULL,'0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,'2013-04-01 11:30:58','2013-04-01 11:30:58',NULL,NULL,NULL,NULL,NULL),
	 ('SYS_INDEXS',NULL,'INX_TYPE','唯一索引',NULL,'C','A','NH','DW',1,0,0,'0','0','1','1','1',NULL,NULL,3,NULL,'0','1',NULL,'0',NULL,'0','0',NULL,'0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,'2013-04-01 11:30:58','2013-04-01 11:30:58',NULL,NULL,NULL,NULL,NULL),
	 ('SYS_INDEXS',NULL,'OBJ_ID','对象标识',NULL,'C','A','NH','DW',30,0,0,'0','0','1','1','1',NULL,NULL,1,NULL,'0','1',NULL,'0',NULL,'0','0',NULL,'0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,'2013-04-01 11:30:58','2013-04-01 11:30:58',NULL,NULL,NULL,NULL,NULL);
INSERT INTO SYS_OBJCOLS (OBJ_ID,COL_BASE,COL_ID,COL_MC,COL_DES,COL_TYPE,COL_APPTYPE1,COL_APPTYPE2,COL_CSTATT,COL_LEN,COL_PREC,COL_SCALE,COL_ISKEY,COL_ISNULL,COL_VISIBLE,COL_EDITABLE,COL_EDIT,COL_VIEW,COL_DEFAULT,COL_DISP,COL_ORDER,COL_MUNIT,COL_USE,COL_VALUE,COL_ISFKEY,COL_FOBJ,COL_LANG,COL_ISCALC,COL_COLGS,COL_CHECK,COL_REGEXREF,COL_ALIAS,COL_COLJY,COL_CONT,COL_EXT_ID,COL_OPT,COL_REF,F_LANG,F_GUID,F_STAU,F_CRDATE,F_CHDATE,COL_FKEY,SYS_ID,COL_ADD_LOG,F_CRUSER,F_CHUSER) VALUES
	 ('SYS_KEYS',NULL,'F_CHDATE','F_CHDATE',NULL,'T','A','NH','DW',11,0,6,'0','0','1','1','1',NULL,'sysdate',32,NULL,'0','1',NULL,'0',NULL,'0','0',NULL,'0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,'2013-04-01 11:31:51','2013-04-01 11:31:51',NULL,NULL,NULL,NULL,NULL),
	 ('SYS_KEYS',NULL,'F_CRDATE','F_CRDATE',NULL,'T','A','NH','DW',11,0,6,'0','0','1','1','1',NULL,'sysdate',31,NULL,'0','1',NULL,'0',NULL,'0','0',NULL,'0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,'2013-04-01 11:31:51','2013-04-01 11:31:51',NULL,NULL,NULL,NULL,NULL),
	 ('SYS_KEYS',NULL,'F_STAU','F_STAU',NULL,'I','A','NH','DW',22,0,0,'0','0','1','1','1',NULL,'0',30,NULL,'0','1',NULL,'0',NULL,'0','0',NULL,'0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,'2013-04-01 11:31:51','2013-04-01 11:31:51',NULL,NULL,NULL,NULL,NULL);
INSERT INTO SYS_OBJCOLS (OBJ_ID,COL_BASE,COL_ID,COL_MC,COL_DES,COL_TYPE,COL_APPTYPE1,COL_APPTYPE2,COL_CSTATT,COL_LEN,COL_PREC,COL_SCALE,COL_ISKEY,COL_ISNULL,COL_VISIBLE,COL_EDITABLE,COL_EDIT,COL_VIEW,COL_DEFAULT,COL_DISP,COL_ORDER,COL_MUNIT,COL_USE,COL_VALUE,COL_ISFKEY,COL_FOBJ,COL_LANG,COL_ISCALC,COL_COLGS,COL_CHECK,COL_REGEXREF,COL_ALIAS,COL_COLJY,COL_CONT,COL_EXT_ID,COL_OPT,COL_REF,F_LANG,F_GUID,F_STAU,F_CRDATE,F_CHDATE,COL_FKEY,SYS_ID,COL_ADD_LOG,F_CRUSER,F_CHUSER) VALUES
	 ('SYS_KEYS',NULL,'KEY_CNT','KEY_CNT',NULL,'I','A','NH','DW',22,0,0,'0','0','1','1','1',NULL,NULL,5,NULL,'0','1',NULL,'0',NULL,'0','0',NULL,'0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,'2013-04-01 11:31:51','2013-04-01 11:31:51',NULL,NULL,NULL,NULL,NULL),
	 ('SYS_KEYS',NULL,'KEY_FINDEX1','KEY_FINDEX1',NULL,'C','A','NH','DW',30,0,0,'0','1','1','1','1',NULL,NULL,22,NULL,'0','1',NULL,'0',NULL,'0','0',NULL,'0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,'2013-04-01 11:31:51','2013-04-01 11:31:51',NULL,NULL,NULL,NULL,NULL),
	 ('SYS_KEYS',NULL,'KEY_FINDEX2','KEY_FINDEX2',NULL,'C','A','NH','DW',30,0,0,'0','1','1','1','1',NULL,NULL,23,NULL,'0','1',NULL,'0',NULL,'0','0',NULL,'0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,'2013-04-01 11:31:51','2013-04-01 11:31:51',NULL,NULL,NULL,NULL,NULL),
	 ('SYS_KEYS',NULL,'KEY_FINDEX3','KEY_FINDEX3',NULL,'C','A','NH','DW',30,0,0,'0','1','1','1','1',NULL,NULL,24,NULL,'0','1',NULL,'0',NULL,'0','0',NULL,'0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,'2013-04-01 11:31:51','2013-04-01 11:31:51',NULL,NULL,NULL,NULL,NULL),
	 ('SYS_KEYS',NULL,'KEY_FINDEX4','KEY_FINDEX4',NULL,'C','A','NH','DW',30,0,0,'0','1','1','1','1',NULL,NULL,25,NULL,'0','1',NULL,'0',NULL,'0','0',NULL,'0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,'2013-04-01 11:31:51','2013-04-01 11:31:51',NULL,NULL,NULL,NULL,NULL),
	 ('SYS_KEYS',NULL,'KEY_FINDEX5','KEY_FINDEX5',NULL,'C','A','NH','DW',30,0,0,'0','1','1','1','1',NULL,NULL,26,NULL,'0','1',NULL,'0',NULL,'0','0',NULL,'0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,'2013-04-01 11:31:51','2013-04-01 11:31:51',NULL,NULL,NULL,NULL,NULL),
	 ('SYS_KEYS',NULL,'KEY_FINDEX6','KEY_FINDEX6',NULL,'C','A','NH','DW',30,0,0,'0','1','1','1','1',NULL,NULL,27,NULL,'0','1',NULL,'0',NULL,'0','0',NULL,'0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,'2013-04-01 11:31:51','2013-04-01 11:31:51',NULL,NULL,NULL,NULL,NULL),
	 ('SYS_KEYS',NULL,'KEY_FINDEX7','KEY_FINDEX7',NULL,'C','A','NH','DW',30,0,0,'0','1','1','1','1',NULL,NULL,28,NULL,'0','1',NULL,'0',NULL,'0','0',NULL,'0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,'2013-04-01 11:31:51','2013-04-01 11:31:51',NULL,NULL,NULL,NULL,NULL),
	 ('SYS_KEYS',NULL,'KEY_FINDEX8','KEY_FINDEX8',NULL,'C','A','NH','DW',30,0,0,'0','1','1','1','1',NULL,NULL,29,NULL,'0','1',NULL,'0',NULL,'0','0',NULL,'0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,'2013-04-01 11:31:51','2013-04-01 11:31:51',NULL,NULL,NULL,NULL,NULL),
	 ('SYS_KEYS',NULL,'KEY_ID','KEY_ID',NULL,'C','A','NH','DW',60,0,0,'1','0','1','1','1',NULL,NULL,1,NULL,'0','1',NULL,'0',NULL,'0','0',NULL,'0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,'2013-04-01 11:31:51','2013-04-01 11:31:51',NULL,NULL,NULL,NULL,NULL);
INSERT INTO SYS_OBJCOLS (OBJ_ID,COL_BASE,COL_ID,COL_MC,COL_DES,COL_TYPE,COL_APPTYPE1,COL_APPTYPE2,COL_CSTATT,COL_LEN,COL_PREC,COL_SCALE,COL_ISKEY,COL_ISNULL,COL_VISIBLE,COL_EDITABLE,COL_EDIT,COL_VIEW,COL_DEFAULT,COL_DISP,COL_ORDER,COL_MUNIT,COL_USE,COL_VALUE,COL_ISFKEY,COL_FOBJ,COL_LANG,COL_ISCALC,COL_COLGS,COL_CHECK,COL_REGEXREF,COL_ALIAS,COL_COLJY,COL_CONT,COL_EXT_ID,COL_OPT,COL_REF,F_LANG,F_GUID,F_STAU,F_CRDATE,F_CHDATE,COL_FKEY,SYS_ID,COL_ADD_LOG,F_CRUSER,F_CHUSER) VALUES
	 ('SYS_KEYS',NULL,'KEY_INDEX','KEY_INDEX',NULL,'C','A','NH','DW',30,0,0,'0','1','1','1','1',NULL,NULL,22,NULL,'0','1',NULL,'0',NULL,'0','0',NULL,'0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,'2013-04-01 11:31:51','2013-04-01 11:31:51',NULL,NULL,'',NULL,NULL),
	 ('SYS_KEYS',NULL,'KEY_PINDEX1','KEY_PINDEX1',NULL,'C','A','NH','DW',30,0,0,'0','1','1','1','1',NULL,NULL,6,NULL,'0','1',NULL,'0',NULL,'0','0',NULL,'0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,'2013-04-01 11:31:51','2013-04-01 11:31:51',NULL,NULL,NULL,NULL,NULL),
	 ('SYS_KEYS',NULL,'KEY_PINDEX10','KEY_PINDEX10',NULL,'C','A','NH','DW',30,0,0,'0','1','1','1','1',NULL,NULL,15,NULL,'0','1',NULL,'0',NULL,'0','0',NULL,'0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,'2013-04-01 11:31:51','2013-04-01 11:31:51',NULL,NULL,NULL,NULL,NULL),
	 ('SYS_KEYS',NULL,'KEY_PINDEX11','KEY_PINDEX11',NULL,'C','A','NH','DW',30,0,0,'0','1','1','1','1',NULL,NULL,16,NULL,'0','1',NULL,'0',NULL,'0','0',NULL,'0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,'2013-04-01 11:31:51','2013-04-01 11:31:51',NULL,NULL,NULL,NULL,NULL),
	 ('SYS_KEYS',NULL,'KEY_PINDEX12','KEY_PINDEX12',NULL,'C','A','NH','DW',30,0,0,'0','1','1','1','1',NULL,NULL,17,NULL,'0','1',NULL,'0',NULL,'0','0',NULL,'0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,'2013-04-01 11:31:51','2013-04-01 11:31:51',NULL,NULL,NULL,NULL,NULL),
	 ('SYS_KEYS',NULL,'KEY_PINDEX13','KEY_PINDEX13',NULL,'C','A','NH','DW',30,0,0,'0','1','1','1','1',NULL,NULL,18,NULL,'0','1',NULL,'0',NULL,'0','0',NULL,'0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,'2013-04-01 11:31:51','2013-04-01 11:31:51',NULL,NULL,NULL,NULL,NULL),
	 ('SYS_KEYS',NULL,'KEY_PINDEX14','KEY_PINDEX14',NULL,'C','A','NH','DW',30,0,0,'0','1','1','1','1',NULL,NULL,19,NULL,'0','1',NULL,'0',NULL,'0','0',NULL,'0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,'2013-04-01 11:31:51','2013-04-01 11:31:51',NULL,NULL,NULL,NULL,NULL),
	 ('SYS_KEYS',NULL,'KEY_PINDEX15','KEY_PINDEX15',NULL,'C','A','NH','DW',30,0,0,'0','1','1','1','1',NULL,NULL,20,NULL,'0','1',NULL,'0',NULL,'0','0',NULL,'0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,'2013-04-01 11:31:51','2013-04-01 11:31:51',NULL,NULL,NULL,NULL,NULL),
	 ('SYS_KEYS',NULL,'KEY_PINDEX16','KEY_PINDEX16',NULL,'C','A','NH','DW',30,0,0,'0','1','1','1','1',NULL,NULL,21,NULL,'0','1',NULL,'0',NULL,'0','0',NULL,'0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,'2013-04-01 11:31:51','2013-04-01 11:31:51',NULL,NULL,NULL,NULL,NULL),
	 ('SYS_KEYS',NULL,'KEY_PINDEX2','KEY_PINDEX2',NULL,'C','A','NH','DW',30,0,0,'0','1','1','1','1',NULL,NULL,7,NULL,'0','1',NULL,'0',NULL,'0','0',NULL,'0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,'2013-04-01 11:31:51','2013-04-01 11:31:51',NULL,NULL,NULL,NULL,NULL);
INSERT INTO SYS_OBJCOLS (OBJ_ID,COL_BASE,COL_ID,COL_MC,COL_DES,COL_TYPE,COL_APPTYPE1,COL_APPTYPE2,COL_CSTATT,COL_LEN,COL_PREC,COL_SCALE,COL_ISKEY,COL_ISNULL,COL_VISIBLE,COL_EDITABLE,COL_EDIT,COL_VIEW,COL_DEFAULT,COL_DISP,COL_ORDER,COL_MUNIT,COL_USE,COL_VALUE,COL_ISFKEY,COL_FOBJ,COL_LANG,COL_ISCALC,COL_COLGS,COL_CHECK,COL_REGEXREF,COL_ALIAS,COL_COLJY,COL_CONT,COL_EXT_ID,COL_OPT,COL_REF,F_LANG,F_GUID,F_STAU,F_CRDATE,F_CHDATE,COL_FKEY,SYS_ID,COL_ADD_LOG,F_CRUSER,F_CHUSER) VALUES
	 ('SYS_KEYS',NULL,'KEY_PINDEX3','KEY_PINDEX3',NULL,'C','A','NH','DW',30,0,0,'0','1','1','1','1',NULL,NULL,8,NULL,'0','1',NULL,'0',NULL,'0','0',NULL,'0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,'2013-04-01 11:31:51','2013-04-01 11:31:51',NULL,NULL,NULL,NULL,NULL),
	 ('SYS_KEYS',NULL,'KEY_PINDEX4','KEY_PINDEX4',NULL,'C','A','NH','DW',30,0,0,'0','1','1','1','1',NULL,NULL,9,NULL,'0','1',NULL,'0',NULL,'0','0',NULL,'0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,'2013-04-01 11:31:51','2013-04-01 11:31:51',NULL,NULL,NULL,NULL,NULL),
	 ('SYS_KEYS',NULL,'KEY_PINDEX5','KEY_PINDEX5',NULL,'C','A','NH','DW',30,0,0,'0','1','1','1','1',NULL,NULL,10,NULL,'0','1',NULL,'0',NULL,'0','0',NULL,'0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,'2013-04-01 11:31:51','2013-04-01 11:31:51',NULL,NULL,NULL,NULL,NULL),
	 ('SYS_KEYS',NULL,'KEY_PINDEX6','KEY_PINDEX6',NULL,'C','A','NH','DW',30,0,0,'0','1','1','1','1',NULL,NULL,11,NULL,'0','1',NULL,'0',NULL,'0','0',NULL,'0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,'2013-04-01 11:31:51','2013-04-01 11:31:51',NULL,NULL,NULL,NULL,NULL),
	 ('SYS_KEYS',NULL,'KEY_PINDEX7','KEY_PINDEX7',NULL,'C','A','NH','DW',30,0,0,'0','1','1','1','1',NULL,NULL,12,NULL,'0','1',NULL,'0',NULL,'0','0',NULL,'0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,'2013-04-01 11:31:51','2013-04-01 11:31:51',NULL,NULL,NULL,NULL,NULL),
	 ('SYS_KEYS',NULL,'KEY_PINDEX8','KEY_PINDEX8',NULL,'C','A','NH','DW',30,0,0,'0','1','1','1','1',NULL,NULL,13,NULL,'0','1',NULL,'0',NULL,'0','0',NULL,'0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,'2013-04-01 11:31:51','2013-04-01 11:31:51',NULL,NULL,NULL,NULL,NULL),
	 ('SYS_KEYS',NULL,'KEY_PINDEX9','KEY_PINDEX9',NULL,'C','A','NH','DW',30,0,0,'0','1','1','1','1',NULL,NULL,14,NULL,'0','1',NULL,'0',NULL,'0','0',NULL,'0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,'2013-04-01 11:31:51','2013-04-01 11:31:51',NULL,NULL,NULL,NULL,NULL),
	 ('SYS_KEYS',NULL,'KEY_TYPE','KEY_TYPE',NULL,'C','A','NH','DW',30,0,0,'0','0','1','1','1',NULL,NULL,3,NULL,'0','1',NULL,'0',NULL,'0','0',NULL,'0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,'2013-04-01 11:31:51','2013-04-01 11:31:51',NULL,NULL,NULL,NULL,NULL),
	 ('SYS_KEYS',NULL,'OBJ_DEPID','OBJ_DEPID',NULL,'C','A','NH','DW',30,0,0,'0','0','1','1','1',NULL,NULL,4,NULL,'0','1',NULL,'0',NULL,'0','0',NULL,'0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,'2013-04-01 11:31:51','2013-04-01 11:31:51',NULL,NULL,NULL,NULL,NULL),
	 ('SYS_KEYS',NULL,'OBJ_ID','OBJ_ID',NULL,'C','A','NH','DW',30,0,0,'0','0','1','1','1',NULL,NULL,2,NULL,'0','1',NULL,'0',NULL,'0','0',NULL,'0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,'2013-04-01 11:31:51','2013-04-01 11:31:51',NULL,NULL,NULL,NULL,NULL);
INSERT INTO SYS_OBJCOLS (OBJ_ID,COL_BASE,COL_ID,COL_MC,COL_DES,COL_TYPE,COL_APPTYPE1,COL_APPTYPE2,COL_CSTATT,COL_LEN,COL_PREC,COL_SCALE,COL_ISKEY,COL_ISNULL,COL_VISIBLE,COL_EDITABLE,COL_EDIT,COL_VIEW,COL_DEFAULT,COL_DISP,COL_ORDER,COL_MUNIT,COL_USE,COL_VALUE,COL_ISFKEY,COL_FOBJ,COL_LANG,COL_ISCALC,COL_COLGS,COL_CHECK,COL_REGEXREF,COL_ALIAS,COL_COLJY,COL_CONT,COL_EXT_ID,COL_OPT,COL_REF,F_LANG,F_GUID,F_STAU,F_CRDATE,F_CHDATE,COL_FKEY,SYS_ID,COL_ADD_LOG,F_CRUSER,F_CHUSER) VALUES
	 ('SYS_MDL_CTN',' ','CTN_FCT1','CTN_FCT1',' ','C','A','NH','DW',30,0,0,'0','0','1','1','1',' ',' ',5,'','0','1','','1','SYS_FACTS','0','0',' ','0',' ',' ',' ',' ',' ',' ',' ',' ',' ',0,'2013-04-02 11:19:02','2014-05-27 00:51:45',' ',NULL,NULL,NULL,NULL),
	 ('SYS_MDL_CTN',NULL,'CTN_FCT10','CTN_FCT10',NULL,'C','A','NH','DW',30,0,0,'0','1','1','1','1',NULL,NULL,14,NULL,'0','1',NULL,'1','SYS_FACTS','0','0',NULL,'0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,'2013-04-02 11:19:02','2013-04-02 11:19:02',NULL,NULL,NULL,NULL,NULL),
	 ('SYS_MDL_CTN',NULL,'CTN_FCT11','CTN_FCT11',NULL,'C','A','NH','DW',30,0,0,'0','1','1','1','1',NULL,NULL,15,NULL,'0','1',NULL,'1','SYS_FACTS','0','0',NULL,'0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,'2013-04-02 11:19:02','2013-04-02 11:19:02',NULL,NULL,NULL,NULL,NULL),
	 ('SYS_MDL_CTN',NULL,'CTN_FCT12','CTN_FCT12',NULL,'C','A','NH','DW',30,0,0,'0','1','1','1','1',NULL,NULL,16,NULL,'0','1',NULL,'1','SYS_FACTS','0','0',NULL,'0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,'2013-04-02 11:19:02','2013-04-02 11:19:02',NULL,NULL,NULL,NULL,NULL),
	 ('SYS_MDL_CTN',NULL,'CTN_FCT13','CTN_FCT13',NULL,'C','A','NH','DW',30,0,0,'0','1','1','1','1',NULL,NULL,17,NULL,'0','1',NULL,'1','SYS_FACTS','0','0',NULL,'0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,'2013-04-02 11:19:02','2013-04-02 11:19:02',NULL,NULL,NULL,NULL,NULL),
	 ('SYS_MDL_CTN',NULL,'CTN_FCT14','CTN_FCT14',NULL,'C','A','NH','DW',30,0,0,'0','1','1','1','1',NULL,NULL,18,NULL,'0','1',NULL,'1','SYS_FACTS','0','0',NULL,'0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,'2013-04-02 11:19:02','2013-04-02 11:19:02',NULL,NULL,NULL,NULL,NULL),
	 ('SYS_MDL_CTN',NULL,'CTN_FCT15','CTN_FCT15',NULL,'C','A','NH','DW',30,0,0,'0','1','1','1','1',NULL,NULL,19,NULL,'0','1',NULL,'1','SYS_FACTS','0','0',NULL,'0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,'2013-04-02 11:19:02','2013-04-02 11:19:02',NULL,NULL,NULL,NULL,NULL),
	 ('SYS_MDL_CTN',NULL,'CTN_FCT16','CTN_FCT16',NULL,'C','A','NH','DW',30,0,0,'0','1','1','1','1',NULL,NULL,20,NULL,'0','1',NULL,'1','SYS_FACTS','0','0',NULL,'0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,'2013-04-02 11:19:02','2013-04-02 11:19:02',NULL,NULL,NULL,NULL,NULL),
	 ('SYS_MDL_CTN',' ','CTN_FCT2','CTN_FCT2',' ','C','A','NH','DW',30,0,0,'0','1','1','1','1',' ',' ',6,'','0','1','','1','SYS_FACTS','0','0',' ','0',' ',' ',' ',' ',' ',' ',' ',' ',' ',0,'2013-04-02 11:19:02','2014-05-27 00:51:45',' ',NULL,NULL,NULL,NULL),
	 ('SYS_MDL_CTN',' ','CTN_FCT3','CTN_FCT3',' ','C','A','NH','DW',30,0,0,'0','1','1','1','1',' ',' ',7,'','0','1','','1','SYS_FACTS','0','0',' ','0',' ',' ',' ',' ',' ',' ',' ',' ',' ',0,'2013-04-02 11:19:02','2014-05-27 00:51:45',' ',NULL,NULL,NULL,NULL);
INSERT INTO SYS_OBJCOLS (OBJ_ID,COL_BASE,COL_ID,COL_MC,COL_DES,COL_TYPE,COL_APPTYPE1,COL_APPTYPE2,COL_CSTATT,COL_LEN,COL_PREC,COL_SCALE,COL_ISKEY,COL_ISNULL,COL_VISIBLE,COL_EDITABLE,COL_EDIT,COL_VIEW,COL_DEFAULT,COL_DISP,COL_ORDER,COL_MUNIT,COL_USE,COL_VALUE,COL_ISFKEY,COL_FOBJ,COL_LANG,COL_ISCALC,COL_COLGS,COL_CHECK,COL_REGEXREF,COL_ALIAS,COL_COLJY,COL_CONT,COL_EXT_ID,COL_OPT,COL_REF,F_LANG,F_GUID,F_STAU,F_CRDATE,F_CHDATE,COL_FKEY,SYS_ID,COL_ADD_LOG,F_CRUSER,F_CHUSER) VALUES
	 ('SYS_MDL_CTN',NULL,'CTN_FCT4','CTN_FCT4',NULL,'C','A','NH','DW',30,0,0,'0','1','1','1','1',NULL,NULL,8,NULL,'0','1',NULL,'1','SYS_FACTS','0','0',NULL,'0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,'2013-04-02 11:19:02','2013-04-02 11:19:02',NULL,NULL,NULL,NULL,NULL),
	 ('SYS_MDL_CTN',NULL,'CTN_FCT5','CTN_FCT5',NULL,'C','A','NH','DW',30,0,0,'0','1','1','1','1',NULL,NULL,9,NULL,'0','1',NULL,'1','SYS_FACTS','0','0',NULL,'0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,'2013-04-02 11:19:02','2013-04-02 11:19:02',NULL,NULL,NULL,NULL,NULL),
	 ('SYS_MDL_CTN',NULL,'CTN_FCT6','CTN_FCT6',NULL,'C','A','NH','DW',30,0,0,'0','1','1','1','1',NULL,NULL,10,NULL,'0','1',NULL,'1','SYS_FACTS','0','0',NULL,'0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,'2013-04-02 11:19:02','2013-04-02 11:19:02',NULL,NULL,NULL,NULL,NULL),
	 ('SYS_MDL_CTN',NULL,'CTN_FCT7','CTN_FCT7',NULL,'C','A','NH','DW',30,0,0,'0','1','1','1','1',NULL,NULL,11,NULL,'0','1',NULL,'1','SYS_FACTS','0','0',NULL,'0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,'2013-04-02 11:19:02','2013-04-02 11:19:02',NULL,NULL,NULL,NULL,NULL),
	 ('SYS_MDL_CTN',NULL,'CTN_FCT8','CTN_FCT8',NULL,'C','A','NH','DW',30,0,0,'0','1','1','1','1',NULL,NULL,12,NULL,'0','1',NULL,'1','SYS_FACTS','0','0',NULL,'0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,'2013-04-02 11:19:02','2013-04-02 11:19:02',NULL,NULL,NULL,NULL,NULL),
	 ('SYS_MDL_CTN',NULL,'CTN_FCT9','CTN_FCT9',NULL,'C','A','NH','DW',30,0,0,'0','1','1','1','1',NULL,NULL,13,NULL,'0','1',NULL,'1','SYS_FACTS','0','0',NULL,'0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,'2013-04-02 11:19:02','2013-04-02 11:19:02',NULL,NULL,NULL,NULL,NULL),
	 ('SYS_MDL_CTN',NULL,'CTN_ID','CTN_ID',NULL,'C','A','NH','DW',30,0,0,'1','0','1','1','1',NULL,NULL,2,NULL,'0','1',NULL,'0',NULL,'0','0',NULL,'0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,'2013-04-02 11:19:02','2013-04-02 11:19:02',NULL,NULL,NULL,NULL,NULL),
	 ('SYS_MDL_CTN',NULL,'CTN_MC','CTN_MC',NULL,'C','A','NH','DW',255,0,0,'0','1','1','1','1',NULL,NULL,4,NULL,'0','1',NULL,'0',NULL,'0','0',NULL,'0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,'2013-04-02 11:19:02','2013-04-02 11:19:02',NULL,NULL,NULL,NULL,NULL),
	 ('SYS_MDL_CTN',NULL,'CTN_TYPE','CTN_TYPE',NULL,'C','A','NH','DW',4,0,0,'0','0','1','1','1',NULL,NULL,3,NULL,'0','1',NULL,'0',NULL,'0','0',NULL,'0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,'2013-04-02 11:19:02','2013-04-02 11:19:02',NULL,NULL,NULL,NULL,NULL),
	 ('SYS_MDL_CTN','F_CHDATE','F_CHDATE','修改时间','维表、关系表、事实表 都需要的列','T','A','NH','DW',19,100,1,'0','1','1','1','1',' ',' ',7,'','0','1','','0',' ','0','0',' ','0',' ',' ',' ',' ',' ',' ',' ',' ',' ',1,'2021-01-09 12:50:53','2021-09-26 14:36:49',' ',' ',' ',NULL,NULL);
INSERT INTO SYS_OBJCOLS (OBJ_ID,COL_BASE,COL_ID,COL_MC,COL_DES,COL_TYPE,COL_APPTYPE1,COL_APPTYPE2,COL_CSTATT,COL_LEN,COL_PREC,COL_SCALE,COL_ISKEY,COL_ISNULL,COL_VISIBLE,COL_EDITABLE,COL_EDIT,COL_VIEW,COL_DEFAULT,COL_DISP,COL_ORDER,COL_MUNIT,COL_USE,COL_VALUE,COL_ISFKEY,COL_FOBJ,COL_LANG,COL_ISCALC,COL_COLGS,COL_CHECK,COL_REGEXREF,COL_ALIAS,COL_COLJY,COL_CONT,COL_EXT_ID,COL_OPT,COL_REF,F_LANG,F_GUID,F_STAU,F_CRDATE,F_CHDATE,COL_FKEY,SYS_ID,COL_ADD_LOG,F_CRUSER,F_CHUSER) VALUES
	 ('SYS_MDL_CTN',' ','F_CHUSER','修改人',' ','C','A','NH','DW',30,100,1,'0','1','1','1','1',' ',' ',11,'','0','1','','1','BSUSER','0','0',' ','0',' ',' ',' ',' ',' ',' ',' ',' ',' ',1,'2021-09-22 15:35:28','2021-09-23 15:41:46',' ',NULL,NULL,NULL,NULL),
	 ('SYS_MDL_CTN','F_CRDATE','F_CRDATE','创建时间','维表、关系表、事实表 都需要的列','T','A','NH','DW',19,100,1,'0','1','1','1','1',' ',' ',7,'','0','1','','0',' ','0','0',' ','0',' ',' ',' ',' ',' ',' ',' ',' ',' ',1,'2021-01-09 12:50:53','2021-09-26 14:36:49',' ',' ',' ',NULL,NULL),
	 ('SYS_MDL_CTN',' ','F_CRUSER','创建人',' ','C','A','NH','DW',30,100,1,'0','1','1','1','1',' ',' ',11,'','0','1','','1','BSUSER','0','0',' ','0',' ',' ',' ',' ',' ',' ',' ',' ',' ',1,'2021-09-22 15:35:28','2021-09-23 15:41:46',' ',NULL,NULL,NULL,NULL),
	 ('SYS_MDL_CTN',NULL,'MDL_ID','MDL_ID',NULL,'C','A','NH','DW',30,0,0,'1','0','1','1','1',NULL,NULL,1,NULL,'0','1',NULL,'0',NULL,'0','0',NULL,'0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,'2013-04-02 11:19:02','2013-04-02 11:19:02',NULL,NULL,NULL,NULL,NULL),
	 ('SYS_MDL_CTN',NULL,'PCTN_ID','PCTN_ID',NULL,'C','A','NH','DW',30,0,0,'0','1','1','1','1',NULL,NULL,21,NULL,'0','1',NULL,'0',NULL,'0','0',NULL,'0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,'2013-04-02 11:19:02','2013-04-02 11:19:02',NULL,NULL,NULL,NULL,NULL),
	 ('SYS_MDL_VAL',NULL,'MDL_ID','MDL_ID',NULL,'C','A','NH','DW',30,0,0,'1','0','1','1','1',NULL,NULL,1,NULL,'0','1',NULL,'0',NULL,'0','0',NULL,'0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,'2013-04-02 11:21:12','2013-04-02 11:21:12',NULL,NULL,NULL,NULL,NULL),
	 ('SYS_MDL_VAL',NULL,'MDL_KEY','MDL_KEY',NULL,'C','A','NH','DW',30,0,0,'1','0','1','1','1',NULL,NULL,2,NULL,'0','1',NULL,'0',NULL,'0','0',NULL,'0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,'2013-04-02 11:21:12','2013-04-02 11:21:12',NULL,NULL,NULL,NULL,NULL),
	 ('SYS_MDL_VAL',' ','MDL_NOTE','MDL_NOTE',' ','C','A','NH','DW',100,100,1,'0','1','1','1','1',' ',' ',14,'','0','1','','0',' ','0','0',' ','0',' ',' ',' ',' ',' ',' ',' ',' ',' ',1,'2020-04-28 11:27:23','2020-04-28 11:29:12',' ',' ',' ',NULL,NULL),
	 ('SYS_MDL_VAL',NULL,'MDL_VALUE','MDL_VALUE',NULL,'C','A','NH','DW',255,0,0,'0','1','1','1','1',NULL,NULL,4,NULL,'0','1',NULL,'0',NULL,'0','0',NULL,'0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,'2013-04-02 11:21:12','2013-04-02 11:21:12',NULL,NULL,NULL,NULL,NULL),
	 ('SYS_MDL_VAL',NULL,'UNIT_ID','UNIT_ID',NULL,'C','A','NH','DW',30,0,0,'1','1','1','1','1',NULL,NULL,3,NULL,'0','1',NULL,'0',NULL,'0','0',NULL,'0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,'2013-04-02 11:21:12','2013-04-02 11:21:12',NULL,NULL,NULL,NULL,NULL);
INSERT INTO SYS_OBJCOLS (OBJ_ID,COL_BASE,COL_ID,COL_MC,COL_DES,COL_TYPE,COL_APPTYPE1,COL_APPTYPE2,COL_CSTATT,COL_LEN,COL_PREC,COL_SCALE,COL_ISKEY,COL_ISNULL,COL_VISIBLE,COL_EDITABLE,COL_EDIT,COL_VIEW,COL_DEFAULT,COL_DISP,COL_ORDER,COL_MUNIT,COL_USE,COL_VALUE,COL_ISFKEY,COL_FOBJ,COL_LANG,COL_ISCALC,COL_COLGS,COL_CHECK,COL_REGEXREF,COL_ALIAS,COL_COLJY,COL_CONT,COL_EXT_ID,COL_OPT,COL_REF,F_LANG,F_GUID,F_STAU,F_CRDATE,F_CHDATE,COL_FKEY,SYS_ID,COL_ADD_LOG,F_CRUSER,F_CHUSER) VALUES
	 ('SYS_MODEL',NULL,'CVT_UNITSET','CVT_UNITSET',NULL,'C','A','NH','DW',1,0,0,'0','0','1','1','1',NULL,'0',17,NULL,'0','1',NULL,'0',NULL,'0','0',NULL,'0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,'2013-04-02 11:21:59','2013-04-02 11:21:59',NULL,NULL,NULL,NULL,NULL),
	 ('SYS_MODEL',NULL,'F_BM_STAU','F_BM_STAU',NULL,'I','A','NH','DW',22,0,0,'0','1','1','1','1',NULL,'0',18,NULL,'0','1',NULL,'0',NULL,'0','0',NULL,'0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,'2013-04-02 11:21:59','2013-04-02 11:21:59',NULL,NULL,NULL,NULL,NULL),
	 ('SYS_MODEL','F_CHDATE','F_CHDATE','修改时间','维表、关系表、事实表 都需要的列','T','A','NH','DW',19,100,1,'0','1','1','1','1',' ',' ',7,'','0','1','','0',' ','0','0',' ','0',' ',' ',' ',' ',' ',' ',' ',' ',' ',1,'2021-01-09 12:50:53','2021-09-26 14:36:49',' ',' ',' ',NULL,NULL),
	 ('SYS_MODEL',' ','F_CHUSER','修改人',' ','C','A','NH','DW',30,100,1,'0','1','1','1','1',' ',' ',11,'','0','1','','1','BSUSER','0','0',' ','0',' ',' ',' ',' ',' ',' ',' ',' ',' ',1,'2021-09-22 15:35:28','2021-09-23 15:41:46',' ',NULL,NULL,NULL,NULL),
	 ('SYS_MODEL','F_CRDATE','F_CRDATE','创建时间','维表、关系表、事实表 都需要的列','T','A','NH','DW',19,100,1,'0','1','1','1','1',' ',' ',7,'','0','1','','0',' ','0','0',' ','0',' ',' ',' ',' ',' ',' ',' ',' ',' ',1,'2021-01-09 12:50:53','2021-09-26 14:36:49',' ',' ',' ',NULL,NULL),
	 ('SYS_MODEL',' ','F_CRUSER','创建人',' ','C','A','NH','DW',30,100,1,'0','1','1','1','1',' ',' ',11,'','0','1','','1','BSUSER','0','0',' ','0',' ',' ',' ',' ',' ',' ',' ',' ',' ',1,'2021-09-22 15:35:28','2021-09-23 15:41:46',' ',NULL,NULL,NULL,NULL),
	 ('SYS_MODEL',NULL,'F_DF_STAU','F_DF_STAU',NULL,'I','A','NH','DW',22,0,0,'0','1','1','1','1',NULL,'0',19,NULL,'0','1',NULL,'0',NULL,'0','0',NULL,'0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,'2013-04-02 11:21:59','2013-04-02 11:21:59',NULL,NULL,NULL,NULL,NULL),
	 ('SYS_MODEL','F_ENABLE','F_ENABLE','是否停用',' ','C','A','NH','DW',1,75,1,'0','1','1','1','4','0:停用;1:使用','1',10,'','0','1','','0',' ','0','0',' ','0',' ',' ',' ',' ',' ',' ',' ',' ',' ',1,'2021-08-28 16:20:22','2021-09-26 14:36:49',' ',' ',' ',NULL,NULL),
	 ('SYS_MODEL',' ','F_MODELFKEY','F_MODELFKEY',' ','C','A','NH','DW',30,200,1,'0','1','1','1','1',' ','0',23,'','0','1','','0',' ','0','0',' ','0',' ',' ',' ',' ',' ',' ',' ',' ',' ',1,'2022-04-07 14:49:14','2022-04-07 14:49:40',' ',' ',' ','wangye',' '),
	 ('SYS_MODEL',NULL,'F_STAU','F_STAU',NULL,'I','A','NH','DW',22,0,0,'0','1','1','1','1',NULL,'0',8,NULL,'0','1',NULL,'0',NULL,'0','0',NULL,'0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,'2013-04-02 11:21:59','2013-04-02 11:21:59',NULL,NULL,NULL,NULL,NULL);
INSERT INTO SYS_OBJCOLS (OBJ_ID,COL_BASE,COL_ID,COL_MC,COL_DES,COL_TYPE,COL_APPTYPE1,COL_APPTYPE2,COL_CSTATT,COL_LEN,COL_PREC,COL_SCALE,COL_ISKEY,COL_ISNULL,COL_VISIBLE,COL_EDITABLE,COL_EDIT,COL_VIEW,COL_DEFAULT,COL_DISP,COL_ORDER,COL_MUNIT,COL_USE,COL_VALUE,COL_ISFKEY,COL_FOBJ,COL_LANG,COL_ISCALC,COL_COLGS,COL_CHECK,COL_REGEXREF,COL_ALIAS,COL_COLJY,COL_CONT,COL_EXT_ID,COL_OPT,COL_REF,F_LANG,F_GUID,F_STAU,F_CRDATE,F_CHDATE,COL_FKEY,SYS_ID,COL_ADD_LOG,F_CRUSER,F_CHUSER) VALUES
	 ('SYS_MODEL',NULL,'KEY_SET','KEY_SET',NULL,'C','A','NH','DW',1,0,0,'0','0','1','1','1',NULL,'0',6,NULL,'0','1',NULL,'0',NULL,'0','0',NULL,'0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,'2013-04-02 11:21:59','2013-04-02 11:21:59',NULL,NULL,NULL,NULL,NULL),
	 ('SYS_MODEL',NULL,'MDL_BHDCT','MDL_BHDCT',NULL,'C','A','NH','DW',30,0,0,'0','1','1','1','1',NULL,NULL,15,NULL,'0','1',NULL,'0',NULL,'0','0',NULL,'0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,'2013-04-02 11:21:59','2013-04-02 11:21:59',NULL,NULL,NULL,NULL,NULL),
	 ('SYS_MODEL',NULL,'MDL_ID','模型编号',NULL,'C','A','NH','DW',30,100,0,'1','0','1','1','1',NULL,NULL,1,NULL,'0','1',NULL,'0',NULL,'0','0',NULL,'0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,'2013-04-02 11:21:59','2013-04-02 11:21:59',NULL,NULL,NULL,NULL,NULL),
	 ('SYS_MODEL',NULL,'MDL_KEYDCT','MDL_KEYDCT',NULL,'C','A','NH','DW',30,0,0,'0','1','1','1','1',NULL,NULL,4,NULL,'0','1',NULL,'0',NULL,'0','0',NULL,'0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,'2013-04-02 11:21:59','2013-04-02 11:21:59',NULL,NULL,NULL,NULL,NULL),
	 ('SYS_MODEL',' ','MDL_MC','模型名称',' ','C','A','NH','DW',60,100,0,'0','0','1','1','1',' ',' ',2,'','0','1','','0',' ','0','0',' ','0',' ',' ',' ',' ',' ',' ',' ',' ',' ',0,'2013-04-02 11:21:59','2014-05-27 01:10:27',' ',NULL,NULL,NULL,NULL),
	 ('SYS_MODEL',NULL,'MDL_NDCT','MDL_NDCT',NULL,'C','A','NH','DW',30,0,0,'0','1','1','1','1',NULL,NULL,12,NULL,'0','1',NULL,'0',NULL,'0','0',NULL,'0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,'2013-04-02 11:21:59','2013-04-02 11:21:59',NULL,NULL,NULL,NULL,NULL),
	 ('SYS_MODEL',NULL,'MDL_RDCT','MDL_RDCT',NULL,'C','A','NH','DW',30,0,0,'0','1','1','1','1',NULL,NULL,14,NULL,'0','1',NULL,'0',NULL,'0','0',NULL,'0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,'2013-04-02 11:21:59','2013-04-02 11:21:59',NULL,NULL,NULL,NULL,NULL),
	 ('SYS_MODEL',NULL,'MDL_SJLX','MDL_SJLX',NULL,'C','A','NH','DW',1,0,0,'0','0','1','1','1',NULL,'Y',11,NULL,'0','1',NULL,'0',NULL,'0','0',NULL,'0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,'2013-04-02 11:21:59','2013-04-02 11:21:59',NULL,NULL,NULL,NULL,NULL),
	 ('SYS_MODEL',NULL,'MDL_TYDCT','MDL_TYDCT',NULL,'C','A','NH','DW',30,0,0,'0','1','1','1','1',NULL,NULL,16,NULL,'0','1',NULL,'0',NULL,'0','0',NULL,'0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,'2013-04-02 11:21:59','2013-04-02 11:21:59',NULL,NULL,NULL,NULL,NULL),
	 ('SYS_MODEL',NULL,'MDL_TYPE','MDL_TYPE',NULL,'C','A','NH','DW',4,0,0,'0','0','1','1','1',NULL,NULL,3,NULL,'0','1',NULL,'0',NULL,'0','0',NULL,'0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,'2013-04-02 11:21:59','2013-04-02 11:21:59',NULL,NULL,NULL,NULL,NULL);
INSERT INTO SYS_OBJCOLS (OBJ_ID,COL_BASE,COL_ID,COL_MC,COL_DES,COL_TYPE,COL_APPTYPE1,COL_APPTYPE2,COL_CSTATT,COL_LEN,COL_PREC,COL_SCALE,COL_ISKEY,COL_ISNULL,COL_VISIBLE,COL_EDITABLE,COL_EDIT,COL_VIEW,COL_DEFAULT,COL_DISP,COL_ORDER,COL_MUNIT,COL_USE,COL_VALUE,COL_ISFKEY,COL_FOBJ,COL_LANG,COL_ISCALC,COL_COLGS,COL_CHECK,COL_REGEXREF,COL_ALIAS,COL_COLJY,COL_CONT,COL_EXT_ID,COL_OPT,COL_REF,F_LANG,F_GUID,F_STAU,F_CRDATE,F_CHDATE,COL_FKEY,SYS_ID,COL_ADD_LOG,F_CRUSER,F_CHUSER) VALUES
	 ('SYS_MODEL',NULL,'MDL_UNITDCT','MDL_UNITDCT',NULL,'C','A','NH','DW',30,0,0,'0','1','1','1','1',NULL,NULL,5,NULL,'0','1',NULL,'0',NULL,'0','0',NULL,'0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,'2013-04-02 11:21:59','2013-04-02 11:21:59',NULL,NULL,NULL,NULL,NULL),
	 ('SYS_MODEL',NULL,'MDL_YDCT','MDL_YDCT',NULL,'C','A','NH','DW',30,0,0,'0','1','1','1','1',NULL,NULL,13,NULL,'0','1',NULL,'0',NULL,'0','0',NULL,'0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,'2013-04-02 11:21:59','2013-04-02 11:21:59',NULL,NULL,NULL,NULL,NULL),
	 ('SYS_MODEL',' ','OBJ_GROUP','OBJ_GROUP',' ','C','A','NH','DW',100,200,1,'0','1','1','1','1',' ',' ',24,'','0','1','','0',' ','0','0',' ','0',' ',' ',' ',' ',' ',' ',' ',' ',' ',1,'2023-07-11 14:45:00','2023-07-11 14:45:00',' ',' ',' ','9999',' '),
	 ('SYS_MODEL',' ','SYS_ID','SYS_ID',' ','C','A','NH','DW',30,0,0,'0','0','1','1','1',' ',' ',7,'','0','1','','1','SYS_LIST','0','0',' ','0',' ',' ',' ',' ',' ',' ',' ',' ',' ',0,'2013-04-02 11:21:59','2014-05-27 01:10:27',' ',NULL,NULL,NULL,NULL),
	 ('SYS_OBJCOLS',' ','COL_ADD_LOG',' ',' ','C','A','NH','DW',30,100,1,'0','1','1','1','1',' ',' ',11,'','0','1','','1','BSUSER','0','0',' ','0',' ',' ',' ',' ',' ',' ',' ',' ',' ',1,'2021-09-22 15:35:28','2021-09-23 15:41:46',' ',NULL,NULL,NULL,NULL),
	 ('SYS_OBJCOLS',NULL,'COL_ALIAS','COL_ALIAS',NULL,'C','A','NH','DW',30,0,0,'0','1','1','1','1',NULL,NULL,32,NULL,'0','1',NULL,'0',NULL,'0','0',NULL,'0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,'2013-04-01 11:43:15','2013-04-01 11:43:15',NULL,NULL,NULL,NULL,NULL),
	 ('SYS_OBJCOLS',NULL,'COL_APPTYPE1','COL_APPTYPE1',NULL,'C','A','NH','DW',1,0,0,'0','0','1','1','1',NULL,NULL,7,NULL,'0','1',NULL,'0',NULL,'0','0',NULL,'0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,'2013-04-01 11:43:15','2013-04-01 11:43:15',NULL,NULL,NULL,NULL,NULL),
	 ('SYS_OBJCOLS',NULL,'COL_APPTYPE2','COL_APPTYPE2',NULL,'C','A','NH','DW',2,0,0,'0','0','1','1','1',NULL,NULL,8,NULL,'0','1',NULL,'0',NULL,'0','0',NULL,'0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,'2013-04-01 11:43:15','2013-04-01 11:43:15',NULL,NULL,NULL,NULL,NULL),
	 ('SYS_OBJCOLS',' ','COL_BASE','COL_BASE',' ','C','A','NH','DW',1000,0,0,'0','1','1','1','1',' ',' ',2,'','0','1','','1','SYS_COL_DEF','0','0',' ','0',' ',' ',' ',' ',' ','@refCol=fkey@COL_ID,skey@COL_ID;@refCol=fkey@COL_MC,skey@COL_MC;@refCol=fkey@COL_LEN,skey@COL_LEN;@refCol=fkey@COL_PREC,skey@COL_PREC;@refCol=fkey@COL_ISNULL,skey@COL_ISNULL;@refCol=fkey@COL_VIEW,skey@COL_VIEW;@refCol=fkey@COL_LANG,skey@COL_LANG;@refCol=fkey@COL_DES,skey@COL_DES;@refCol=fkey@COL_EDIT,skey@COL_EDIT;@refCol=fkey@COL_TYPE,skey@COL_TYPE;@refCol=fkey@COL_DEF,skey@COL_DEFAULT;',' ',' ',' ',0,'2013-04-01 11:43:15','2014-07-14 19:28:13',' ',NULL,NULL,NULL,NULL),
	 ('SYS_OBJCOLS',NULL,'COL_CHECK','COL_CHECK',NULL,'C','A','NH','DW',1,0,0,'0','1','1','1','1',NULL,'0',30,NULL,'0','1',NULL,'0',NULL,'0','0',NULL,'0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,'2013-04-01 11:43:15','2013-04-01 11:43:15',NULL,NULL,NULL,NULL,NULL);
INSERT INTO SYS_OBJCOLS (OBJ_ID,COL_BASE,COL_ID,COL_MC,COL_DES,COL_TYPE,COL_APPTYPE1,COL_APPTYPE2,COL_CSTATT,COL_LEN,COL_PREC,COL_SCALE,COL_ISKEY,COL_ISNULL,COL_VISIBLE,COL_EDITABLE,COL_EDIT,COL_VIEW,COL_DEFAULT,COL_DISP,COL_ORDER,COL_MUNIT,COL_USE,COL_VALUE,COL_ISFKEY,COL_FOBJ,COL_LANG,COL_ISCALC,COL_COLGS,COL_CHECK,COL_REGEXREF,COL_ALIAS,COL_COLJY,COL_CONT,COL_EXT_ID,COL_OPT,COL_REF,F_LANG,F_GUID,F_STAU,F_CRDATE,F_CHDATE,COL_FKEY,SYS_ID,COL_ADD_LOG,F_CRUSER,F_CHUSER) VALUES
	 ('SYS_OBJCOLS',NULL,'COL_COLGS','COL_COLGS',NULL,'C','A','NH','DW',255,0,0,'0','1','1','1','1',NULL,NULL,29,NULL,'0','1',NULL,'0',NULL,'0','0',NULL,'0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,'2013-04-01 11:43:15','2013-04-01 11:43:15',NULL,NULL,NULL,NULL,NULL),
	 ('SYS_OBJCOLS',NULL,'COL_COLJY','COL_COLJY',NULL,'C','A','NH','DW',255,0,0,'0','1','1','1','1',NULL,NULL,33,NULL,'0','1',NULL,'0',NULL,'0','0',NULL,'0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,'2013-04-01 11:43:15','2013-04-01 11:43:15',NULL,NULL,NULL,NULL,NULL),
	 ('SYS_OBJCOLS',NULL,'COL_CONT','COL_CONT',NULL,'C','A','NH','DW',2,0,0,'0','1','1','1','1',NULL,NULL,34,NULL,'0','1',NULL,'0',NULL,'0','0',NULL,'0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,'2013-04-01 11:43:15','2013-04-01 11:43:15',NULL,NULL,NULL,NULL,NULL),
	 ('SYS_OBJCOLS',NULL,'COL_CSTATT','COL_CSTATT',NULL,'C','A','NH','DW',2,0,0,'0','0','1','1','1',NULL,NULL,9,NULL,'0','1',NULL,'0',NULL,'0','0',NULL,'0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,'2013-04-01 11:43:15','2013-04-01 11:43:15',NULL,NULL,NULL,NULL,NULL),
	 ('SYS_OBJCOLS',NULL,'COL_DEFAULT','COL_DEFAULT',NULL,'C','A','NH','DW',255,0,0,'0','1','1','1','1',NULL,NULL,19,NULL,'0','1',NULL,'0',NULL,'0','0',NULL,'0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,'2013-04-01 11:43:15','2013-04-01 11:43:15',NULL,NULL,NULL,NULL,NULL),
	 ('SYS_OBJCOLS',NULL,'COL_DES','COL_DES',NULL,'C','A','NH','DW',255,0,0,'0','1','1','1','1',NULL,NULL,5,NULL,'0','1',NULL,'0',NULL,'0','0',NULL,'0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,'2013-04-01 11:43:15','2013-04-01 11:43:15',NULL,NULL,NULL,NULL,NULL),
	 ('SYS_OBJCOLS',NULL,'COL_DISP','COL_DISP',NULL,'I','A','NH','DW',22,0,0,'0','0','1','1','1',NULL,NULL,20,NULL,'0','1',NULL,'0',NULL,'0','0',NULL,'0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,'2013-04-01 11:43:15','2013-04-01 11:43:15',NULL,NULL,NULL,NULL,NULL),
	 ('SYS_OBJCOLS',NULL,'COL_EDIT','COL_EDIT',NULL,'C','A','NH','DW',4,0,0,'0','1','1','1','1',NULL,NULL,17,NULL,'0','1',NULL,'0',NULL,'0','0',NULL,'0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,'2013-04-01 11:43:15','2013-04-01 11:43:15',NULL,NULL,NULL,NULL,NULL),
	 ('SYS_OBJCOLS',NULL,'COL_EDITABLE','COL_EDITABLE',NULL,'C','A','NH','DW',1,0,0,'0','0','1','1','1',NULL,'1',16,NULL,'0','1',NULL,'0',NULL,'0','0',NULL,'0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,'2013-04-01 11:43:15','2013-04-01 11:43:15',NULL,NULL,NULL,NULL,NULL),
	 ('SYS_OBJCOLS',NULL,'COL_EXT_ID','COL_EXT_ID',NULL,'C','A','NH','DW',30,0,0,'0','1','1','1','1',NULL,NULL,35,NULL,'0','1',NULL,'0',NULL,'0','0',NULL,'0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,'2013-04-01 11:43:15','2013-04-01 11:43:15',NULL,NULL,NULL,NULL,NULL);
INSERT INTO SYS_OBJCOLS (OBJ_ID,COL_BASE,COL_ID,COL_MC,COL_DES,COL_TYPE,COL_APPTYPE1,COL_APPTYPE2,COL_CSTATT,COL_LEN,COL_PREC,COL_SCALE,COL_ISKEY,COL_ISNULL,COL_VISIBLE,COL_EDITABLE,COL_EDIT,COL_VIEW,COL_DEFAULT,COL_DISP,COL_ORDER,COL_MUNIT,COL_USE,COL_VALUE,COL_ISFKEY,COL_FOBJ,COL_LANG,COL_ISCALC,COL_COLGS,COL_CHECK,COL_REGEXREF,COL_ALIAS,COL_COLJY,COL_CONT,COL_EXT_ID,COL_OPT,COL_REF,F_LANG,F_GUID,F_STAU,F_CRDATE,F_CHDATE,COL_FKEY,SYS_ID,COL_ADD_LOG,F_CRUSER,F_CHUSER) VALUES
	 ('SYS_OBJCOLS',NULL,'COL_FKEY','COL_FKEY',NULL,'C','A','NH','DW',30,0,0,'0','1','1','1','1',NULL,NULL,43,NULL,'0','1',NULL,'0',NULL,'0','0',NULL,'0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,'2013-04-01 11:43:15','2013-04-01 11:43:15',NULL,NULL,NULL,NULL,NULL),
	 ('SYS_OBJCOLS',NULL,'COL_FOBJ','COL_FOBJ',NULL,'C','A','NH','DW',30,0,0,'0','1','1','1','1',NULL,NULL,26,NULL,'0','1',NULL,'0',NULL,'0','0',NULL,'0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,'2013-04-01 11:43:15','2013-04-01 11:43:15',NULL,NULL,NULL,NULL,NULL),
	 ('SYS_OBJCOLS',NULL,'COL_ID','列标识',NULL,'C','A','NH','DW',30,100,0,'1','0','1','1','1',NULL,NULL,3,NULL,'0','1',NULL,'0',NULL,'0','0',NULL,'0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,'2013-04-01 11:43:15','2013-04-01 11:43:15',NULL,NULL,NULL,NULL,NULL),
	 ('SYS_OBJCOLS',NULL,'COL_ISCALC','COL_ISCALC',NULL,'C','A','NH','DW',1,0,0,'0','0','1','1','1',NULL,'0',28,NULL,'0','1',NULL,'0',NULL,'0','0',NULL,'0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,'2013-04-01 11:43:15','2013-04-01 11:43:15',NULL,NULL,NULL,NULL,NULL),
	 ('SYS_OBJCOLS',NULL,'COL_ISFKEY','COL_ISFKEY',NULL,'C','A','NH','DW',1,0,0,'0','1','1','1','1',NULL,'0',25,NULL,'0','1',NULL,'0',NULL,'0','0',NULL,'0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,'2013-04-01 11:43:15','2013-04-01 11:43:15',NULL,NULL,NULL,NULL,NULL),
	 ('SYS_OBJCOLS',NULL,'COL_ISKEY','COL_ISKEY',NULL,'C','A','NH','DW',1,0,0,'0','0','1','1','1',NULL,'0',13,NULL,'0','1',NULL,'0',NULL,'0','0',NULL,'0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,'2013-04-01 11:43:15','2013-04-01 11:43:15',NULL,NULL,NULL,NULL,NULL),
	 ('SYS_OBJCOLS',NULL,'COL_ISNULL','COL_ISNULL',NULL,'C','A','NH','DW',1,0,0,'0','0','1','1','1',NULL,'1',14,NULL,'0','1',NULL,'0',NULL,'0','0',NULL,'0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,'2013-04-01 11:43:15','2013-04-01 11:43:15',NULL,NULL,NULL,NULL,NULL),
	 ('SYS_OBJCOLS',NULL,'COL_LANG','COL_LANG',NULL,'C','A','NH','DW',1,0,0,'0','1','1','1','1',NULL,'0',27,NULL,'0','1',NULL,'0',NULL,'0','0',NULL,'0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,'2013-04-01 11:43:15','2013-04-01 11:43:15',NULL,NULL,NULL,NULL,NULL),
	 ('SYS_OBJCOLS',NULL,'COL_LEN','COL_LEN',NULL,'I','A','NH','DW',22,0,0,'0','0','1','1','1',NULL,NULL,10,NULL,'0','1',NULL,'0',NULL,'0','0',NULL,'0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,'2013-04-01 11:43:15','2013-04-01 11:43:15',NULL,NULL,NULL,NULL,NULL),
	 ('SYS_OBJCOLS',NULL,'COL_MC','列名称',NULL,'C','A','NH','DW',255,100,0,'0','0','1','1','1',NULL,NULL,4,NULL,'0','1',NULL,'0',NULL,'0','0',NULL,'0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,'2013-04-01 11:43:15','2013-04-01 11:43:15',NULL,NULL,NULL,NULL,NULL);
INSERT INTO SYS_OBJCOLS (OBJ_ID,COL_BASE,COL_ID,COL_MC,COL_DES,COL_TYPE,COL_APPTYPE1,COL_APPTYPE2,COL_CSTATT,COL_LEN,COL_PREC,COL_SCALE,COL_ISKEY,COL_ISNULL,COL_VISIBLE,COL_EDITABLE,COL_EDIT,COL_VIEW,COL_DEFAULT,COL_DISP,COL_ORDER,COL_MUNIT,COL_USE,COL_VALUE,COL_ISFKEY,COL_FOBJ,COL_LANG,COL_ISCALC,COL_COLGS,COL_CHECK,COL_REGEXREF,COL_ALIAS,COL_COLJY,COL_CONT,COL_EXT_ID,COL_OPT,COL_REF,F_LANG,F_GUID,F_STAU,F_CRDATE,F_CHDATE,COL_FKEY,SYS_ID,COL_ADD_LOG,F_CRUSER,F_CHUSER) VALUES
	 ('SYS_OBJCOLS',NULL,'COL_MUNIT','COL_MUNIT',NULL,'C','A','NH','DW',1,0,0,'0','0','1','1','1',NULL,'0',22,NULL,'0','1',NULL,'0',NULL,'0','0',NULL,'0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,'2013-04-01 11:43:15','2013-04-01 11:43:15',NULL,NULL,NULL,NULL,NULL),
	 ('SYS_OBJCOLS',NULL,'COL_OPT','COL_OPT',NULL,'C','A','NH','DW',1000,0,0,'0','1','1','1','1',NULL,NULL,36,NULL,'0','1',NULL,'0',NULL,'0','0',NULL,'0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,'2013-04-01 11:43:15','2013-04-01 11:43:15',NULL,NULL,NULL,NULL,NULL),
	 ('SYS_OBJCOLS',NULL,'COL_ORDER','COL_ORDER',NULL,'C','A','NH','DW',1,0,0,'0','1','1','1','1',NULL,NULL,21,NULL,'0','1',NULL,'0',NULL,'0','0',NULL,'0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,'2013-04-01 11:43:15','2013-04-01 11:43:15',NULL,NULL,NULL,NULL,NULL),
	 ('SYS_OBJCOLS',NULL,'COL_PREC','COL_PREC',NULL,'I','A','NH','DW',22,0,0,'0','0','1','1','1',NULL,NULL,11,NULL,'0','1',NULL,'0',NULL,'0','0',NULL,'0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,'2013-04-01 11:43:15','2013-04-01 11:43:15',NULL,NULL,NULL,NULL,NULL),
	 ('SYS_OBJCOLS',NULL,'COL_REF','COL_REF',NULL,'C','A','NH','DW',30,0,0,'0','1','1','1','1',NULL,NULL,37,NULL,'0','1',NULL,'0',NULL,'0','0',NULL,'0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,'2013-04-01 11:43:15','2013-04-01 11:43:15',NULL,NULL,NULL,NULL,NULL),
	 ('SYS_OBJCOLS',NULL,'COL_REGEXREF','COL_REGEXREF',NULL,'C','A','NH','DW',30,0,0,'0','1','1','1','1',NULL,NULL,31,NULL,'0','1',NULL,'0',NULL,'0','0',NULL,'0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,'2013-04-01 11:43:15','2013-04-01 11:43:15',NULL,NULL,NULL,NULL,NULL),
	 ('SYS_OBJCOLS',NULL,'COL_SCALE','COL_SCALE',NULL,'I','A','NH','DW',22,0,0,'0','0','1','1','1',NULL,NULL,12,NULL,'0','1',NULL,'0',NULL,'0','0',NULL,'0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,'2013-04-01 11:43:15','2013-04-01 11:43:15',NULL,NULL,NULL,NULL,NULL),
	 ('SYS_OBJCOLS',NULL,'COL_TYPE','COL_TYPE',NULL,'C','A','NH','DW',2,0,0,'0','0','1','1','1',NULL,NULL,6,NULL,'0','1',NULL,'0',NULL,'0','0',NULL,'0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,'2013-04-01 11:43:15','2013-04-01 11:43:15',NULL,NULL,NULL,NULL,NULL),
	 ('SYS_OBJCOLS',NULL,'COL_USE','COL_USE',NULL,'C','A','NH','DW',1,0,0,'0','0','1','1','1',NULL,'1',23,NULL,'0','1',NULL,'0',NULL,'0','0',NULL,'0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,'2013-04-01 11:43:15','2013-04-01 11:43:15',NULL,NULL,NULL,NULL,NULL),
	 ('SYS_OBJCOLS',NULL,'COL_VALUE','COL_VALUE',NULL,'C','A','NH','DW',1,0,0,'0','1','1','1','1',NULL,NULL,24,NULL,'0','1',NULL,'0',NULL,'0','0',NULL,'0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,'2013-04-01 11:43:15','2013-04-01 11:43:15',NULL,NULL,NULL,NULL,NULL);
INSERT INTO SYS_OBJCOLS (OBJ_ID,COL_BASE,COL_ID,COL_MC,COL_DES,COL_TYPE,COL_APPTYPE1,COL_APPTYPE2,COL_CSTATT,COL_LEN,COL_PREC,COL_SCALE,COL_ISKEY,COL_ISNULL,COL_VISIBLE,COL_EDITABLE,COL_EDIT,COL_VIEW,COL_DEFAULT,COL_DISP,COL_ORDER,COL_MUNIT,COL_USE,COL_VALUE,COL_ISFKEY,COL_FOBJ,COL_LANG,COL_ISCALC,COL_COLGS,COL_CHECK,COL_REGEXREF,COL_ALIAS,COL_COLJY,COL_CONT,COL_EXT_ID,COL_OPT,COL_REF,F_LANG,F_GUID,F_STAU,F_CRDATE,F_CHDATE,COL_FKEY,SYS_ID,COL_ADD_LOG,F_CRUSER,F_CHUSER) VALUES
	 ('SYS_OBJCOLS',NULL,'COL_VIEW','COL_VIEW',NULL,'C','A','NH','DW',1000,0,0,'0','1','1','1','1',NULL,NULL,18,NULL,'0','1',NULL,'0',NULL,'0','0',NULL,'0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,'2013-04-01 11:43:15','2013-04-01 11:43:15',NULL,NULL,NULL,NULL,NULL),
	 ('SYS_OBJCOLS',NULL,'COL_VISIBLE','COL_VISIBLE',NULL,'C','A','NH','DW',1,0,0,'0','0','1','1','1',NULL,'1',15,NULL,'0','1',NULL,'0',NULL,'0','0',NULL,'0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,'2013-04-01 11:43:15','2013-04-01 11:43:15',NULL,NULL,NULL,NULL,NULL),
	 ('SYS_OBJCOLS','F_CHDATE','F_CHDATE','修改时间','维表、关系表、事实表 都需要的列','T','A','NH','DW',19,100,1,'0','1','1','1','1',' ',' ',7,'','0','1','','0',' ','0','0',' ','0',' ',' ',' ',' ',' ',' ',' ',' ',' ',1,'2021-01-09 12:50:53','2021-09-26 14:36:49',' ',' ',' ',NULL,NULL),
	 ('SYS_OBJCOLS',' ','F_CHUSER','修改人',' ','C','A','NH','DW',30,100,1,'0','1','1','1','1',' ',' ',11,'','0','1','','1','BSUSER','0','0',' ','0',' ',' ',' ',' ',' ',' ',' ',' ',' ',1,'2021-09-22 15:35:28','2021-09-23 15:41:46',' ',NULL,NULL,NULL,NULL),
	 ('SYS_OBJCOLS','F_CRDATE','F_CRDATE','创建时间','维表、关系表、事实表 都需要的列','T','A','NH','DW',19,100,1,'0','1','1','1','1',' ',' ',7,'','0','1','','0',' ','0','0',' ','0',' ',' ',' ',' ',' ',' ',' ',' ',' ',1,'2021-01-09 12:50:53','2021-09-26 14:36:49',' ',' ',' ',NULL,NULL),
	 ('SYS_OBJCOLS',' ','F_CRUSER','创建人',' ','C','A','NH','DW',30,100,1,'0','1','1','1','1',' ',' ',11,'','0','1','','1','BSUSER','0','0',' ','0',' ',' ',' ',' ',' ',' ',' ',' ',' ',1,'2021-09-22 15:35:28','2021-09-23 15:41:46',' ',NULL,NULL,NULL,NULL),
	 ('SYS_OBJCOLS',NULL,'F_GUID','F_GUID',NULL,'C','A','NH','DW',30,0,0,'0','1','1','1','1',NULL,NULL,39,NULL,'0','1',NULL,'0',NULL,'0','0',NULL,'0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,'2013-04-01 11:43:15','2013-04-01 11:43:15',NULL,NULL,NULL,NULL,NULL),
	 ('SYS_OBJCOLS',NULL,'F_LANG','F_LANG',NULL,'C','A','NH','DW',30,0,0,'0','1','1','1','1',NULL,NULL,38,NULL,'0','1',NULL,'0',NULL,'0','0',NULL,'0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,'2013-04-01 11:43:15','2013-04-01 11:43:15',NULL,NULL,NULL,NULL,NULL),
	 ('SYS_OBJCOLS',NULL,'F_STAU','F_STAU',NULL,'I','A','NH','DW',22,0,0,'0','0','1','1','1',NULL,'0',40,NULL,'0','1',NULL,'0',NULL,'0','0',NULL,'0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,'2013-04-01 11:43:15','2013-04-01 11:43:15',NULL,NULL,NULL,NULL,NULL),
	 ('SYS_OBJCOLS',NULL,'OBJ_ID','OBJ_ID',NULL,'C','A','NH','DW',30,0,0,'1','0','1','1','1',NULL,NULL,1,NULL,'0','1',NULL,'0',NULL,'0','0',NULL,'0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,'2013-04-01 11:43:15','2013-04-01 11:43:15',NULL,NULL,NULL,NULL,NULL);
INSERT INTO SYS_OBJCOLS (OBJ_ID,COL_BASE,COL_ID,COL_MC,COL_DES,COL_TYPE,COL_APPTYPE1,COL_APPTYPE2,COL_CSTATT,COL_LEN,COL_PREC,COL_SCALE,COL_ISKEY,COL_ISNULL,COL_VISIBLE,COL_EDITABLE,COL_EDIT,COL_VIEW,COL_DEFAULT,COL_DISP,COL_ORDER,COL_MUNIT,COL_USE,COL_VALUE,COL_ISFKEY,COL_FOBJ,COL_LANG,COL_ISCALC,COL_COLGS,COL_CHECK,COL_REGEXREF,COL_ALIAS,COL_COLJY,COL_CONT,COL_EXT_ID,COL_OPT,COL_REF,F_LANG,F_GUID,F_STAU,F_CRDATE,F_CHDATE,COL_FKEY,SYS_ID,COL_ADD_LOG,F_CRUSER,F_CHUSER) VALUES
	 ('SYS_OBJCOLS',' ','SYS_ID',' ',' ','C','A','NH','DW',30,100,1,'0','1','1','1','1',' ',' ',11,'','0','1','','1','BSUSER','0','0',' ','0',' ',' ',' ',' ',' ',' ',' ',' ',' ',1,'2021-09-22 15:35:28','2021-09-23 15:41:46',' ',NULL,NULL,NULL,NULL),
	 ('SYS_OBJECTS','F_CHDATE','F_CHDATE','修改时间','维表、关系表、事实表 都需要的列','T','A','NH','DW',19,100,1,'0','1','1','1','1',' ',' ',7,'','0','1','','0',' ','0','0',' ','0',' ',' ',' ',' ',' ',' ',' ',' ',' ',1,'2021-01-09 12:50:53','2021-09-26 14:36:49',' ',' ',' ',NULL,NULL),
	 ('SYS_OBJECTS',' ','F_CHUSER','修改人',' ','C','A','NH','DW',30,100,1,'0','1','1','1','1',' ',' ',11,'','0','1','','1','BSUSER','0','0',' ','0',' ',' ',' ',' ',' ',' ',' ',' ',' ',1,'2021-09-22 15:35:28','2021-09-23 15:41:46',' ',NULL,NULL,NULL,NULL),
	 ('SYS_OBJECTS','F_CRDATE','F_CRDATE','创建时间','维表、关系表、事实表 都需要的列','T','A','NH','DW',19,100,1,'0','1','1','1','1',' ',' ',7,'','0','1','','0',' ','0','0',' ','0',' ',' ',' ',' ',' ',' ',' ',' ',' ',1,'2021-01-09 12:50:53','2021-09-26 14:36:49',' ',' ',' ',NULL,NULL),
	 ('SYS_OBJECTS',' ','F_CRUSER','创建人',' ','C','A','NH','DW',30,100,1,'0','1','1','1','1',' ',' ',11,'','0','1','','1','BSUSER','0','0',' ','0',' ',' ',' ',' ',' ',' ',' ',' ',' ',1,'2021-09-22 15:35:28','2021-09-23 15:41:46',' ',NULL,NULL,NULL,NULL),
	 ('SYS_OBJECTS',' ','F_ENABLE ','是否启用',' ','C','A','NH','DW',1,100,1,'0','1','1','1','1',' ',' ',22,'','0','1','','0',' ','0','0',' ','0',' ',' ',' ',' ',' ',' ',' ',' ',' ',1,'2021-09-22 15:35:28','2021-09-22 15:35:28',' ',NULL,NULL,NULL,NULL),
	 ('SYS_OBJECTS',NULL,'F_GUID','F_GUID',NULL,'C','A','NH','DW',30,0,0,'0','1','1','1','1',NULL,NULL,18,NULL,'0','1',NULL,'0',NULL,'0','0',NULL,'0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,'2013-04-01 11:44:02','2013-08-20 16:42:13',NULL,NULL,NULL,NULL,NULL),
	 ('SYS_OBJECTS',NULL,'F_STAU','F_STAU',NULL,'I','A','NH','DW',22,0,0,'0','0','1','1','1',NULL,'0',20,NULL,'0','1',NULL,'0',NULL,'0','0',NULL,'0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,'2013-04-01 11:44:02','2013-08-20 16:42:13',NULL,NULL,NULL,NULL,NULL),
	 ('SYS_OBJECTS',NULL,'INDEX_SPACE','INDEX_SPACE',NULL,'C','A','NH','DW',30,16,2,'0','1','1','1','1',NULL,NULL,27,NULL,'0','1',NULL,'0',NULL,'0','0',NULL,'0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,'2013-08-20 16:42:13','2013-08-20 16:42:13',NULL,NULL,NULL,NULL,NULL),
	 ('SYS_OBJECTS',NULL,'OBJ_APPTYPE','OBJ_APPTYPE',NULL,'C','A','NH','DW',3,0,0,'0','0','1','1','1',NULL,NULL,5,NULL,'0','1',NULL,'0',NULL,'0','0',NULL,'0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,'2013-04-01 11:44:02','2013-08-20 16:42:13',NULL,NULL,NULL,NULL,NULL);
INSERT INTO SYS_OBJCOLS (OBJ_ID,COL_BASE,COL_ID,COL_MC,COL_DES,COL_TYPE,COL_APPTYPE1,COL_APPTYPE2,COL_CSTATT,COL_LEN,COL_PREC,COL_SCALE,COL_ISKEY,COL_ISNULL,COL_VISIBLE,COL_EDITABLE,COL_EDIT,COL_VIEW,COL_DEFAULT,COL_DISP,COL_ORDER,COL_MUNIT,COL_USE,COL_VALUE,COL_ISFKEY,COL_FOBJ,COL_LANG,COL_ISCALC,COL_COLGS,COL_CHECK,COL_REGEXREF,COL_ALIAS,COL_COLJY,COL_CONT,COL_EXT_ID,COL_OPT,COL_REF,F_LANG,F_GUID,F_STAU,F_CRDATE,F_CHDATE,COL_FKEY,SYS_ID,COL_ADD_LOG,F_CRUSER,F_CHUSER) VALUES
	 ('SYS_OBJECTS',NULL,'OBJ_BUILDNO','OBJ_BUILDNO',NULL,'C','A','NH','DW',4,0,0,'0','0','1','1','1',NULL,'0000',6,NULL,'0','1',NULL,'0',NULL,'0','0',NULL,'0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,'2013-04-01 11:44:02','2013-08-20 16:42:13',NULL,NULL,NULL,NULL,NULL),
	 ('SYS_OBJECTS',NULL,'OBJ_CSTCOL','OBJ_CSTCOL',NULL,'C','A','NH','DW',1,0,0,'0','0','1','1','1',NULL,'1',11,NULL,'0','1',NULL,'0',NULL,'0','0',NULL,'0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,'2013-04-01 11:44:02','2013-08-20 16:42:13',NULL,NULL,NULL,NULL,NULL),
	 ('SYS_OBJECTS',' ','OBJ_DES','OBJ_DES',' ','C','A','NH','DW',255,100,0,'0','1','1','1','1',' ',' ',3,'','0','1','','0',' ','0','0',' ','0',' ',' ',' ',' ',' ',' ',' ',' ',' ',0,'2013-04-01 11:44:02','2014-05-28 22:52:12',' ',NULL,NULL,NULL,NULL),
	 ('SYS_OBJECTS',NULL,'OBJ_DTRG','OBJ_DTRG',NULL,'C','A','NH','DW',30,0,0,'0','1','1','1','1',NULL,NULL,14,NULL,'0','1',NULL,'0',NULL,'0','0',NULL,'0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,'2013-04-01 11:44:02','2013-08-20 16:42:13',NULL,NULL,NULL,NULL,NULL),
	 ('SYS_OBJECTS',' ','OBJ_GROUP','OBJ_GROUP',' ','C','A','NH','DW',100,200,1,'0','1','1','1','1',' ',' ',32,'','0','1','','0',' ','0','0',' ','0',' ',' ',' ',' ',' ',' ',' ',' ',' ',1,'2023-07-10 11:47:22','2023-07-10 11:47:22',' ',' ',' ','9999',' '),
	 ('SYS_OBJECTS',' ','OBJ_ID','字典编号',' ','C','A','NH','DW',30,300,0,'1','0','1','1','1',' ',' ',0,'','0','1','','0',' ','0','0',' ','0',' ',' ',' ',' ',' ',' ',' ',' ',' ',0,'2013-04-01 11:44:02','2014-06-06 00:18:45',' ',NULL,NULL,NULL,NULL),
	 ('SYS_OBJECTS',NULL,'OBJ_ITRG','OBJ_ITRG',NULL,'C','A','NH','DW',30,0,0,'0','1','1','1','1',NULL,NULL,13,NULL,'0','1',NULL,'0',NULL,'0','0',NULL,'0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,'2013-04-01 11:44:02','2013-08-20 16:42:13',NULL,NULL,NULL,NULL,NULL),
	 ('SYS_OBJECTS',NULL,'OBJ_LANG','OBJ_LANG',NULL,'C','A','NH','DW',1,0,0,'0','0','1','1','1',NULL,'0',9,NULL,'0','1',NULL,'0',NULL,'0','0',NULL,'0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,'2013-04-01 11:44:02','2013-08-20 16:42:13',NULL,NULL,NULL,NULL,NULL),
	 ('SYS_OBJECTS',' ','OBJ_LANGINIT','多语言初始化',' ','C','A','NH','DW',1,100,1,'0','1','1','1','4',' ','0',0,'','0','1','','0',' ','0','0',' ','0',' ',' ',' ',' ',' ',' ',' ',' ',' ',1,'2021-10-19 16:31:44','2021-10-19 16:31:44',' ',NULL,NULL,NULL,NULL),
	 ('SYS_OBJECTS',' ','OBJ_MC','字典名称',' ','C','A','NH','DW',255,300,0,'0','0','1','1','1',' ',' ',1,'','0','1','','0',' ','0','0',' ','0',' ',' ',' ',' ',' ',' ',' ',' ',' ',0,'2013-04-01 11:44:02','2014-06-06 00:18:45',' ',NULL,NULL,NULL,NULL);
INSERT INTO SYS_OBJCOLS (OBJ_ID,COL_BASE,COL_ID,COL_MC,COL_DES,COL_TYPE,COL_APPTYPE1,COL_APPTYPE2,COL_CSTATT,COL_LEN,COL_PREC,COL_SCALE,COL_ISKEY,COL_ISNULL,COL_VISIBLE,COL_EDITABLE,COL_EDIT,COL_VIEW,COL_DEFAULT,COL_DISP,COL_ORDER,COL_MUNIT,COL_USE,COL_VALUE,COL_ISFKEY,COL_FOBJ,COL_LANG,COL_ISCALC,COL_COLGS,COL_CHECK,COL_REGEXREF,COL_ALIAS,COL_COLJY,COL_CONT,COL_EXT_ID,COL_OPT,COL_REF,F_LANG,F_GUID,F_STAU,F_CRDATE,F_CHDATE,COL_FKEY,SYS_ID,COL_ADD_LOG,F_CRUSER,F_CHUSER) VALUES
	 ('SYS_OBJECTS',NULL,'OBJ_MLID','OBJ_MLID',NULL,'C','A','NH','DW',30,0,0,'0','1','1','1','1',NULL,NULL,17,NULL,'0','1',NULL,'0',NULL,'0','0',NULL,'0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,'2013-04-01 11:44:02','2013-08-20 16:42:13',NULL,NULL,NULL,NULL,NULL),
	 ('SYS_OBJECTS',NULL,'OBJ_MUNIT','OBJ_MUNIT',NULL,'C','A','NH','DW',1,0,0,'0','0','1','1','1',NULL,'0',10,NULL,'0','1',NULL,'0',NULL,'0','0',NULL,'0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,'2013-04-01 11:44:02','2013-08-20 16:42:13',NULL,NULL,NULL,NULL,NULL),
	 ('SYS_OBJECTS',' ','OBJ_NAME','OBJ_NAME',' ','C','A','NH','DW',255,100,0,'0','1','1','1','1',' ',' ',2,'','0','1','','0',' ','0','0',' ','0',' ',' ',' ',' ',' ',' ',' ',' ',' ',0,'2013-04-01 11:44:02','2014-05-28 22:52:12',' ',NULL,NULL,NULL,NULL),
	 ('SYS_OBJECTS',NULL,'OBJ_PKEYS','OBJ_PKEYS',NULL,'C','A','NH','DW',255,0,0,'0','1','1','1','1',NULL,NULL,8,NULL,'0','1',NULL,'0',NULL,'0','0',NULL,'0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,'2013-04-01 11:44:02','2013-08-20 16:42:13',NULL,NULL,NULL,NULL,NULL),
	 ('SYS_OBJECTS',NULL,'OBJ_REF','OBJ_REF',NULL,'C','A','NH','DW',30,0,0,'0','1','1','1','1',NULL,NULL,19,NULL,'0','1',NULL,'0',NULL,'0','0',NULL,'0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,'2013-04-01 11:44:02','2013-08-20 16:42:13',NULL,NULL,NULL,NULL,NULL),
	 ('SYS_OBJECTS',NULL,'OBJ_STRG','OBJ_STRG',NULL,'C','A','NH','DW',30,0,0,'0','1','1','1','1',NULL,NULL,15,NULL,'0','1',NULL,'0',NULL,'0','0',NULL,'0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,'2013-04-01 11:44:02','2013-08-20 16:42:13',NULL,NULL,NULL,NULL,NULL),
	 ('SYS_OBJECTS',NULL,'OBJ_TEMP','OBJ_TEMP',NULL,'C','A','NH','DW',30,0,0,'0','1','1','1','1',NULL,NULL,16,NULL,'0','1',NULL,'0',NULL,'0','0',NULL,'0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,'2013-04-01 11:44:02','2013-08-20 16:42:13',NULL,NULL,NULL,NULL,NULL),
	 ('SYS_OBJECTS',' ','OBJ_TEMPTYPE','OBJ_TEMPTYPE',' ','C','A','NH','DW',1,200,1,'0','1','1','1','1',' ',' ',32,'','0','1','','0',' ','0','0',' ','0',' ',' ',' ',' ',' ',' ',' ',' ',' ',1,'2023-07-10 11:47:22','2023-07-10 11:47:22',' ',' ',' ','9999',' '),
	 ('SYS_OBJECTS',' ','OBJ_TYPE','OBJ_TYPE',' ','C','A','NH','DW',1,100,0,'0','0','1','1','1',' ',' ',4,'','0','1','','0',' ','0','0',' ','0',' ',' ',' ',' ',' ',' ',' ',' ',' ',0,'2013-04-01 11:44:02','2014-05-28 22:52:12',' ',NULL,NULL,NULL,NULL),
	 ('SYS_OBJECTS',NULL,'OBJ_UNIT','OBJ_UNIT',NULL,'C','A','NH','DW',1,0,0,'0','0','1','1','1',NULL,'0',23,NULL,'0','1',NULL,'0',NULL,'0','0',NULL,'0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,'2013-04-01 11:44:02','2013-08-20 16:42:13',NULL,NULL,NULL,NULL,NULL);
INSERT INTO SYS_OBJCOLS (OBJ_ID,COL_BASE,COL_ID,COL_MC,COL_DES,COL_TYPE,COL_APPTYPE1,COL_APPTYPE2,COL_CSTATT,COL_LEN,COL_PREC,COL_SCALE,COL_ISKEY,COL_ISNULL,COL_VISIBLE,COL_EDITABLE,COL_EDIT,COL_VIEW,COL_DEFAULT,COL_DISP,COL_ORDER,COL_MUNIT,COL_USE,COL_VALUE,COL_ISFKEY,COL_FOBJ,COL_LANG,COL_ISCALC,COL_COLGS,COL_CHECK,COL_REGEXREF,COL_ALIAS,COL_COLJY,COL_CONT,COL_EXT_ID,COL_OPT,COL_REF,F_LANG,F_GUID,F_STAU,F_CRDATE,F_CHDATE,COL_FKEY,SYS_ID,COL_ADD_LOG,F_CRUSER,F_CHUSER) VALUES
	 ('SYS_OBJECTS',NULL,'OBJ_UTRG','OBJ_UTRG',NULL,'C','A','NH','DW',30,0,0,'0','1','1','1','1',NULL,NULL,12,NULL,'0','1',NULL,'0',NULL,'0','0',NULL,'0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,'2013-04-01 11:44:02','2013-08-20 16:42:13',NULL,NULL,NULL,NULL,NULL),
	 ('SYS_OBJECTS',' ','SYS_ID','SYS_ID',' ','C','A','NH','DW',30,0,0,'0','0','1','1','1',' ',' ',7,'','0','1','','1','SYS_LIST','0','0',' ','0',' ',' ',' ',' ',' ',' ',' ',' ',' ',0,'2013-04-01 11:44:02','2014-05-26 16:40:40',' ',NULL,NULL,NULL,NULL),
	 ('SYS_OBJECTS',NULL,'TABLE_SPACE','TABLE_SPACE',NULL,'C','A','NH','DW',30,16,2,'0','1','1','1','1',NULL,NULL,26,NULL,'0','1',NULL,'0',NULL,'0','0',NULL,'0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,'2013-08-20 16:42:13','2013-08-20 16:42:13',NULL,NULL,NULL,NULL,NULL),
	 ('SYS_OBJECTS',NULL,'UNIT_ID','UNIT_ID',NULL,'C','A','NH','DW',30,0,0,'0','1','1','1','1',NULL,NULL,24,NULL,'0','1',NULL,'0',NULL,'0','0',NULL,'0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,'2013-04-01 11:44:02','2013-08-20 16:42:13',NULL,NULL,NULL,NULL,NULL),
	 ('SYS_OBJ_VAL',NULL,'F_NOTE','F_NOTE',NULL,'C','A','NH','DW',255,0,0,'0','1','1','1','1',NULL,NULL,4,NULL,'0','1',NULL,'0',NULL,'0','0',NULL,'0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,'2013-04-01 11:44:47','2013-04-01 11:44:47',NULL,NULL,NULL,NULL,NULL),
	 ('SYS_OBJ_VAL',NULL,'OBJ_ID','OBJ_ID',NULL,'C','A','NH','DW',30,0,0,'1','0','1','1','1',NULL,NULL,1,NULL,'0','1',NULL,'0',NULL,'0','0',NULL,'0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,'2013-04-01 11:44:47','2013-04-01 11:44:47',NULL,NULL,NULL,NULL,NULL),
	 ('SYS_OBJ_VAL',' ','OBJ_KEY','属性名称',' ','C','A','NH','DW',30,100,1,'1','1','1','1','1',' ',' ',11,'','0','1','','0','0','0','0',' ','0',' ',' ',' ',' ',' ',' ',' ',' ',' ',1,'2021-09-22 15:35:28','2021-09-23 15:41:46',' ',NULL,NULL,NULL,NULL),
	 ('SYS_OBJ_VAL',NULL,'OBJ_VALUE','OBJ_VALUE',NULL,'C','A','NH','DW',255,0,0,'0','1','1','1','1',NULL,NULL,3,NULL,'0','1',NULL,'0',NULL,'0','0',NULL,'0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,'2013-04-01 11:44:47','2013-04-01 11:44:47',NULL,NULL,NULL,NULL,NULL),
	 ('SYS_OPLOG',' ','F_CLIENT','F_CLIENT',' ','C','A','NH','DW',30,100,1,'0','1','1','1','1',' ',' ',10,'','0','1','','0',' ','0','0',' ','0',' ',' ',' ',' ',' ',' ',' ',' ',' ',1,'2018-07-13 20:36:24','2018-07-13 20:36:24',' ',' ',' ',NULL,NULL),
	 ('SYS_OPLOG',' ','F_CONTENTVIEW','包含表单id',' ','C','A','NH','DW',50,100,1,'0','1','1','1','1',' ',' ',22,'','0','1','','0',' ','0','0',' ','0',' ',' ',' ',' ',' ',' ',' ',' ',' ',1,'2020-03-30 17:10:03','2020-03-30 17:10:03',' ',' ',' ',NULL,NULL);
INSERT INTO SYS_OBJCOLS (OBJ_ID,COL_BASE,COL_ID,COL_MC,COL_DES,COL_TYPE,COL_APPTYPE1,COL_APPTYPE2,COL_CSTATT,COL_LEN,COL_PREC,COL_SCALE,COL_ISKEY,COL_ISNULL,COL_VISIBLE,COL_EDITABLE,COL_EDIT,COL_VIEW,COL_DEFAULT,COL_DISP,COL_ORDER,COL_MUNIT,COL_USE,COL_VALUE,COL_ISFKEY,COL_FOBJ,COL_LANG,COL_ISCALC,COL_COLGS,COL_CHECK,COL_REGEXREF,COL_ALIAS,COL_COLJY,COL_CONT,COL_EXT_ID,COL_OPT,COL_REF,F_LANG,F_GUID,F_STAU,F_CRDATE,F_CHDATE,COL_FKEY,SYS_ID,COL_ADD_LOG,F_CRUSER,F_CHUSER) VALUES
	 ('SYS_OPLOG',' ','F_ETIME','F_ETIME',' ','C','A','NH','DW',30,0,0,'0','1','1','1','1',' ',' ',6,'','0','1','','0',' ','0','0',' ','0',' ',' ',' ',' ',' ',' ',' ',' ',' ',0,'2018-07-13 20:36:24','2018-07-13 20:36:24',' ',' ',' ',NULL,NULL),
	 ('SYS_OPLOG',' ','F_FORMS','表单id',' ','C','A','NH','DW',100,100,1,'0','1','1','1','1',' ',' ',19,'','0','1','','0',' ','0','0',' ','0',' ',' ',' ',' ',' ',' ',' ',' ',' ',1,'2020-03-30 17:10:03','2020-03-30 17:10:03',' ',' ',' ',NULL,NULL),
	 ('SYS_OPLOG',' ','F_GNBH','功能编号',' ','C','A','NH','DW',200,100,0,'1','0','1','1','1',' ',' ',0,'','0','1','','0',' ','0','0',' ','0',' ',' ',' ',' ',' ',' ',' ',' ',' ',0,'2013-04-01 09:42:01','2021-08-31 19:17:22',' ',NULL,NULL,NULL,NULL),
	 ('SYS_OPLOG',' ','F_GNMC','F_GNMC',' ','C','A','NH','DW',255,0,0,'0','1','1','1','1',' ',' ',3,'','0','1','','0',' ','0','0',' ','0',' ',' ',' ',' ',' ',' ',' ',' ',' ',0,'2018-07-13 20:36:24','2018-07-13 20:36:24',' ',' ',' ',NULL,NULL),
	 ('SYS_OPLOG',' ','F_GNMK','功能模块',' ','C','A','NH','DW',30,100,1,'0','1','1','1','1',' ',' ',7,'','0','1','','0',' ','0','0',' ','0',' ',' ',' ',' ',' ',' ',' ',' ',' ',1,'2018-07-13 20:36:24','2018-07-13 20:36:24',' ',' ',' ',NULL,NULL),
	 ('SYS_OPLOG',' ','F_IP','F_IP',' ','C','A','NH','DW',30,100,1,'0','1','1','1','1',' ',' ',11,'','0','1','','0',' ','0','0',' ','0',' ',' ',' ',' ',' ',' ',' ',' ',' ',1,'2018-07-13 20:36:24','2018-07-13 20:36:24',' ',' ',' ',NULL,NULL),
	 ('SYS_OPLOG',' ','F_MAC','MAC地址',' ','C','A','NH','DW',100,100,1,'0','1','1','1','1',' ',' ',22,'','0','1','','0',' ','0','0',' ','0',' ',' ',' ',' ',' ',' ',' ',' ',' ',1,'2020-08-25 20:28:18','2020-08-30 16:36:20',' ',' ',NULL,NULL,NULL),
	 ('SYS_OPLOG',' ','F_NAME','F_NAME',' ','C','A','NH','DW',30,100,1,'0','1','1','1','1',' ',' ',9,'','0','1','','0',' ','0','0',' ','0',' ',' ',' ',' ',' ',' ',' ',' ',' ',1,'2018-07-13 20:36:24','2018-07-13 20:36:24',' ',' ',' ',NULL,NULL),
	 ('SYS_OPLOG',' ','F_NODEID','节点id',' ','C','A','NH','DW',50,100,1,'0','1','1','1','1',' ',' ',18,'','0','1','','0',' ','0','0',' ','0',' ',' ',' ',' ',' ',' ',' ',' ',' ',1,'2020-03-30 17:10:03','2020-03-30 17:10:03',' ',' ',' ',NULL,NULL),
	 ('SYS_OPLOG',' ','F_PREPAREVIEW','预开表单id',' ','C','A','NH','DW',50,100,1,'0','1','1','1','1',' ',' ',20,'','0','1','','0',' ','0','0',' ','0',' ',' ',' ',' ',' ',' ',' ',' ',' ',1,'2020-03-30 17:10:03','2020-03-30 17:10:03',' ',' ',' ',NULL,NULL);
INSERT INTO SYS_OBJCOLS (OBJ_ID,COL_BASE,COL_ID,COL_MC,COL_DES,COL_TYPE,COL_APPTYPE1,COL_APPTYPE2,COL_CSTATT,COL_LEN,COL_PREC,COL_SCALE,COL_ISKEY,COL_ISNULL,COL_VISIBLE,COL_EDITABLE,COL_EDIT,COL_VIEW,COL_DEFAULT,COL_DISP,COL_ORDER,COL_MUNIT,COL_USE,COL_VALUE,COL_ISFKEY,COL_FOBJ,COL_LANG,COL_ISCALC,COL_COLGS,COL_CHECK,COL_REGEXREF,COL_ALIAS,COL_COLJY,COL_CONT,COL_EXT_ID,COL_OPT,COL_REF,F_LANG,F_GUID,F_STAU,F_CRDATE,F_CHDATE,COL_FKEY,SYS_ID,COL_ADD_LOG,F_CRUSER,F_CHUSER) VALUES
	 ('SYS_OPLOG',' ','F_PRODUCT','F_PRODUCT',' ','C','A','NH','DW',30,100,1,'0','1','1','1','1',' ',' ',17,'','0','1','','0',' ','0','0',' ','0',' ',' ',' ',' ',' ',' ',' ',' ',' ',1,'2018-07-13 20:36:24','2018-07-13 20:36:24',' ',' ',' ',NULL,NULL),
	 ('SYS_OPLOG',' ','F_REQUEST','请求参数',' ','B','A','NH','DW',300,100,1,'0','1','1','1','1',' ',' ',26,'','0','1','','0',' ','0','0',' ','0',' ',' ',' ',' ',' ',' ',' ',' ',' ',1,'2021-08-03 14:07:52','2021-08-03 14:07:52',' ',NULL,NULL,NULL,NULL),
	 ('SYS_OPLOG',' ','F_RESPONSE','返回参数',' ','B','A','NH','DW',30,100,1,'0','1','1','1','1',' ',' ',27,'','0','1','','0',' ','0','0',' ','0',' ',' ',' ',' ',' ',' ',' ',' ',' ',1,'2021-08-03 14:07:52','2021-08-03 14:07:52',' ',NULL,NULL,NULL,NULL),
	 ('SYS_OPLOG',' ','F_SERVERID','系统id',' ','C','A','NH','DW',30,100,1,'0','1','1','1','1',' ',' ',21,'','0','1','','0',' ','0','0',' ','0',' ',' ',' ',' ',' ',' ',' ',' ',' ',1,'2020-03-30 17:10:03','2020-03-30 17:10:03',' ',' ',' ',NULL,NULL),
	 ('SYS_OPLOG',' ','F_SJ','F_SJ',' ','C','A','NH','DW',255,0,0,'0','1','1','1','1',' ',' ',4,'','0','1','','0',' ','0','0',' ','0',' ',' ',' ',' ',' ',' ',' ',' ',' ',0,'2018-07-13 20:36:24','2018-07-13 20:36:24',' ',' ',' ',NULL,NULL),
	 ('SYS_OPLOG',' ','F_STIME','F_STIME',' ','C','A','NH','DW',30,0,0,'0','1','1','1','1',' ',' ',5,'','0','1','','0',' ','0','0',' ','0',' ',' ',' ',' ',' ',' ',' ',' ',' ',0,'2018-07-13 20:36:24','2018-07-13 20:36:24',' ',' ',' ',NULL,NULL),
	 ('SYS_OPLOG',' ','F_STR01','F_STR01',' ','C','A','NH','DW',30,100,1,'0','1','1','1','1',' ',' ',12,'','0','1','','0',' ','0','0',' ','0',' ',' ',' ',' ',' ',' ',' ',' ',' ',1,'2018-07-13 20:36:24','2018-07-13 20:36:24',' ',' ',' ',NULL,NULL),
	 ('SYS_OPLOG',' ','F_STR02','F_STR02',' ','C','A','NH','DW',30,100,1,'0','1','1','1','1',' ',' ',13,'','0','1','','0',' ','0','0',' ','0',' ',' ',' ',' ',' ',' ',' ',' ',' ',1,'2018-07-13 20:36:24','2018-07-13 20:36:24',' ',' ',' ',NULL,NULL),
	 ('SYS_OPLOG',' ','F_STR03','F_STR03',' ','C','A','NH','DW',30,100,1,'0','1','1','1','1',' ',' ',14,'','0','1','','0',' ','0','0',' ','0',' ',' ',' ',' ',' ',' ',' ',' ',' ',1,'2018-07-13 20:36:24','2018-07-13 20:36:24',' ',' ',' ',NULL,NULL),
	 ('SYS_OPLOG',' ','F_STR04','F_STR04',' ','C','A','NH','DW',30,100,1,'0','1','1','1','1',' ',' ',15,'','0','1','','0',' ','0','0',' ','0',' ',' ',' ',' ',' ',' ',' ',' ',' ',1,'2018-07-13 20:36:24','2018-07-13 20:36:24',' ',' ',' ',NULL,NULL);
INSERT INTO SYS_OBJCOLS (OBJ_ID,COL_BASE,COL_ID,COL_MC,COL_DES,COL_TYPE,COL_APPTYPE1,COL_APPTYPE2,COL_CSTATT,COL_LEN,COL_PREC,COL_SCALE,COL_ISKEY,COL_ISNULL,COL_VISIBLE,COL_EDITABLE,COL_EDIT,COL_VIEW,COL_DEFAULT,COL_DISP,COL_ORDER,COL_MUNIT,COL_USE,COL_VALUE,COL_ISFKEY,COL_FOBJ,COL_LANG,COL_ISCALC,COL_COLGS,COL_CHECK,COL_REGEXREF,COL_ALIAS,COL_COLJY,COL_CONT,COL_EXT_ID,COL_OPT,COL_REF,F_LANG,F_GUID,F_STAU,F_CRDATE,F_CHDATE,COL_FKEY,SYS_ID,COL_ADD_LOG,F_CRUSER,F_CHUSER) VALUES
	 ('SYS_OPLOG',' ','F_STR05','F_STR05',' ','C','A','NH','DW',30,100,1,'0','1','1','1','1',' ',' ',16,'','0','1','','0',' ','0','0',' ','0',' ',' ',' ',' ',' ',' ',' ',' ',' ',1,'2018-07-13 20:36:24','2018-07-13 20:36:24',' ',' ',' ',NULL,NULL),
	 ('SYS_OPLOG',' ','F_TYPE','请求方式',' ','C','A','NH','DW',30,100,1,'0','1','1','1','1',' ',' ',25,'','0','1','','0',' ','0','0',' ','0',' ',' ',' ',' ',' ',' ',' ',' ',' ',1,'2021-08-03 14:07:52','2021-08-03 14:07:52',' ',NULL,NULL,NULL,NULL),
	 ('SYS_OPLOG',' ','F_URL','请求地址',' ','C','A','NH','DW',100,100,1,'0','1','1','1','1',' ',' ',24,'','0','1','','0',' ','0','0',' ','0',' ',' ',' ',' ',' ',' ',' ',' ',' ',1,'2021-08-03 14:07:52','2021-08-03 14:07:52',' ',NULL,NULL,NULL,NULL),
	 ('SYS_OPLOG',' ','F_USER','用户',' ','C','A','NH','DW',30,100,1,'0','1','1','1','1',' ',' ',8,'','0','1','','0',' ','0','0',' ','0',' ',' ',' ',' ',' ',' ',' ',' ',' ',1,'2018-07-13 20:36:24','2018-07-13 20:36:24',' ',' ',' ',NULL,NULL),
	 ('SYS_OPLOG',' ','F_XTBH','系统编号',' ','C','A','NH','DW',30,0,0,'1','0','1','1','1',' ',' ',0,'','0','1','','0',' ','0','0',' ','0',' ',' ',' ',' ',' ',' ',' ',' ',' ',0,'2018-07-13 20:36:24','2018-07-13 20:36:24',' ',' ',' ',NULL,NULL),
	 ('SYS_OPLOG',' ','LOG_ID','LOG_ID',' ','C','A','NH','DW',255,0,0,'0','0','1','1','1',' ',' ',1,'','0','1','','0',' ','0','0',' ','0',' ',' ',' ',' ',' ',' ',' ',' ',' ',0,'2018-07-13 20:36:24','2018-07-13 20:36:24',' ',' ',' ',NULL,NULL),
	 ('SYS_RLGL',NULL,'DCT_BMCOL1','DCT_BMCOL1',NULL,'C','A','NH','DW',30,0,0,'0','0','1','1','1',NULL,NULL,6,NULL,'0','1',NULL,'0',NULL,'0','0',NULL,'0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,'2013-07-15 17:05:35','2013-07-15 17:05:35',NULL,NULL,NULL,NULL,NULL),
	 ('SYS_RLGL',NULL,'DCT_BMCOL2','DCT_BMCOL2',NULL,'C','A','NH','DW',30,0,0,'0','0','1','1','1',NULL,NULL,7,NULL,'0','1',NULL,'0',NULL,'0','0',NULL,'0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,'2013-07-15 17:05:35','2013-07-15 17:05:35',NULL,NULL,NULL,NULL,NULL),
	 ('SYS_RLGL',NULL,'DCT_ID1','DCT_ID1',NULL,'C','A','NH','DW',30,0,0,'0','0','1','1','1',NULL,NULL,3,NULL,'0','1',NULL,'0',NULL,'0','0',NULL,'0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,'2013-07-15 17:05:35','2013-07-15 17:05:35',NULL,NULL,NULL,NULL,NULL),
	 ('SYS_RLGL',NULL,'DCT_ID2','DCT_ID2',NULL,'C','A','NH','DW',30,0,0,'0','0','1','1','1',NULL,NULL,4,NULL,'0','1',NULL,'0',NULL,'0','0',NULL,'0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,'2013-07-15 17:05:35','2013-07-15 17:05:35',NULL,NULL,NULL,NULL,NULL);
INSERT INTO SYS_OBJCOLS (OBJ_ID,COL_BASE,COL_ID,COL_MC,COL_DES,COL_TYPE,COL_APPTYPE1,COL_APPTYPE2,COL_CSTATT,COL_LEN,COL_PREC,COL_SCALE,COL_ISKEY,COL_ISNULL,COL_VISIBLE,COL_EDITABLE,COL_EDIT,COL_VIEW,COL_DEFAULT,COL_DISP,COL_ORDER,COL_MUNIT,COL_USE,COL_VALUE,COL_ISFKEY,COL_FOBJ,COL_LANG,COL_ISCALC,COL_COLGS,COL_CHECK,COL_REGEXREF,COL_ALIAS,COL_COLJY,COL_CONT,COL_EXT_ID,COL_OPT,COL_REF,F_LANG,F_GUID,F_STAU,F_CRDATE,F_CHDATE,COL_FKEY,SYS_ID,COL_ADD_LOG,F_CRUSER,F_CHUSER) VALUES
	 ('SYS_RLGL','F_CHDATE','F_CHDATE','修改时间','维表、关系表、事实表 都需要的列','T','A','NH','DW',19,100,1,'0','1','1','1','1',' ',' ',7,'','0','1','','0',' ','0','0',' ','0',' ',' ',' ',' ',' ',' ',' ',' ',' ',1,'2021-01-09 12:50:53','2021-09-26 14:36:49',' ',' ',' ',NULL,NULL),
	 ('SYS_RLGL',' ','F_CHUSER','修改人',' ','C','A','NH','DW',30,100,1,'0','1','1','1','1',' ',' ',11,'','0','1','','1','BSUSER','0','0',' ','0',' ',' ',' ',' ',' ',' ',' ',' ',' ',1,'2021-09-22 15:35:28','2021-09-23 15:41:46',' ',NULL,NULL,NULL,NULL),
	 ('SYS_RLGL','F_CRDATE','F_CRDATE','创建时间','维表、关系表、事实表 都需要的列','T','A','NH','DW',19,100,1,'0','1','1','1','1',' ',' ',7,'','0','1','','0',' ','0','0',' ','0',' ',' ',' ',' ',' ',' ',' ',' ',' ',1,'2021-01-09 12:50:53','2021-09-26 14:36:49',' ',' ',' ',NULL,NULL),
	 ('SYS_RLGL',' ','F_CRUSER','创建人',' ','C','A','NH','DW',30,100,1,'0','1','1','1','1',' ',' ',11,'','0','1','','1','BSUSER','0','0',' ','0',' ',' ',' ',' ',' ',' ',' ',' ',' ',1,'2021-09-22 15:35:28','2021-09-23 15:41:46',' ',NULL,NULL,NULL,NULL),
	 ('SYS_RLGL',NULL,'ISCHECK_SAMEGRADE','ISCHECK_SAMEGRADE',NULL,'C','A','NH','DW',1,0,0,'0','1','1','1','1',NULL,'0',18,NULL,'0','1',NULL,'0',NULL,'0','0',NULL,'0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,'2013-07-15 17:05:35','2013-07-15 17:05:35',NULL,NULL,NULL,NULL,NULL),
	 ('SYS_RLGL',NULL,'ISCREATEDDW','ISCREATEDDW',NULL,'C','A','NH','DW',1,0,0,'0','1','1','1','1',NULL,'0',13,NULL,'0','1',NULL,'0',NULL,'0','0',NULL,'0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,'2013-07-15 17:05:35','2013-07-15 17:05:35',NULL,NULL,NULL,NULL,NULL),
	 ('SYS_RLGL',NULL,'ISMUSTMX_SUBDCT','ISMUSTMX_SUBDCT',NULL,'C','A','NH','DW',1,0,0,'0','0','1','1','1',NULL,'0',17,NULL,'0','1',NULL,'0',NULL,'0','0',NULL,'0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,'2013-07-15 17:05:35','2013-07-15 17:05:35',NULL,NULL,NULL,NULL,NULL),
	 ('SYS_RLGL',NULL,'ISORDER','ISORDER',NULL,'C','A','NH','DW',1,0,0,'0','0','1','1','1',NULL,'0',16,NULL,'0','1',NULL,'0',NULL,'0','0',NULL,'0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,'2013-07-15 17:05:35','2013-07-15 17:05:35',NULL,NULL,NULL,NULL,NULL),
	 ('SYS_RLGL',NULL,'ISSORTYEAR','ISSORTYEAR',NULL,'C','A','NH','DW',1,0,0,'0','1','1','1','1',NULL,'0',14,NULL,'0','1',NULL,'0',NULL,'0','0',NULL,'0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,'2013-07-15 17:05:35','2013-07-15 17:05:35',NULL,NULL,NULL,NULL,NULL),
	 ('SYS_RLGL',NULL,'ISWH','ISWH',NULL,'C','A','NH','DW',1,0,0,'0','1','1','1','1',NULL,'1',15,NULL,'0','1',NULL,'0',NULL,'0','0',NULL,'0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,'2013-07-15 17:05:35','2013-07-15 17:05:35',NULL,NULL,NULL,NULL,NULL);
INSERT INTO SYS_OBJCOLS (OBJ_ID,COL_BASE,COL_ID,COL_MC,COL_DES,COL_TYPE,COL_APPTYPE1,COL_APPTYPE2,COL_CSTATT,COL_LEN,COL_PREC,COL_SCALE,COL_ISKEY,COL_ISNULL,COL_VISIBLE,COL_EDITABLE,COL_EDIT,COL_VIEW,COL_DEFAULT,COL_DISP,COL_ORDER,COL_MUNIT,COL_USE,COL_VALUE,COL_ISFKEY,COL_FOBJ,COL_LANG,COL_ISCALC,COL_COLGS,COL_CHECK,COL_REGEXREF,COL_ALIAS,COL_COLJY,COL_CONT,COL_EXT_ID,COL_OPT,COL_REF,F_LANG,F_GUID,F_STAU,F_CRDATE,F_CHDATE,COL_FKEY,SYS_ID,COL_ADD_LOG,F_CRUSER,F_CHUSER) VALUES
	 ('SYS_RLGL',NULL,'RLGL_ID','RLGL_ID',NULL,'C','A','NH','DW',30,0,0,'1','0','1','1','1',NULL,NULL,1,NULL,'0','1',NULL,'0',NULL,'0','0',NULL,'0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,'2013-07-15 17:05:35','2013-07-15 17:05:35',NULL,NULL,NULL,NULL,NULL),
	 ('SYS_RLGL',NULL,'RLGL_MC','RLGL_MC',NULL,'C','A','NH','DW',60,0,0,'0','0','1','1','1',NULL,NULL,2,NULL,'0','1',NULL,'0',NULL,'0','0',NULL,'0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,'2013-07-15 17:05:35','2013-07-15 17:05:35',NULL,NULL,NULL,NULL,NULL),
	 ('SYS_RLGL',NULL,'RLGL_OBJID','RLGL_OBJID',NULL,'C','A','NH','DW',30,0,0,'0','0','1','1','1',NULL,NULL,5,NULL,'0','1',NULL,'0',NULL,'0','0',NULL,'0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,'2013-07-15 17:05:35','2013-07-15 17:05:35',NULL,NULL,NULL,NULL,NULL),
	 ('SYS_RLGL',NULL,'RLGL_TYPE','RLGL_TYPE',NULL,'C','A','NH','DW',2,0,0,'0','1','1','1','1',NULL,'00',11,NULL,'0','1',NULL,'0',NULL,'0','0',NULL,'0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,'2013-07-15 17:05:35','2013-07-15 17:05:35',NULL,NULL,NULL,NULL,NULL),
	 ('SYS_RLGL',NULL,'RLGL_UNIT','RLGL_UNIT',NULL,'C','A','NH','DW',2,0,0,'0','1','1','1','1',NULL,'00',9,NULL,'0','1',NULL,'0',NULL,'0','0',NULL,'0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,'2013-07-15 17:05:35','2013-07-15 17:05:35',NULL,NULL,NULL,NULL,NULL),
	 ('SYS_RLGL',NULL,'RLGL_USCST','RLGL_USCST',NULL,'C','A','NH','DW',2,0,0,'0','1','1','1','1',NULL,'00',10,NULL,'0','1',NULL,'0',NULL,'0','0',NULL,'0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,'2013-07-15 17:05:35','2013-07-15 17:05:35',NULL,NULL,NULL,NULL,NULL),
	 ('SYS_RLGL',NULL,'RLGL_YEAR','RLGL_YEAR',NULL,'C','A','NH','DW',2,0,0,'0','1','1','1','1',NULL,'00',8,NULL,'0','1',NULL,'0',NULL,'0','0',NULL,'0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,'2013-07-15 17:05:35','2013-07-15 17:05:35',NULL,NULL,NULL,NULL,NULL),
	 ('SYS_RLGL',NULL,'RLT_OBJID','RLT_OBJID',NULL,'C','A','NH','DW',30,0,0,'0','1','1','1','1',NULL,NULL,19,NULL,'0','1',NULL,'0',NULL,'0','0',NULL,'0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,'2013-07-15 17:05:35','2013-07-15 17:05:35',NULL,NULL,NULL,NULL,NULL),
	 ('SYS_RLGL',' ','SYS_ID','SYS_ID',' ','C','A','NH','DW',30,0,0,'0','1','1','1','1',' ',' ',12,'','0','1','','1','SYS_LIST','0','0',' ','0',' ',' ',' ',' ',' ',' ',' ',' ',' ',0,'2013-07-15 17:05:35','2016-10-19 21:32:22',' ',NULL,NULL,NULL,NULL);

INSERT INTO SYS_DICTS (DCT_ID,OBJ_ID,DCT_MC,DCT_NAME,DCT_DES,DCT_QXOBJID,DCT_TYPE,DCT_BMCOLID,DCT_BZCOLID,DCT_MCCOLID,DCT_JSCOLID,DCT_MXCOLID,DCT_BMSTRU,DCT_PTCOLID,DCT_KZCOLID,SYS_ID,DCT_FIXLEN,DCT_STEP,DCT_KPI,DCT_SELECT,DCT_MUNIT,DCT_CTLONE,DCT_EXTAUTO,DCT_CREATE,DCT_CHANGE,DCT_NOTUSE,DCT_SUBJECT,DCT_UNIT,DCT_CST,DCT_FKEY1,DCT_FKEYDCT1,DCT_FKEY2,DCT_FKEYDCT2,DCT_FKEY3,DCT_FKEYDCT3,DCT_FKEY4,DCT_FKEYDCT4,DCT_FKEY5,DCT_FKEYDCT5,DCT_FKEY6,DCT_FKEYDCT6,DCT_FKEY7,DCT_FKEYDCT7,DCT_FKEY8,DCT_FKEYDCT8,DCT_SYNCDATE,F_STAU,F_CRDATE,F_CHDATE,DCT_QXSTAT,DCT_AFFIXDCT,F_GUID,F_CRUSER,F_CHUSER,F_ENABLE,F_DELETED,F_DICTSFKEY) VALUES
	 ('BSCONF','BSCONF','系统配置表',' ',' ',' ','9',' ',' ',' ',' ',' ',' ',' ',' ','ESPBASE','0',1,'0','0','0','0','1','0','0','0',' ',' ','00',' ',' ',' ',' ',' ',' ',' ',' ',' ',' ',' ',' ',' ',' ',' ',' ','2013-03-27 15:16:15',0,'2013-03-27 15:16:15','2013-04-17 14:15:24','0',' ',NULL,NULL,NULL,NULL,NULL,'0'),
	 ('SYS_DCT_CST','SYS_DCT_CST','自定义数据字典设置',' ',' ',' ','9','DCT_ID',' ','DCT_ID',' ',' ',' ',' ',NULL,'ESPBASE','0',1,'0','0','0','0','1','1','1','1',' ',' ','00',' ',' ',' ',' ',' ',' ',' ',' ',' ',' ',' ',' ',' ',' ',' ',' ',NULL,1,'2013-03-27 15:11:31','2013-03-27 15:11:31','0',NULL,NULL,NULL,NULL,NULL,NULL,'0'),
	 ('SYS_DICTS','SYS_DICTS','数据字典',' ',' ',' ','9','DCT_ID',' ','DCT_MC',' ',' ',' ',' ',NULL,'ESPBASE','0',1,'0','0','0','0','1','1','1','1',' ',' ','00',' ',' ',' ',' ',' ',' ',' ',' ',' ',' ',' ',' ',' ',' ',' ',' ',NULL,1,'2013-03-27 15:11:30','2013-03-27 15:11:30','0',NULL,NULL,NULL,NULL,NULL,NULL,'0'),
	 ('SYS_FACTS','SYS_FACTS','交易管理表',' ',' ',' ','9','FCT_ID',' ','FCT_MC',' ',' ',' ',' ',NULL,'ESPBASE','0',1,'0','0','0','0','1','1','1','1',' ',' ','00',' ',' ',' ',' ',' ',' ',' ',' ',' ',' ',' ',' ',' ',' ',' ',' ',NULL,1,'2013-03-27 15:11:35','2013-03-27 15:11:35','0',NULL,NULL,NULL,NULL,NULL,NULL,'0'),
	 ('SYS_INDEXS','SYS_INDEXS','索引信息',' ',' ',' ','9','INX_ID',' ','INX_MC',' ',' ',' ',' ',NULL,'ESPBASE','0',1,'0','0','0','0','1','1','1','1',' ',' ','00',' ',' ',' ',' ',' ',' ',' ',' ',' ',' ',' ',' ',' ',' ',' ',' ',NULL,1,'2013-03-27 15:11:29','2013-03-27 15:11:29','0',NULL,NULL,NULL,NULL,NULL,NULL,'0'),
	 ('SYS_KEYS','SYS_KEYS','主键管理表',' ',' ',' ','9','KEY_ID',' ','KEY_ID',' ',' ',' ',' ',NULL,'ESPBASE','0',1,'0','0','0','0','1','1','1','1',' ',' ','00',' ',' ',' ',' ',' ',' ',' ',' ',' ',' ',' ',' ',' ',' ',' ',' ',NULL,1,'2013-03-27 15:11:29','2013-03-27 15:11:29','0',NULL,NULL,NULL,NULL,NULL,NULL,'0'),
	 ('SYS_MDL_CTN','SYS_MDL_CTN','模型定义',' ',' ',' ','9','CTN_ID',' ','CTN_MC',' ',' ',' ',' ',NULL,'ESPBASE','0',1,'0','0','0','0','1','1','1','1',' ',' ','00',' ',' ',' ',' ',' ',' ',' ',' ',' ',' ',' ',' ',' ',' ',' ',' ',NULL,1,'2013-03-27 15:11:40','2013-03-27 15:11:40','0',NULL,NULL,NULL,NULL,NULL,NULL,'0');	 
	 
	 
INSERT INTO SYS_DICTS (DCT_ID,OBJ_ID,DCT_MC,DCT_NAME,DCT_DES,DCT_QXOBJID,DCT_TYPE,DCT_BMCOLID,DCT_BZCOLID,DCT_MCCOLID,DCT_JSCOLID,DCT_MXCOLID,DCT_BMSTRU,DCT_PTCOLID,DCT_KZCOLID,SYS_ID,DCT_FIXLEN,DCT_STEP,DCT_KPI,DCT_SELECT,DCT_MUNIT,DCT_CTLONE,DCT_EXTAUTO,DCT_CREATE,DCT_CHANGE,DCT_NOTUSE,DCT_SUBJECT,DCT_UNIT,DCT_CST,DCT_FKEY1,DCT_FKEYDCT1,DCT_FKEY2,DCT_FKEYDCT2,DCT_FKEY3,DCT_FKEYDCT3,DCT_FKEY4,DCT_FKEYDCT4,DCT_FKEY5,DCT_FKEYDCT5,DCT_FKEY6,DCT_FKEYDCT6,DCT_FKEY7,DCT_FKEYDCT7,DCT_FKEY8,DCT_FKEYDCT8,DCT_SYNCDATE,F_STAU,F_CRDATE,F_CHDATE,DCT_QXSTAT,DCT_AFFIXDCT,F_GUID,F_CRUSER,F_CHUSER,F_ENABLE,F_DELETED,F_DICTSFKEY) VALUES
	 ('SYS_MDL_VAL','SYS_MDL_VAL','模型关键值定义',NULL,' ',NULL,'9',' ',NULL,' ',' ',' ',NULL,' ',NULL,'ESPBASE','0',1,'0','0','0','0','1','1','1','1',NULL,NULL,'00',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,'2013-04-02 11:21:12','2013-04-02 11:21:12','0',NULL,NULL,NULL,NULL,NULL,NULL,'0'),
	 ('SYS_MODEL','SYS_MODEL','数据模型',' ',' ',' ','9','MDL_ID',' ','MDL_MC',' ',' ',' ',' ',NULL,'ESPBASE','0',1,'0','0','0','0','1','1','1','1',' ',' ','00',' ',' ',' ',' ',' ',' ',' ',' ',' ',' ',' ',' ',' ',' ',' ',' ',NULL,1,'2013-03-27 15:11:38','2013-03-27 15:11:38','0',NULL,NULL,NULL,NULL,NULL,NULL,'0'),
	 ('SYS_OBJCOLS','SYS_OBJCOLS','对象列管理表',' ',' ',' ','9','COL_ID',' ','COL_MC',' ',' ',' ',' ',NULL,'ESPBASE','0',1,'0','0','0','0','1','1','1','1',' ',' ','00',' ',' ',' ',' ',' ',' ',' ',' ',' ',' ',' ',' ',' ',' ',' ',' ',NULL,1,'2013-03-27 15:11:28','2013-03-27 15:11:28','0',NULL,NULL,NULL,NULL,NULL,NULL,'0'),
	 ('SYS_OBJECTS','SYS_OBJECTS','对象管理表',' ',' ',' ','9','OBJ_ID',' ','OBJ_MC',' ',' ',' ',' ',NULL,'ESPBASE','0',1,'0','0','0','0','1','1','1','1',' ',' ','00',' ',' ',' ',' ',' ',' ',' ',' ',' ',' ',' ',' ',' ',' ',' ',' ',NULL,1,'2013-03-27 15:11:26','2013-03-27 15:11:26','0',NULL,NULL,NULL,NULL,NULL,NULL,'0'),
	 ('SYS_OBJ_VAL','SYS_OBJ_VAL','SYS_OBJ_VAL',NULL,' ',NULL,'9',' ',NULL,' ',' ',' ',NULL,' ',NULL,'ESPBASE','0',1,'0','0','0','0','1','1','1','1',NULL,NULL,'00',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,'2013-04-01 11:44:47','2013-04-01 11:44:47','0',NULL,NULL,NULL,NULL,NULL,NULL,'0'),
	 ('SYS_OPLOG','SYS_OPLOG','SYS_OPLOG',' ',' ',' ','9',' ',' ',' ',' ',' ',' ',' ',' ','ESPBASE','0',1,'0','0','0','0','1','1','1','1',' ',' ','00',' ',' ',' ',' ',' ',' ',' ',' ',' ',' ',' ',' ',' ',' ',' ',' ','2018-07-13 20:36:24',0,'2018-07-13 20:36:24','2018-07-13 20:36:24','0',' ',' ',NULL,NULL,NULL,NULL,'0'),
	 ('SYS_RLGL','SYS_RLGL','字典关系表',' ',' ',' ','9','RLGL_ID',' ','RLGL_MC',' ',' ',' ',' ',NULL,'ESPBASE','0',1,'0','0','0','0','1','1','1','1',' ',' ','00',' ',' ',' ',' ',' ',' ',' ',' ',' ',' ',' ',' ',' ',' ',' ',' ',NULL,1,'2013-03-27 15:11:34','2013-03-27 15:11:34','0',NULL,NULL,NULL,NULL,NULL,NULL,'0');

INSERT INTO SYS_INDEXS (INX_ID,INX_NAME,INX_TYPE,INX_CLT,OBJ_ID,INX_COLS,F_STAU,F_CRDATE,F_CHDATE,F_CRUSER,F_CHUSER) VALUES
	 ('INX_BSCONF','INX_BSCONF','1','1','BSCONF','F_VKEY',0,'2013-04-01 09:39:59','2013-04-01 09:39:59',NULL,NULL),
	 ('INX_SYS_MDL_CTN','INX_SYS_MDL_CTN','1','1','SYS_MDL_CTN','MDL_ID,CTN_ID',0,'2013-04-02 11:19:02','2013-04-02 11:19:02',NULL,NULL),
	 ('INX_SYS_MDL_VAL','INX_SYS_MDL_VAL','1','1','SYS_MDL_VAL','MDL_ID,MDL_KEY,UNIT_ID',0,'2013-04-02 11:21:12','2013-04-02 11:21:12',NULL,NULL),
	 ('INX_SYS_MODEL','INX_SYS_MODEL','1','1','SYS_MODEL','MDL_ID',0,'2013-04-02 11:21:59','2013-04-02 11:21:59',NULL,NULL),
	 ('INX_SYS_OBJ_VAL','INX_SYS_OBJ_VAL','1','1','SYS_OBJ_VAL','OBJ_ID,OBJ_KEY',0,'2013-04-01 11:44:47','2013-04-01 11:44:47',NULL,NULL),
	 ('INX_SYS_OPLOG','INX_SYS_OPLOG','0','0','SYS_OPLOG','LOG_ID,F_GNBH',0,'2018-07-13 20:36:24','2018-07-13 20:36:24',NULL,NULL),
	 ('SYSOBJCOLS_PK','SYSOBJCOLS_PK','1','1','SYS_OBJCOLS','OBJ_ID,COL_ID',0,'2013-04-01 11:43:15','2013-04-01 11:43:15',NULL,NULL),
	 ('SYS_C00234594','SYS_C00234594','1','1','SYS_INDEXS','INX_ID',0,'2013-04-01 11:30:58','2013-04-01 11:30:58',NULL,NULL),
	 ('SYS_DCT_CST_PK','SYS_DCT_CST_PK','1','1','SYS_DCT_CST','DCT_ID,UNIT_ID,DCT_KEY',0,'2013-04-01 11:19:18','2013-04-01 11:19:18',NULL,NULL);
INSERT INTO SYS_INDEXS (INX_ID,INX_NAME,INX_TYPE,INX_CLT,OBJ_ID,INX_COLS,F_STAU,F_CRDATE,F_CHDATE,F_CRUSER,F_CHUSER) VALUES
	 ('SYS_DICTS','SYS_DICTS','1','1','SYS_DICTS','DCT_ID',1,'2016-10-19 14:18:34','2016-10-19 14:18:34',NULL,NULL),
	 ('SYS_DICTS_PK','SYS_DICTS_PK','1','1','SYS_DICTS','DCT_ID',0,'2013-04-01 11:20:42','2013-04-01 11:20:42',NULL,NULL),
	 ('SYS_FACTS','SYS_FACTS','1','1','SYS_FACTS','FCT_ID',1,'2014-05-26 16:55:47','2014-05-26 16:55:47',NULL,NULL),
	 ('SYS_FACTS_PK','SYS_FACTS_PK','1','1','SYS_FACTS','FCT_ID',0,'2013-04-01 11:23:19','2013-04-01 11:23:19',NULL,NULL),
	 ('SYS_KEYS_PK','SYS_KEYS_PK','1','1','SYS_KEYS','KEY_ID',0,'2013-04-01 11:31:51','2013-04-01 11:31:51',NULL,NULL),
	 ('SYS_MDL_CTN','SYS_MDL_CTN','1','1','SYS_MDL_CTN','MDL_ID,CTN_ID',1,'2014-05-26 16:59:09','2014-05-26 16:59:09',NULL,NULL),
	 ('SYS_MODEL','SYS_MODEL','1','1','SYS_MODEL','MDL_ID',1,'2014-05-26 17:17:51','2014-05-26 17:17:51',NULL,NULL),
	 ('SYS_OBJCOLS','SYS_OBJCOLS','1','1','SYS_OBJCOLS','OBJ_ID,COL_ID',1,'2014-07-14 19:16:00','2014-07-14 19:16:00',NULL,NULL),
	 ('SYS_OBJECTS','SYS_OBJECTS','1','1','SYS_OBJECTS','OBJ_ID',1,'2014-06-05 16:26:19','2014-06-05 16:26:19',NULL,NULL),
	 ('SYS_OBJECTS_PK','SYS_OBJECTS_PK','1','1','SYS_OBJECTS','OBJ_ID',0,'2013-04-01 11:44:02','2013-04-01 11:44:02',NULL,NULL);
INSERT INTO SYS_INDEXS (INX_ID,INX_NAME,INX_TYPE,INX_CLT,OBJ_ID,INX_COLS,F_STAU,F_CRDATE,F_CHDATE,F_CRUSER,F_CHUSER) VALUES
	 ('SYS_OPLOG_KEY','SYS_OPLOG','1','1','SYS_OPLOG','F_XTBH',0,'2018-07-13 20:36:24','2018-07-13 20:36:24',NULL,NULL),
	 ('SYS_RLGL','SYS_RLGL','1','1','SYS_RLGL','RLGL_ID',1,'2016-10-19 14:19:15','2016-10-19 14:19:15',NULL,NULL),
	 ('SYS_RLGL_PK','SYS_RLGL_PK','1','1','SYS_RLGL','RLGL_ID',0,'2013-07-15 17:05:35','2013-07-15 17:05:35',NULL,NULL);
	 
INSERT INTO SYS_KEYS (KEY_ID,OBJ_ID,KEY_TYPE,OBJ_DEPID,KEY_CNT,KEY_PINDEX1,KEY_PINDEX2,KEY_PINDEX3,KEY_PINDEX4,KEY_PINDEX5,KEY_PINDEX6,KEY_PINDEX7,KEY_PINDEX8,KEY_PINDEX9,KEY_PINDEX10,KEY_PINDEX11,KEY_PINDEX12,KEY_PINDEX13,KEY_PINDEX14,KEY_PINDEX15,KEY_PINDEX16,KEY_FINDEX1,KEY_FINDEX2,KEY_FINDEX3,KEY_FINDEX4,KEY_FINDEX5,KEY_FINDEX6,KEY_FINDEX7,KEY_FINDEX8,F_STAU,F_CRDATE,F_CHDATE,KEY_INDEX) VALUES
	 ('SYS_DICTS','SYS_DICTS','P',' ',1,'DCT_ID',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,'2016-10-19 14:18:34','2016-10-19 14:18:34',' '),
	 ('SYS_FACTS','SYS_FACTS','P',' ',1,'FCT_ID',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,'2014-05-26 16:55:47','2014-05-26 16:55:47',' '),
	 ('SYS_MDL_CTN','SYS_MDL_CTN','P',' ',2,'MDL_ID','CTN_ID',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,'2014-05-26 16:59:09','2014-05-26 16:59:09',' '),
	 ('SYS_MDL_VAL_PK','SYS_MDL_VAL','P',' ',3,'MDL_ID','MDL_KEY','UNIT_ID',' ',' ',' ',' ',' ',' ',' ',' ',' ',' ',' ',' ',' ',' ',' ',' ',' ',' ',' ',' ',' ',0,'2013-03-27 15:11:38','2013-03-27 15:11:38',' '),
	 ('SYS_MODEL','SYS_MODEL','P',' ',1,'MDL_ID',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,'2014-05-26 17:17:51','2014-05-26 17:17:51',' '),
	 ('SYS_OBJCOLS','SYS_OBJCOLS','P',' ',2,'OBJ_ID','COL_ID',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,'2014-07-14 19:16:00','2014-07-14 19:16:00',' '),
	 ('SYS_OBJECTS','SYS_OBJECTS','P',' ',1,'OBJ_ID',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,'2014-06-05 16:26:19','2014-06-05 16:26:19',' '),
	 ('SYS_OPLOG','SYS_OPLOG','P',' ',1,'F_XTBH',' ',' ',' ',' ',' ',' ',' ',' ',' ',' ',' ',' ',' ',' ',' ',' ',' ',' ',' ',' ',' ',' ',' ',0,'2018-07-13 20:36:24','2018-07-13 20:36:24','SYS_OPLOG_KEY'),
	 ('SYS_RLGL','SYS_RLGL','P',' ',1,'RLGL_ID',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,'2016-10-19 14:19:15','2016-10-19 14:19:15',' ');
	 
