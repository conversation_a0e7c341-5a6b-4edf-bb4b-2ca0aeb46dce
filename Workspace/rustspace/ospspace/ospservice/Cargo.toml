[package]
name = "ospservice"
version = "0.1.0"
edition = "2021"

[dependencies]
    tokio = { version = "1", features = ["full"] }
    async-trait = "0.1.83"
    ospbase = { path = "../ospbase" }
    libloading = "0.8"
    futures = "0.3"
    thiserror = "1.0"
    serde = { version = "1.0", features = ["derive"] }
    serde_json = "1.0"
    tracing = "0.1"
    tracing-subscriber = "0.3"
    semver = "1.0"
    lazy_static = "1.4"
    rhai = "1.20"
    once_cell = "1"
    
