pub mod process;
pub mod scripts;


pub mod service;
pub mod plugin;


use std::sync::Arc;

use ospbase::data::{context::SVRContext, request::OSPRequest, response::OSPResponse};
use service::ServiceContainer;
use tokio::sync::RwLock;



pub type AsyncContext = Arc<RwLock<SVRContext>>;
pub type AsyncRequest = Arc<dyn OSPRequest>;
pub type AsyncResponse = Arc<RwLock<dyn OSPResponse>>;
pub type AsyncContainer = Arc<RwLock<ServiceContainer>>;



pub fn add(left: u64, right: u64) -> u64 {
    left + right
}

#[cfg(test)]
mod tests {
    // use plugin::manager::PluginManager;

    use super::*;

    #[test]
    fn it_works() {
        let result = add(2, 2);
        assert_eq!(result, 4);
        // test().await;
    }


//     #[tokio::test]
//     async fn test() {
//         let manager = PluginManager::new();
    
//         // 加载插件
//         manager.load_plugin("plugins/example_plugin.dll").await.unwrap();
        
//         // 初始化所有插件
//         manager.initialize_all().await.unwrap();
        
//         // 异步执行插件
//         // if let Some(plugin) = manager.get_plugin("example_plugin").await {
//             // let result = plugin.execute("test data").await;
//             // println!("Plugin result: {}", result.unwrap());
//         // }
        
//         // 批量执行所有插件
//         // let results = manager.execute_all("test data").await;
//         // println!("All results: {:?}", results);
        
//         // 关闭所有插件
//         manager.shutdown_all().await.unwrap();
        
//         // Ok(())
//     }
}
