pub mod manager;
pub mod executor;


// use std::sync::Arc;

// use tokio::sync::RwLock;

use crate::plugin::group::PluginGroup;



pub struct ServiceStage {
    name: String,
    groups: Vec<PluginGroup>,
}

impl ServiceStage {
    pub fn new(name: &str) -> Self {
        Self {
            name: name.to_string(),
            groups: Vec::new(),
        }
    }

    /// 添加插件组
    pub fn add_group(&mut self, group: PluginGroup) {
        self.groups.push(group);
    }

    /// 删除插件组
    pub fn remove_group(&mut self, name: &str) -> Option<PluginGroup> {
        if let Some(pos) = self.groups.iter().position(|g| g.name() == name) {
            Some(self.groups.remove(pos))
        } else {
            None
        }
    }

    /// 获取阶段名称
    pub fn name(&self) -> &str {
        &self.name
    }
    /// 获取指定名称的组（只读）
    pub fn get_group(&self, name: &str) -> Option<&PluginGroup> {
        self.groups.iter().find(|g| g.name() == name)
    }

    /// 获取指定名称的组（可变）
    pub fn get_group_mut(&mut self, name: &str) -> Option<&mut PluginGroup> {
        self.groups.iter_mut().find(|g| g.name() == name)
    }
    /// 获取所有组
    pub fn groups(&self) -> &[PluginGroup] {
        &self.groups
    }

    /// 获取可变的组列表
    pub fn groups_mut(&mut self) -> &mut Vec<PluginGroup> {
        &mut self.groups
    }
}

pub enum TransactionMode {
    ContextDependent,
    Manual,
    Automatic,
    StageBased,
    GroupBased,
}

pub enum ScriptMode {
    None,
    Lua,
    JavaScript,
    Python,
}

pub struct ServiceContainer {
    service_id: String,
    prepare_stage: ServiceStage,
    execute_stage: ServiceStage,
    finish_stage: ServiceStage,
    transaction_mode: TransactionMode,
    script_mode: ScriptMode,
}

impl ServiceContainer {
    pub fn new(id: impl Into<String>) -> Self {
        Self {
            service_id: id.into(),
            prepare_stage: ServiceStage::new("prepare"),
            execute_stage: ServiceStage::new("execute"),
            finish_stage: ServiceStage::new("finish"),
            transaction_mode: TransactionMode::ContextDependent,
            script_mode: ScriptMode::None,
        }
    }

    // Method to set the service ID
    pub fn set_service_id(&mut self, id: impl Into<String>) {
        self.service_id = id.into();
    }

    // Method to get the service ID
    pub fn service_id(&self) -> &str {
        &self.service_id
    }

    // 获取准备阶段
    pub fn prepare_stage(&self) -> &ServiceStage {
        &self.prepare_stage
    }

    // 获取准备阶段（可变）
    pub fn prepare_stage_mut(&mut self) -> &mut ServiceStage {
        &mut self.prepare_stage
    }

    // 获取执行阶段
    pub fn execute_stage(&self) -> &ServiceStage {
        &self.execute_stage
    }

    // 获取执行阶段（可变）
    pub fn execute_stage_mut(&mut self) -> &mut ServiceStage {
        &mut self.execute_stage
    }

    // 获取结束阶段
    pub fn finish_stage(&self) -> &ServiceStage {
        &self.finish_stage
    }

    // 获取结束阶段（可变）
    pub fn finish_stage_mut(&mut self) -> &mut ServiceStage {
        &mut self.finish_stage
    }

    // Method to set the transaction mode
    pub fn set_transaction_mode(&mut self, mode: TransactionMode) {
        self.transaction_mode = mode;
    }

    // Method to get the current transaction mode
    pub fn transaction_mode(&self) -> &TransactionMode {
        &self.transaction_mode
    }

    // Method to set the script mode
    pub fn set_script_mode(&mut self, mode: ScriptMode) {
        self.script_mode = mode;
    }

    // Method to get the current script mode
    pub fn script_mode(&self) -> &ScriptMode {
        &self.script_mode
    }
}