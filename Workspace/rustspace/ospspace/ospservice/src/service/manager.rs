use std::collections::HashMap;
use std::sync::RwLock;
use lazy_static::lazy_static;

use crate::AsyncContainer;

// use super::AsyncContainer;

lazy_static! {
    static ref SERVICE_CONTAINERS: RwLock<HashMap<String, AsyncContainer>> = RwLock::new(HashMap::new());
}

#[derive(Debug)]
pub enum ServiceManagerError {
    ContainerNotFound(String),
    ContainerAlreadyExists(String),
    LockError(String),
}

pub struct ServiceManager;

impl ServiceManager {
    // 添加新的服务容器
    pub fn add_container(id: impl Into<String>, container: AsyncContainer) -> Result<(), ServiceManagerError> {
        let id = id.into();
        let mut containers = SERVICE_CONTAINERS
            .write()
            .map_err(|e| ServiceManagerError::LockError(e.to_string()))?;

        if containers.contains_key(&id) {
            return Err(ServiceManagerError::ContainerAlreadyExists(id));
        }

        containers.insert(id, container);
        Ok(())
    }

    // 移除服务容器
    pub fn remove_container(id: &str) -> Result<AsyncContainer, ServiceManagerError> {
        let mut containers = SERVICE_CONTAINERS
            .write()
            .map_err(|e| ServiceManagerError::LockError(e.to_string()))?;

        containers
            .remove(id)
            .ok_or_else(|| ServiceManagerError::ContainerNotFound(id.to_string()))
    }

    // 获取服务容器
    pub fn get_container(id: &str) -> Result<AsyncContainer, ServiceManagerError> {
        let containers = SERVICE_CONTAINERS
            .read()
            .map_err(|e| ServiceManagerError::LockError(e.to_string()))?;

        containers
            .get(id)
            .cloned()
            .ok_or_else(|| ServiceManagerError::ContainerNotFound(id.to_string()))
    }

    // 获取所有容器ID
    pub fn get_all_container_ids() -> Result<Vec<String>, ServiceManagerError> {
        let containers = SERVICE_CONTAINERS
            .read()
            .map_err(|e| ServiceManagerError::LockError(e.to_string()))?;

        Ok(containers.keys().cloned().collect())
    }

    // 检查容器是否存在
    pub fn container_exists(id: &str) -> Result<bool, ServiceManagerError> {
        let containers = SERVICE_CONTAINERS
            .read()
            .map_err(|e| ServiceManagerError::LockError(e.to_string()))?;

        Ok(containers.contains_key(id))
    }

    // 获取容器数量
    pub fn container_count() -> Result<usize, ServiceManagerError> {
        let containers = SERVICE_CONTAINERS
            .read()
            .map_err(|e| ServiceManagerError::LockError(e.to_string()))?;

        Ok(containers.len())
    }

    // 清除所有容器
    pub fn clear_containers() -> Result<(), ServiceManagerError> {
        let mut containers = SERVICE_CONTAINERS
            .write()
            .map_err(|e| ServiceManagerError::LockError(e.to_string()))?;

        containers.clear();
        Ok(())
    }
}

// 单元测试
#[cfg(test)]
mod tests {
    use std::sync::Arc;

    use crate::service::{ServiceContainer, TransactionMode};

    use super::*;

    #[tokio::test]
    async fn test_service_manager() {
        // 创建测试容器
        let container = ServiceContainer::new("test1".to_string());
        let async_container = Arc::new(tokio::sync::RwLock::new(container));
        
        // 测试添加容器
        assert!(ServiceManager::add_container("test1", async_container).is_ok());
        
        // 测试重复添加
        let container2 = ServiceContainer::new("test1".to_string());
        let async_container2 = Arc::new(tokio::sync::RwLock::new(container2));
        assert!(matches!(
            ServiceManager::add_container("test1", async_container2),
            Err(ServiceManagerError::ContainerAlreadyExists(_))
        ));

        // 测试获取容器
        let container = ServiceManager::get_container("test1").unwrap();
        {
            let container = container.read().await;
            assert_eq!(container.service_id(), "test1");
        }

        // 测试获取不存在的容器
        assert!(matches!(
            ServiceManager::get_container("non_existent"),
            Err(ServiceManagerError::ContainerNotFound(_))
        ));

        // 测试容器数量
        assert_eq!(ServiceManager::container_count().unwrap(), 1);

        // 测试修改容器
        {
            let container = ServiceManager::get_container("test1").unwrap();
            let mut container = container.write().await;
            container.set_transaction_mode(TransactionMode::Automatic);
        }

        // 测试移除容器
        assert!(ServiceManager::remove_container("test1").is_ok());
        assert!(matches!(
            ServiceManager::remove_container("test1"),
            Err(ServiceManagerError::ContainerNotFound(_))
        ));

        // 测试清除所有容器
        assert!(ServiceManager::clear_containers().is_ok());
        assert_eq!(ServiceManager::container_count().unwrap(), 0);
    }
}
