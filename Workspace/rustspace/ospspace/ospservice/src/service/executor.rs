use crate::{plugin::{group::PluginGroup, PluginError, PluginValue}, AsyncContainer, AsyncContext, AsyncRequest, AsyncResponse};

use super::{ServiceStage, TransactionMode};



pub struct ServiceExecutor;

impl ServiceExecutor {
    /// 执行所有阶段的插件
    pub async fn execute(
        container: AsyncContainer,
        context: AsyncContext,
        request: AsyncRequest,
        response: AsyncResponse,
    ) -> Result<Vec<PluginValue>, PluginError> {
        let mut results = Vec::new();
        
        // 获取容器的只读访问
        let service_container = container.read().await;

        // 1. 执行准备阶段
        let prepare_results = Self::execute_stage(
            &service_container.prepare_stage(),
            container.clone(),
            context.clone(),
            request.clone(),
            response.clone(),
        ).await?;
        results.extend(prepare_results);

        // 2. 执行执行阶段
        let execute_results = Self::execute_stage(
            &service_container.execute_stage(),
            container.clone(),
            context.clone(),
            request.clone(),
            response.clone(),
        ).await?;
        results.extend(execute_results);

        // 3. 执行结束阶段
        let finish_results = Self::execute_stage(
            &service_container.finish_stage(),
            container.clone(),
            context.clone(),
            request.clone(),
            response.clone(),
        ).await?;
        results.extend(finish_results);

        Ok(results)
    }

    /// 执行单个阶段的所有插件组
    async fn execute_stage(
        stage: &ServiceStage,
        container: AsyncContainer,
        context: AsyncContext,
        request: AsyncRequest,
        response: AsyncResponse,
    ) -> Result<Vec<PluginValue>, PluginError> {
        let mut stage_results = Vec::new();

        // 遍历阶段中的所有组
        for group in stage.groups() {
            // 根据阶段名称选择对应的执行方法
            let group_results = match stage.name() {
                "prepare" => {
                    Self::execute_group_prepare(
                        group,
                        container.clone(),
                        context.clone(),
                        request.clone(),
                        response.clone(),
                    ).await?
                },
                "execute" => {
                    Self::execute_group_execute(
                        group,
                        container.clone(),
                        context.clone(),
                        request.clone(),
                        response.clone(),
                    ).await?
                },
                "finish" => {
                    Self::execute_group_finish(
                        group,
                        container.clone(),
                        context.clone(),
                        request.clone(),
                        response.clone(),
                    ).await?
                },
                _ => Vec::new(),
            };
            stage_results.extend(group_results);
        }

        Ok(stage_results)
    }

    /// 执行组的prepare方法
    async fn execute_group_prepare(
        group: &PluginGroup,
        container: AsyncContainer,
        context: AsyncContext,
        request: AsyncRequest,
        response: AsyncResponse,
    ) -> Result<Vec<PluginValue>, PluginError> {
        let mut results = Vec::new();
        for plugin in group.plugins() {
            let result = plugin.prepare(
                container.clone(),
                context.clone(),
                request.clone(),
                response.clone(),
            ).await?;
            results.push(result);
        }
        Ok(results)
    }

    /// 执行组的execute方法
    async fn execute_group_execute(
        group: &PluginGroup,
        container: AsyncContainer,
        context: AsyncContext,
        request: AsyncRequest,
        response: AsyncResponse,
    ) -> Result<Vec<PluginValue>, PluginError> {
        let mut results = Vec::new();
        for plugin in group.plugins() {
            let result = plugin.execute(
                container.clone(),
                context.clone(),
                request.clone(),
                response.clone(),
            ).await?;
            results.push(result);
        }
        Ok(results)
    }

    /// 执行组的finish方法
    async fn execute_group_finish(
        group: &PluginGroup,
        container: AsyncContainer,
        context: AsyncContext,
        request: AsyncRequest,
        response: AsyncResponse,
    ) -> Result<Vec<PluginValue>, PluginError> {
        let mut results = Vec::new();
        for plugin in group.plugins() {
            let result = plugin.finish(
                container.clone(),
                context.clone(),
                request.clone(),
                response.clone(),
            ).await?;
            results.push(result);
        }
        Ok(results)
    }

    async fn _execute_with_transaction(
        _container: AsyncContainer,
        _context: AsyncContext,
        _request: AsyncRequest,
        _response: AsyncResponse,
    ) -> Result<Vec<PluginValue>, PluginError> {
        let service_container = _container.read().await;
        let mode = service_container.transaction_mode();
        match mode {
            TransactionMode::ContextDependent => {
                // 上下文依赖的事务处理
                Err(PluginError::ExecuteError("Automatic transaction mode is not supported".into()))
            },
            TransactionMode::Automatic => {
                // 自动事务处理
                Err(PluginError::ExecuteError("Automatic transaction mode is not supported".into()))
            },
            TransactionMode::Manual => {
                // 手动事务处理
                Err(PluginError::ExecuteError("Automatic transaction mode is not supported".into()))
            },
            TransactionMode::StageBased => {
                // 基于阶段的事务
                Err(PluginError::ExecuteError("StageBased transaction mode is not supported".into()))
            },
            TransactionMode::GroupBased => {
                // 基于组的事务
                Err(PluginError::ExecuteError("GroupBased transaction mode is not supported".into()))
            }
        }
    }
}

// #[cfg(test)]
// mod tests {
//     use crate::plugin::ServiceContainer;

//     use super::*;
//     use std::sync::Arc;
//     use ospbase::data::context::SVRContext;
//     use tokio::sync::RwLock;

//     #[tokio::test]
//     async fn test_service_executor() {
//         // 创建测试容器
//         let container = Arc::new(RwLock::new(ServiceContainer::new()));
        
//         // 创建测试上下文和请求响应
//         let context = Arc::new(RwLock::new(SVRContext::new()));
//         let request = Arc::new(TestRequest::new());
//         let response = Arc::new(RwLock::new(TestResponse::new()));

//         // 执行服务
//         let results = ServiceExecutor::execute(
//             container,
//             context,
//             request,
//             response,
//         ).await;

//         assert!(results.is_ok());
//     }
// } 