use std::collections::HashMap;
use async_trait::async_trait;
use super::{ProcessGraph, Node, NodeType};
use std::collections::HashSet;

/// 条件评估器特征
/// 用于评估流程中的条件表达式
pub trait ConditionEvaluator: Send + Sync {
    /// 评估条件表达式
    /// * `condition` - 条件表达式字符串
    /// * `context` - 任务上下文
    /// * 返回 - 条件是否满足
    fn evaluate(&self, condition: &str, context: &TaskContext) -> bool;
}

/// 简单条件评估器的默认实现
/// 支持基本的相等性比较，格式为 "variable == value"
pub struct SimpleConditionEvaluator;

impl ConditionEvaluator for SimpleConditionEvaluator {
    fn evaluate(&self, condition: &str, context: &TaskContext) -> bool {
        if condition.is_empty() {
            return true;
        }

        // 简单的条件评估实现，格式: "variable == value"
        let parts: Vec<&str> = condition.split("==").collect();
        if parts.len() != 2 {
            return false;
        }

        let var_name = parts[0].trim();
        let expected_value = parts[1].trim().replace('"', "");

        match context.get_variable(var_name) {
            Some(value) => value.to_string().replace('"', "") == expected_value,
            None => false,
        }
    }
}

/// 任务执行的上下文环境
/// 存储流程执行过程中的变量和状态信息
#[derive(Debug, Clone)]
pub struct TaskContext {
    /// 存储流程变量的键值对
    variables: HashMap<String, serde_json::Value>,
    /// 记录节点的入边完成情况，key为节点ID，value为已完成的源节点集合
    pub incoming_completed: HashMap<String, HashSet<String>>,
    /// 当前正在执行的节点ID
    current_node_id: Option<String>,
}

impl TaskContext {
    pub fn new() -> Self {
        Self {
            variables: HashMap::new(),
            incoming_completed: HashMap::new(),
            current_node_id: None,
        }
    }

    pub fn set_variable(&mut self, key: String, value: serde_json::Value) {
        self.variables.insert(key, value);
    }

    pub fn get_variable(&self, key: &str) -> Option<&serde_json::Value> {
        self.variables.get(key)
    }

    pub fn mark_incoming_completed(&mut self, target_node: &str, from_node: &str) {
        println!(">>> Marking completed: target={}, from={}", target_node, from_node);
        self.incoming_completed
            .entry(target_node.to_string())
            .or_insert_with(|| HashSet::new())
            .insert(from_node.to_string());
        
        // 打印当前的完成情况
        println!(">>> Current completed edges for {}: {:?}", 
            target_node, 
            self.incoming_completed.get(target_node).unwrap()
        );
    }
}

/// 任务执行的结果枚举
#[derive(Debug)]
pub enum TaskResult {
    /// 任务执行成功
    Success,
    /// 任务执行失败，包含错误信息
    Failure(String),
}

/// 任务处理器特征
/// 定义了具体任务节点的执行行为
#[async_trait]
pub trait TaskHandler: Send + Sync {
    /// 执行任务
    /// * `context` - 任务执行上下文
    /// * 返回 - 任务执行结果
    async fn execute(&self, graph: &ProcessGraph, node: &Node, context: &mut TaskContext) -> TaskResult;
}

/// 流程执行引擎
/// 负责整个流程的执行和协调
pub struct ProcessEngine {
    /// 流程图定义
    graph: ProcessGraph,
    /// 任务处理器映射表，key为节点ID，value为对应的处理器
    task_handlers: HashMap<String, Box<dyn TaskHandler>>,
    /// 条件评估器
    condition_evaluator: Box<dyn ConditionEvaluator>,
}

impl ProcessEngine {
    /// 创建新的流程引擎实例
    /// * `graph` - 流程图定义
    pub fn new(graph: ProcessGraph) -> Self {
        Self {
            graph,
            task_handlers: HashMap::new(),
            condition_evaluator: Box::new(SimpleConditionEvaluator),
        }
    }

    /// 使用自定义条件评估器创建流程引擎
    /// * `graph` - 流程图定义
    /// * `evaluator` - 自定义的条件评估器
    pub fn with_condition_evaluator(
        graph: ProcessGraph,
        evaluator: Box<dyn ConditionEvaluator>
    ) -> Self {
        Self {
            graph,
            task_handlers: HashMap::new(),
            condition_evaluator: evaluator,
        }
    }

    /// 注册任务处理器
    /// * `node_id` - 节点ID
    /// * `handler` - 任务处理器实现
    pub fn register_task_handler(
        &mut self,
        node_id: String,
        handler: Box<dyn TaskHandler>,
    ) {
        self.task_handlers.insert(node_id, handler);
    }

    /// 执行流程
    /// * `context` - 任务执行上下文
    /// * 返回 - 执行结果
    pub async fn execute(&self, context: &mut TaskContext) -> Result<(), String> {
        let start_node = self.graph.get_start_node()
            .ok_or("No start node found")?;

        self.execute_node(&start_node.id, context).await
    }

    /// 执行单个节点
    /// * `node_id` - 要执行的节点ID
    /// * `context` - 任务执行上下文
    /// * 返回 - 执行结果
    async fn execute_node(&self, node_id: &str, context: &mut TaskContext) -> Result<(), String> {
        let node = self.graph.nodes.get(node_id)
            .ok_or_else(|| format!("Node not found: {}", node_id))?;

        let fut = match node.node_type {
            NodeType::Task => {
                Box::pin(self.execute_task(node, context)).await?;
                Ok(())
            },
            NodeType::Gateway => {
                Box::pin(self.execute_gateway(node, context)).await?;
                Ok(())
            }
        };
        fut
    }

    /// 执行任务节点
    /// * `node` - 任务节点
    /// * `context` - 任务执行上下文
    /// * 返回 - 执行结果
    async fn execute_task(&self, node: &Node, context: &mut TaskContext) -> Result<(), String> {
        println!("\n=== Executing task for node: {} ===", node.id);
        println!("Current node_id in context: {:?}", context.current_node_id);
        
        let incoming_nodes = self.graph.get_incoming_nodes(&node.id);
        println!("Incoming nodes: {:?}", incoming_nodes.iter().map(|n| &n.id).collect::<Vec<_>>());
        
        // 如果是开始节点（入边数为0），直接执行
        if incoming_nodes.is_empty() {
            println!("This is a start node");
            let handler = self.task_handlers.get(&node.id)
                .ok_or_else(|| format!("No handler registered for task: {}", node.id))?;
            context.current_node_id = Some(node.id.clone());
            match handler.execute(&self.graph, node, context).await {
                TaskResult::Success => {
                    println!("Start node executed, current_node_id set to: {}", node.id);
                    let next_nodes = self.graph.get_next_nodes(&node.id);
                    for (next_node, _) in next_nodes {
                        self.execute_node(&next_node.id, context).await?;
                    }
                    return Ok(());
                }
                TaskResult::Failure(err) => return Err(err),
            }
        }

        // 非开始节点的处理逻辑
        let total_incoming = incoming_nodes.len();

        // 标记当前入边已完成
        if let Some(from_node_id) = &context.current_node_id {
            println!("Marking completion from {} to {}", from_node_id, node.id);
            context.mark_incoming_completed(&node.id.clone(), &from_node_id.clone());
        }

        // 检查是否所有入边都已完成
        let completed_edges = context.incoming_completed
            .get(&node.id)
            .map(|set| set.len())
            .unwrap_or(0);

        println!(
            "Node {} completion status: {}/{} edges", 
            node.id, completed_edges, total_incoming
        );

        if completed_edges == total_incoming {
            println!("All edges completed, executing node: {}", node.id);
            let handler = self.task_handlers.get(&node.id)
                .ok_or_else(|| format!("No handler registered for task: {}", node.id))?;
            context.current_node_id = Some(node.id.clone());
            match handler.execute(&self.graph, node, context).await {
                TaskResult::Success => {
                    
                    println!("Node executed successfully, current_node_id set to: {}", node.id);
                    let next_nodes = self.graph.get_next_nodes(&node.id);
                    for (next_node, _) in next_nodes {
                        self.execute_node(&next_node.id, context).await?;
                    }
                    Ok(())
                }
                TaskResult::Failure(err) => Err(err),
            }
        } else {
            println!("Not all edges completed for {}, waiting...", node.id);
            Ok(())
        }
    }

    /// 执行网关节点
    /// * `node` - 网关节点
    /// * `context` - 任务执行上下文
    /// * 返回 - 执行结果
    async fn execute_gateway(&self, node: &Node, context: &mut TaskContext) -> Result<(), String> {

        println!("\n=== Executing gateway for node: {} ===", node.id);
        println!("Current gateway node_id in context: {:?}", context.current_node_id);
    // 获取所有入边节点
    let incoming_nodes = self.graph.get_incoming_nodes(&node.id);
    let total_incoming = incoming_nodes.len();

    // 如果有入边，需要等待所有入边完成
    if !incoming_nodes.is_empty() {
        // 标记当前入边已完成
        if let Some(from_node_id) = &context.current_node_id {
            println!("Marking completion from {} to {}", from_node_id, node.id);
            context.mark_incoming_completed(&node.id.clone(), &from_node_id.clone());
        }

        // 检查是否所有入边都已完成
        let completed_edges = context.incoming_completed
            .get(&node.id)
            .map(|set| set.len())
            .unwrap_or(0);

        println!(
            "Gateway {} completion status: {}/{} edges", 
            node.id, completed_edges, total_incoming
        );

        // 如果入边未全部完成，直接返回
        if completed_edges < total_incoming {
            println!("Not all incoming edges completed for gateway {}, waiting...", node.id);
            return Ok(());
        }
    }
           // 获取所有出边及其条件
           let next_nodes = self.graph.get_next_nodes(&node.id);
           
           // 检查每条出边的条件
           for (next_node, condition) in next_nodes {
               // 设置当前节点为网关节点
               context.current_node_id = Some(node.id.clone());
               println!("Executing gateway node: {}", node.id);
               let should_execute = self.condition_evaluator.evaluate(&condition, &context);

               println!("Gateway {} -> {}, condition: {}, should_execute: {}", 
                   node.id, next_node.id, condition, should_execute);

               if should_execute {
                   // 条件为true，执行下一个节点
                   self.execute_node(&next_node.id, context).await?;
               } else {
                   // 条件为false，标记节点已完成但不执行
                   println!("Condition false for {}, marking as completed without execution", next_node.id);
                   context.mark_incoming_completed(&next_node.id, &node.id);
               }
           }
           Ok(())
    }
} 