pub mod engine;
#[cfg(test)]
mod engine_test;

use std::collections::{HashMap, HashSet, VecDeque};

// 节点类型枚举
#[derive(Debug, Clone, PartialEq)]
pub enum NodeType {
    Task,    // 任务节点
    Gateway, // 网关节点
}

// 应用类型枚举
#[derive(Debug, Clone, PartialEq)]
pub enum AppType {
    Http,           // HTTP 服务
    Database,       // 数据库服务
    MessageQueue,   // 消息队列
    Function,       // 函数计算
    PluginGroup,    // 插件组
    MailService,    // 邮件服务
    CacheService,   // 缓存服务
    BAScriptService,  // 脚本服务
    Custom(String), // 自定义类型
}

// 节点结构
#[derive(Debug, Clone)]
pub struct Node {
    id: String,
    node_type: NodeType,
    app_type: AppType,                              // 应用类型
    properties: HashMap<String, serde_json::Value>, // 扩展属性
}

impl Node {
    pub fn new(id: String, node_type: NodeType, app_type: AppType) -> Self {
        Node {
            id,
            node_type,
            app_type,
            properties: HashMap::new(),
        }
    }

    // 设置属性
    pub fn set_property(&mut self, key: String, value: serde_json::Value) {
        self.properties.insert(key, value);
    }

    // 获取属性
    pub fn get_property(&self, key: &str) -> Option<&serde_json::Value> {
        self.properties.get(key)
    }

    // 删除属性
    pub fn remove_property(&mut self, key: &str) -> Option<serde_json::Value> {
        self.properties.remove(key)
    }

    // Getters
    pub fn id(&self) -> &str {
        &self.id
    }

    pub fn node_type(&self) -> &NodeType {
        &self.node_type
    }

    pub fn app_type(&self) -> &AppType {
        &self.app_type
    }
}

// 边结构
#[derive(Debug, Clone)]
pub struct Edge {
    from: String,      // 起始节点ID
    to: String,        // 目标节点ID
    condition: String, // 条件表达式
}

// 流程图结构
#[derive(Debug)]
pub struct ProcessGraph {
    nodes: HashMap<String, Node>,
    edges: Vec<Edge>,
}

#[derive(Debug)]
pub enum ValidationError {
    CyclicDependency(String),
    IsolatedNode(String),
    MultipleStartNodes,
    MultipleEndNodes,
    NoStartNode,
    NoEndNode,
    InvalidEdge(String),
}

impl ProcessGraph {
    pub fn new() -> Self {
        ProcessGraph {
            nodes: HashMap::new(),
            edges: Vec::new(),
        }
    }
    pub fn add_node(&mut self, node: Node) -> Result<(), String> {
        if self.nodes.contains_key(&node.id) {
            return Err("Node ID already exists".to_string());
        }
        
        self.nodes.insert(node.id.clone(), node);
        Ok(())
    }
    
    // 添加节点
    // pub fn add_node(&mut self, id: String, node_type: NodeType) -> Result<(), String> {
    //     if self.nodes.contains_key(&id) {
    //         return Err("Node ID already exists".to_string());
    //     }
        
    //     self.nodes.insert(id.clone(), Node { id, node_type });
    //     Ok(())
    // }

    // 添加边
    pub fn add_edge(&mut self, from: String, to: String, condition: String) -> Result<(), String> {
        // 检查节点是否存在
        if !self.nodes.contains_key(&from) || !self.nodes.contains_key(&to) {
            return Err("Source or target node does not exist".to_string());
        }

        // 如果是任务节点，检查是否已经有输出边
        if let Some(from_node) = self.nodes.get(&from) {
            if from_node.node_type == NodeType::Task {
                let output_edges = self.edges.iter()
                    .filter(|e| e.from == from)
                    .count();
                if output_edges > 0 {
                    return Err("Task node can only have one output edge".to_string());
                }
            }
        }

        self.edges.push(Edge { from, to, condition });
        Ok(())
    }

    // 获取节点的后继节点
    pub fn get_next_nodes(&self, node_id: &str) -> Vec<(&Node, &str)> {
        self.edges.iter()
            .filter(|e| e.from == node_id)
            .filter_map(|e| {
                self.nodes.get(&e.to)
                    .map(|node| (node, e.condition.as_str()))
            })
            .collect()
    }

    // 获取节点的前驱节点
    pub fn get_prev_nodes(&self, node_id: &str) -> Vec<(&Node, &str)> {
        self.edges.iter()
            .filter(|e| e.to == node_id)
            .filter_map(|e| {
                self.nodes.get(&e.from)
                    .map(|node| (node, e.condition.as_str()))
            })
            .collect()
    }

    /// 验证流程图
    pub fn validate(&self) -> Result<(), ValidationError> {
        self.check_cyclic_dependency()?;
        self.check_isolated_nodes()?;
        self.check_start_end_nodes()?;
        Ok(())
    }

    /// 检查是否存在环
    fn check_cyclic_dependency(&self) -> Result<(), ValidationError> {
        let mut visited = HashSet::new();
        let mut path = HashSet::new();

        // 对每个节点进行DFS，检查是否存在环
        for node_id in self.nodes.keys() {
            if !visited.contains(node_id) {
                if self.has_cycle(node_id, &mut visited, &mut path) {
                    return Err(ValidationError::CyclicDependency(
                        format!("Cycle detected starting from node: {}", node_id)
                    ));
                }
            }
        }
        Ok(())
    }

    fn has_cycle(&self, 
                 node_id: &str, 
                 visited: &mut HashSet<String>, 
                 path: &mut HashSet<String>) -> bool {
        visited.insert(node_id.to_string());
        path.insert(node_id.to_string());

        let has_cycle = self.get_next_nodes(node_id).iter().any(|(next_node, _)| {
            let next_id = &next_node.id;
            if !visited.contains(next_id) {
                self.has_cycle(next_id, visited, path)
            } else {
                path.contains(next_id)
            }
        });

        path.remove(node_id);
        has_cycle
    }

    /// 检查孤立节点
    fn check_isolated_nodes(&self) -> Result<(), ValidationError> {
        for (id, _) in &self.nodes {
            let has_incoming = self.get_prev_nodes(id).len() > 0;
            let has_outgoing = self.get_next_nodes(id).len() > 0;
            
            if !has_incoming && !has_outgoing {
                return Err(ValidationError::IsolatedNode(
                    format!("Node {} is isolated", id)
                ));
            }
        }
        Ok(())
    }

    /// 检查起始和结束节点
    fn check_start_end_nodes(&self) -> Result<(), ValidationError> {
        let mut start_nodes = Vec::new();
        let mut end_nodes = Vec::new();

        for (id, node) in &self.nodes {
            if node.node_type == NodeType::Task {
                let in_degree = self.get_prev_nodes(id).len();
                let out_degree = self.get_next_nodes(id).len();

                if in_degree == 0 {
                    start_nodes.push(id.clone());
                }
                if out_degree == 0 {
                    end_nodes.push(id.clone());
                }
            }
        }

        match (start_nodes.len(), end_nodes.len()) {
            (0, _) => Err(ValidationError::NoStartNode),
            (_, 0) => Err(ValidationError::NoEndNode),
            (1, 1) => Ok(()),
            (_, 1) => Err(ValidationError::MultipleStartNodes),
            (1, _) => Err(ValidationError::MultipleEndNodes),
            _ => Err(ValidationError::MultipleStartNodes),
        }
    }

    /// 检查从起始节点是否可以到达所有节点
    pub fn check_reachability(&self, start_node_id: &str) -> Result<(), ValidationError> {
        let mut visited = HashSet::new();
        let mut queue = VecDeque::new();
        queue.push_back(start_node_id.to_string());

        while let Some(node_id) = queue.pop_front() {
            if !visited.contains(&node_id) {
                visited.insert(node_id.clone());
                
                for (next_node, _) in self.get_next_nodes(&node_id) {
                    queue.push_back(next_node.id.clone());
                }
            }
        }

        // 检查是否所有节点都可达
        for node_id in self.nodes.keys() {
            if !visited.contains(node_id) {
                return Err(ValidationError::InvalidEdge(
                    format!("Node {} is not reachable from start node", node_id)
                ));
            }
        }

        Ok(())
    }

    /// 获取起始节点
    pub fn get_start_node(&self) -> Option<&Node> {
        self.nodes.values()
            .find(|node| 
                node.node_type == NodeType::Task && 
                self.get_prev_nodes(&node.id).is_empty()
            )
    }

    /// 获取结束节点
    pub fn get_end_node(&self) -> Option<&Node> {
        self.nodes.values()
            .find(|node| 
                node.node_type == NodeType::Task && 
                self.get_next_nodes(&node.id).is_empty()
            )
    }

    /// 获取入边节点
    pub fn get_incoming_nodes(&self, node_id: &str) -> Vec<&Node> {
        self.nodes.values()
            .filter(|node| {
                self.get_next_nodes(&node.id)
                    .iter()
                    .any(|(next, _)| next.id == node_id)
            })
            .collect()
    }

    /// 删除节点及其相关的边
    pub fn remove_node(&mut self, node_id: &str) -> Result<(), String> {
        if !self.nodes.contains_key(node_id) {
            return Err("Node does not exist".to_string());
        }

        // 删除与该节点相关的所有边
        self.edges.retain(|edge| edge.from != node_id && edge.to != node_id);
        
        // 删除节点
        self.nodes.remove(node_id);
        Ok(())
    }

    /// 删除边
    pub fn remove_edge(&mut self, from: &str, to: &str) -> Result<(), String> {
        let edge_index = self.edges.iter()
            .position(|edge| edge.from == from && edge.to == to);

        match edge_index {
            Some(index) => {
                self.edges.remove(index);
                Ok(())
            },
            None => Err("Edge does not exist".to_string())
        }
    }
}
