use super::*;
// use std::sync::Arc;
use async_trait::async_trait;
use engine::{<PERSON>E<PERSON><PERSON>, TaskContext, TaskHandler, TaskResult};
use tokio;

// 模拟任务处理器
struct MockTaskHandler {
    name: String,
}

#[async_trait]
impl TaskHandler for MockTaskHandler {
    async fn execute(&self, _graph: &ProcessGraph, _node: &Node, context: &mut TaskContext) -> TaskResult {
        println!(">>> Executing task: {}", self.name);
        println!("    Current incoming completed for {}: {:?}", 
            self.name, 
            context.incoming_completed.get(&self.name)
        );
        
        context.set_variable(
            format!("executed_{}", self.name),
            serde_json::Value::String("true".to_string()),
        );
        TaskResult::Success
    }
}

#[tokio::test]
async fn test_parallel_and_merge_flow() {
    // 创建流程图
    let mut graph = ProcessGraph::new();
    
    // 添加节点
    graph.add_node(Node::new(
        "start".to_string(),
        NodeType::Task,
        AppType::Function,
    )).unwrap();
    
    graph.add_node(Node::new(
        "gateway".to_string(),
        NodeType::Gateway,
        AppType::Function,
    )).unwrap();

    graph.add_node(Node::new(
        "task1".to_string(),
        NodeType::Task,
        AppType::Function,
    )).unwrap();
    
    graph.add_node(Node::new(
        "task2".to_string(),
        NodeType::Task,
        AppType::Function,
    )).unwrap();

    graph.add_node(Node::new(
        "task3".to_string(),
        NodeType::Task,
        AppType::Function,
    )).unwrap();

    graph.add_node(Node::new(
        "merge_task".to_string(),
        NodeType::Task,
        AppType::Function,
    )).unwrap();
    graph.add_node(Node::new(
        "gateway1".to_string(),
        NodeType::Gateway,
        AppType::Function,
    )).unwrap();
    graph.add_node(Node::new(
        "task4".to_string(),
        NodeType::Task,
        AppType::Function,
    )).unwrap();
    graph.add_node(Node::new(
        "task5".to_string(),
        NodeType::Task,
        AppType::Function,
    )).unwrap();
    // graph.add_node(Node::new(
    //         "merge_task1".to_string(),
    //     NodeType::Task,
    //     AppType::Function,
    // )).unwrap();

    // 添加边
    graph.add_edge("start".to_string(), "gateway".to_string(), "".to_string()).unwrap();
    graph.add_edge("gateway".to_string(), "task1".to_string(), "".to_string()).unwrap();
    graph.add_edge("gateway".to_string(), "task2".to_string(), "".to_string()).unwrap();
    graph.add_edge("gateway".to_string(), "task3".to_string(), "".to_string()).unwrap();
    graph.add_edge("task1".to_string(), "merge_task".to_string(), "".to_string()).unwrap();
    graph.add_edge("task2".to_string(), "merge_task".to_string(), "".to_string()).unwrap();
    graph.add_edge("task3".to_string(), "merge_task".to_string(), "".to_string()).unwrap();
    graph.add_edge("merge_task".to_string(), "gateway1".to_string(), "".to_string()).unwrap();
    graph.add_edge("gateway1".to_string(), "task4".to_string(), "".to_string()).unwrap();
    graph.add_edge("gateway1".to_string(), "task5".to_string(), "".to_string()).unwrap();
    // graph.add_edge("task4".to_string(), "merge_task1".to_string(), "".to_string()).unwrap();
    // graph.add_edge("task5".to_string(), "merge_task1".to_string(), "".to_string()).unwrap();

    // 创建引擎
    let mut engine = ProcessEngine::new(graph);

    // 注册任务处理器
    engine.register_task_handler(
        "start".to_string(),
        Box::new(MockTaskHandler { name: "start".to_string() }),
    );
    engine.register_task_handler(
        "task1".to_string(),
        Box::new(MockTaskHandler { name: "task1".to_string() }),
    );
    engine.register_task_handler(
        "task2".to_string(),
        Box::new(MockTaskHandler { name: "task2".to_string() }),
    );
    engine.register_task_handler(
        "task3".to_string(),
        Box::new(MockTaskHandler { name: "task3".to_string() }),
    );
    engine.register_task_handler(
        "merge_task".to_string(),
        Box::new(MockTaskHandler { name: "merge_task".to_string() }),
    );
    engine.register_task_handler(
        "task4".to_string(),
        Box::new(MockTaskHandler { name: "task4".to_string() }),
    );
    engine.register_task_handler(
        "task5".to_string(),
        Box::new(MockTaskHandler { name: "task5".to_string() }),
    );
    engine.register_task_handler(
        "merge_task1".to_string(),
        Box::new(MockTaskHandler { name: "merge_task1".to_string() }),
    );


    // 执行流程
    let mut context = TaskContext::new();
    println!("\nStarting flow execution...");
    let result = engine.execute(&mut context).await;

    // 打印最终的 incoming_completed 状态
    println!("\nFinal incoming_completed state:");
    for (node, completed) in &context.incoming_completed {
        println!("{}: {:?}", node, completed);
    }

    // 验证结果
    assert!(result.is_ok(), "Flow execution failed: {:?}", result);
    
    // 验证所有任务都被执行
    for task in ["start", "task1", "task2", "merge_task"] {
        assert!(
            context.get_variable(&format!("executed_{}", task)).is_some(),
            "Task {} was not executed!", task
        );
    }
}

// 测试条件分支
#[tokio::test]
async fn test_conditional_flow() {
    let mut graph = ProcessGraph::new();
    
    // 添加节点
    graph.add_node(Node::new(
        "start".to_string(),
        NodeType::Task,
        AppType::Function,
    )).unwrap();
    
    graph.add_node(Node::new(   
        "gateway".to_string(),
        NodeType::Gateway,
        AppType::Function,
    )).unwrap();
    
    graph.add_node(Node::new(
        "path1".to_string(),
        NodeType::Task,
        AppType::Function,
    )).unwrap() ;
    
    graph.add_node(Node::new(
        "path2".to_string(),
        NodeType::Task,
        AppType::Function,
    )).unwrap();
    graph.add_node(Node::new(
        "merge_task".to_string(),
        NodeType::Task,
        AppType::Function,
    )).unwrap();
    // 添加边
    graph.add_edge("start".to_string(), "gateway".to_string(), "".to_string()).unwrap();
    graph.add_edge("gateway".to_string(), "path1".to_string(), "condition == \"path1\"".to_string()).unwrap();
    graph.add_edge("gateway".to_string(), "path2".to_string(), "condition == \"path2\"".to_string()).unwrap();
    graph.add_edge("path1".to_string(), "merge_task".to_string(), "".to_string()).unwrap();
    graph.add_edge("path2".to_string(), "merge_task".to_string(), "".to_string()).unwrap();

    let mut engine = ProcessEngine::new(graph);

    // 注册任务处理器
    engine.register_task_handler(
        "start".to_string(),
        Box::new(MockTaskHandler { name: "start".to_string() }),
    );
    engine.register_task_handler(
        "path1".to_string(),
        Box::new(MockTaskHandler { name: "path1".to_string() }),
    );
    engine.register_task_handler(
        "path2".to_string(),
        Box::new(MockTaskHandler { name: "path2".to_string() }),
    );
    engine.register_task_handler(
        "merge_task".to_string(),
        Box::new(MockTaskHandler { name: "merge_task".to_string() }),
    );
    // 测试路径1
    let mut context = TaskContext::new();
    context.set_variable(
        "condition".to_string(),
        serde_json::Value::String("path2".to_string()),
    );
    
    let result = engine.execute(&mut context).await;
    assert!(result.is_ok());
    // assert!(context.get_variable("executed_path1").is_some());
    // assert!(context.get_variable("executed_path2").is_none());
    // 打印最终的 incoming_completed 状态
    println!("\nFinal incoming_completed state:");
    for (node, completed) in &context.incoming_completed {
        println!("{}: {:?}", node, completed);
    }

    // 验证结果
    assert!(result.is_ok(), "Flow execution failed: {:?}", result);

    // 打印最终的 incoming_completed 状态
    println!("\nFinal incoming_completed state:");
    for (node, completed) in &context.incoming_completed {
        println!("{}: {:?}", node, completed);
    }

    // // 验证所有任务都被执行
    // for task in ["start", "task1", "task2", "merge_task"] {
    //     assert!(
    //         context.get_variable(&format!("executed_{}", task)).is_some(),
    //         "Task {} was not executed!", task
    //     );
    // }
} 