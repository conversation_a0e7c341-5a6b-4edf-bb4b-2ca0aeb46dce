use std::sync::Arc;
use rhai::{Engine, Scope, AST, Dynamic};
use std::error::Error;

#[derive(Debug, Clone)]
pub struct EngineOptions {
    pub max_operations: u64,      // 限制脚本执行的最大操作数
    pub max_modules: usize,       // 最大可加载模块数
    pub max_string_size: usize,   // 字符串大小限制
    pub optimization_level: u8,   // 优化级别
}

impl Default for EngineOptions {
    fn default() -> Self {
        Self {
            max_operations: 1_000_000,  // 默认一百万次操作
            max_modules: 10,            // 默认最多10个模块
            max_string_size: 10_000,    // 默认10KB字符串限制
            optimization_level: 2,       // 默认优化级别
        }
    }
}

#[allow(dead_code)]
pub struct BAScriptManager {
    engine: Arc<Engine>,
}

pub trait ScriptEngineConfigurator {
    fn configure_engine(&self, engine: &mut Engine) -> Result<(), Box<dyn Error>>;
    fn get_engine_options(&self) -> EngineOptions {
        EngineOptions::default()
    }
}

pub trait ScriptContextProvider {
    fn provide_context(&self, scope: &mut Scope) -> Result<(), Box<dyn Error>>;
    fn get_context_name(&self) -> &str;
}

impl BAScriptManager {
    pub fn create_instance(
        configurator: &impl ScriptEngineConfigurator
    ) -> Result<Self, Box<dyn Error>> {
        let mut engine = Engine::new();
        
        // 应用引擎选项
        let options = configurator.get_engine_options();
        engine.set_max_operations(options.max_operations);
        engine.set_max_modules(options.max_modules);
        engine.set_max_string_size(options.max_string_size);
        // engine.set_optimization_level(options.optimization_level as rhai::OptimizationLevel);
        
        // 配置引擎
        configurator.configure_engine(&mut engine)?;
        Ok(Self { 
            engine: Arc::new(engine),
        })
    }

    pub fn create_scope(
        &self,
        provider: &impl ScriptContextProvider
    ) -> Scope {
        let mut scope = Scope::new();
        provider.provide_context(&mut scope).unwrap();
        scope
    }

    /// 编译脚本，返回编译后的 AST
    pub fn compile(&self, script: &str) -> Result<AST, Box<dyn Error>> {
        Ok(self.engine.compile(script)?)
    }

    /// 直接执行脚本字符串
    pub fn eval_script(
        &self, 
        script: &str, 
        scope: &mut Scope
    ) -> Result<Dynamic, Box<dyn Error>> {
        Ok(self.engine.eval_with_scope(scope, script)?)
    }

    /// 执行已编译的 AST
    pub fn eval_ast(
        &self, 
        ast: &AST, 
        scope: &mut Scope
    ) -> Result<Dynamic, Box<dyn Error>> {
        Ok(self.engine.eval_ast_with_scope(scope, ast)?)
    }

    // /// 调用脚本中的函数
    // pub fn call_fn<T: Clone + 'static>(
    //     &self,
    //     scope: &mut Scope,
    //     fn_name: &str,
    //     args: impl IntoIterator<Item = Dynamic>,
    // ) -> Result<T, Box<dyn Error>> {
    //     let args: Vec<Dynamic> = args.into_iter().collect();
    //     let result = self.engine.call_fn::<T>(
    //         scope,
    //         fn_name,
    //         args,
    //     )?;
    //     Ok(result)
    // }
}
