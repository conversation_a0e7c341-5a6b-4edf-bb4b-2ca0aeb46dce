use std::collections::HashMap;
// use std::path::{Path, PathBuf};
use std::sync::Arc;
use tokio::sync::RwLock;
use lazy_static::lazy_static;
// use thiserror::Error;

// use libloading::{Library, Symbol};

use super::{PluginError, TranProcPlugin, TranProcPluginBuilder};
// use super::{CreatePluginFn, Plugin, PluginError};

// use tracing::info;

// use std::time::Duration;
// 定义静态builders
lazy_static! {
    static ref BUILDERS: Arc<RwLock<HashMap<String, Arc<dyn TranProcPluginBuilder + Send + Sync>>>> = 
        Arc::new(RwLock::new(HashMap::new()));
}

pub struct PluginBuilderManager {
    // 插件构建器管理
    // builders: Arc<RwLock<HashMap<String, Arc<dyn ServicePluginBuilder>>>>,
}

impl PluginBuilderManager {
    // Add a builder to the manager
    pub async fn register_builder(builder: Arc<dyn TranProcPluginBuilder + Send + Sync>) -> Result<(), PluginError> {        
        let builder_id = builder.id().to_string();
        let mut builders = BUILDERS.write().await;
        
        if builders.contains_key(&builder_id) {
            return Err(PluginError::AlreadyExists(builder_id));
        }
        
        builders.insert(builder_id, builder);
        Ok(())
    }
    // Remove a builder from the manager
    pub async fn unregister_builder(builder_id: &str) -> Result<(), PluginError> {
        let mut builders = BUILDERS.write().await;
        
        if builders.remove(builder_id).is_none() {
            return Err(PluginError::NotFound(builder_id.to_string()));
        }
        
        Ok(())
    }
    // List all registered builders
    pub async fn list_builders() -> Vec<String> {
        let builders = BUILDERS.read().await;
        builders.keys().cloned().collect()
    }
    // Get a builder and create a new plugin
    pub async fn create_plugin(builder_id: &str) -> Result<Arc<dyn TranProcPlugin>, PluginError> {
        let builders = BUILDERS.read().await;
        let builder = builders.get(builder_id)
            .ok_or_else(|| PluginError::NotFound(format!("Builder not found: {}", builder_id)))?;
        
        let plugin = builder.build();
        Ok(plugin)
    }
    // Get a builder
    pub async fn get_builder(builder_id: &str) -> Result<Arc<dyn TranProcPluginBuilder+ Send + Sync>, PluginError> {
        let builders = BUILDERS.read().await;
        builders.get(builder_id)
            .ok_or_else(|| PluginError::NotFound(format!("Builder not found: {}", builder_id)))
            .map(|b| b.clone())
    }
}
