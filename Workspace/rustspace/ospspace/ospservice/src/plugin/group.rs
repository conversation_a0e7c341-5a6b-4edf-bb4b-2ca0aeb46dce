use std::sync::Arc;

use super::{AsyncContainer, AsyncContext, AsyncRequest, AsyncResponse, PluginError, PluginValue, TranProcPlugin};


/// 服务阶段，用于管理多个插件组


// 插件组的执行策略
#[derive(Debug, Clone, Copy)]
pub enum ExecutionStrategy {
    /// 并行执行所有插件
    Parallel,
    /// 串行执行所有插件
    Serial,
    /// 任一插件成功即返回
    FirstSuccess,
    /// 所有插件都必须成功
    AllSuccess,
}

// 插件组结构
// #[derive(Debug)]
pub struct PluginGroup {
    name: String,
    plugins: Vec<Arc<dyn TranProcPlugin>>,
    // settings: HashMap<>,
    strategy: ExecutionStrategy,
}

impl PluginGroup {
    pub fn new(name: &str, strategy: ExecutionStrategy) -> Self {
        Self {
            name: name.to_string(),
            plugins: Vec::new(),
            strategy,
        }
    }
    pub fn name(&self) -> &str {
        &self.name
    }
    // 添加插件到组
    pub async fn add_plugin(&mut self, plugin: Arc<dyn TranProcPlugin>) {
        // let mut plugins = self.plugins.write().await;
        self.plugins.push(plugin);
    }

    // 从组中移除插件
    pub async fn remove_plugin(&mut self, name: &str) -> Result<(), PluginError> {
        // let mut plugins = self.plugins.write().await;
        if let Some(pos) = self.plugins.iter().position(|p| p.name() == name) {
            self.plugins.remove(pos);
            Ok(())
        } else {
            Err(PluginError::NotFound(name.to_string()))
        }
    }

    // 执行插件组
    pub async fn execute(
        &self,
        container   : AsyncContainer,
        context     : AsyncContext,
        request     : AsyncRequest,
        response    : AsyncResponse,
    ) -> Result<Vec<PluginValue>, PluginError> {
        // let plugins = self.plugins.read().await;
        
        match self.strategy {
            // ExecutionStrategy::Parallel => {
            //     // 使用tokio::spawn实现并行执行
            //     let futures: Vec<_> = plugins.iter().map(|plugin| {
            //         tokio::spawn(plugin.execute(
            //             container.clone(),
            //             context.clone(),
            //             request.clone(),
            //             response.clone()
            //         ))
            //     }).collect();
                
            //     let results = futures::future::join_all(futures).await;
            //     // 处理结果...
            // },
            ExecutionStrategy::Serial => {
                self.execute_serial(&self.plugins,container, context,request, response).await
            },
            _ => {
                Err(PluginError::ExecuteError("Invalid execution strategy".into()))
            }
            // ExecutionStrategy::FirstSuccess => {
            //     self.execute_first_success(&plugins, ctx, request, response).await
            // },
            // ExecutionStrategy::AllSuccess => {
            //     self.execute_all_success(&plugins, ctx, request, response).await
            // },
        }
    }

    // 串行执行
    async fn execute_serial(
        &self,
        plugins: &[Arc<dyn TranProcPlugin>],
        container   : AsyncContainer,
        context     : AsyncContext,
        request     : AsyncRequest,
        response    : AsyncResponse,
    ) -> Result<Vec<PluginValue>, PluginError> {
        let mut results = Vec::new();
        
        for plugin in plugins {
            match plugin.execute(container.clone(),context.clone(), request.clone(), response.clone()).await {
                Ok(result) => results.push(result),
                Err(e) => tracing::warn!("Plugin {} execution failed: {}", plugin.name(), e),
            }
        }
        
        Ok(results)
    }

    // 并行执行
    // async fn execute_parallel(
    //     &self,
    //     plugins: &[Arc<dyn Plugin>],
    //     ctx: AsyncContext,
    //     request: AsyncRequest,
    //     response: AsyncResponse,
    // ) -> Result<Vec<()>, PluginError> {
    //     let futures: Vec<_> = plugins
    //         .iter()
    //         .map(|plugin| {
    //             let ctx = Arc::clone(&ctx);
    //             let request = Arc::clone(&request);
    //             let response = Arc::clone(&response);
                
    //             async move {
    //                 plugin.execute(ctx, request, response).await
    //             }
    //         })
    //         .collect();

    //     let results = futures::future::join_all(futures).await;
        
    //     // 处理所有执行结果
    //     // for result in results {
    //     //     if let Err(e) = result {
    //     //         tracing::warn!("Plugin execution failed: {}", e);
    //     //     }
    //     // }
    //     // results
    //     Ok(results)
    // }


    // 任一成功即返回
    // async fn execute_first_success(
    //     &self,
    //     plugins: &[Arc<dyn Plugin>],
    //     ctx: AsyncContext,
    //     request: AsyncRequest,
    //     response: AsyncResponse,
    // ) -> Result<(), PluginError> {
    //     for plugin in plugins {
    //         if let Ok(result) = plugin.execute(ctx.clone(), request.clone(), response.clone()   ).await {
    //             return Ok(result);
    //         }
    //     }
    //     Err(PluginError::ExecuteError("No plugin succeeded".into()))
    // }

    // // 所有都必须成功
    // async fn execute_all_success(
    //     &self,
    //     plugins: &[Arc<dyn Plugin>],
    //     ctx: AsyncContext,
    //     request: AsyncRequest,
    //     response: AsyncResponse,
    // ) -> Result<(), PluginError> {
    //     let mut results = Vec::new();
        
    //     for plugin in plugins {
    //         match plugin.execute(ctx, request, response).await {
    //             Ok(result) => results.push(result),
    //             Err(e) => return Err(PluginError::ExecuteError(
    //                 format!("Plugin {} failed: {}", plugin.name(), e)
    //             )),
    //         }
    //     }
        
    //     Ok(results)
    // }

    /// 获取插件列表（只读）
    pub fn plugins(&self) -> &[Arc<dyn TranProcPlugin>] {
        &self.plugins
    }

    /// 获取插件列表（可变）
    pub fn plugins_mut(&mut self) -> &mut Vec<Arc<dyn TranProcPlugin>> {
        &mut self.plugins
    }
}


// #[tokio::main]
// async fn _main() -> Result<(), PluginError> {
//     let manager = PluginManager::new();
    
//     // 创建一个并行执行的插件组
//     manager.create_group("image_processors", ExecutionStrategy::Parallel).await?;
    
//     // 加载插件
//     manager.load_plugin("plugins/resize.so").await?;
//     manager.load_plugin("plugins/filter.so").await?;
    
//     // 将插件添加到组
//     manager.add_to_group("image_processors", "resize").await?;
//     manager.add_to_group("image_processors", "filter").await?;
    
//     // 执行整个组
//     // let results = manager.execute_group("image_processors", "input.jpg").await?;
    
//     // println!("Results: {:?}", results);
//     Ok(())
// } 