use std::path::Path;

use ospbase::data::KeyValue;
use serde::{Deserialize, Serialize};

use super::PluginError;

#[derive(Debug, Serialize, Deserialize)]
pub struct PluginConfig {
    pub name: String,
    pub version: String,
    pub enabled: bool,
    pub auto_start: bool,
    pub settings: KeyValue,
}

impl PluginConfig {
    pub fn load_config(&self, _path: &Path) -> Result<(), PluginError> {
        // 从配置文件加载插件配置
        Ok(())
    }
    
    pub fn save_config(&self, _path: &Path) -> Result<(), PluginError> {
        // 保存插件配置到文件
        Ok(())
    }
} 