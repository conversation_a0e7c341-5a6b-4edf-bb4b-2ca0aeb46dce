pub mod manager;
pub mod group;
pub mod config;

use std::{any::Any, sync::Arc};

use async_trait::async_trait;
use ospbase::data::KeyValue;

use crate::{Async<PERSON>ontainer, AsyncContext, AsyncRequest, AsyncResponse};



#[repr(C)]
#[derive(Debug, thiserror::Error)]
pub enum PluginError {
    #[error("Plugin {0} not found")]
    NotFound(String),
    #[error("Plugin {0} already exists")]
    AlreadyExists(String),
    #[error("Failed to load library: {0}")]
    LibraryError(#[from] libloading::Error),
    #[error("Plugin initialization failed: {0}")]
    InitError(String),
    #[error("Plugin execution failed: {0}")]
    ExecuteError(String),
}
#[repr(C)]
#[derive(Debug)]
pub enum PluginValue {
    Success,
    Error(u32,String),
}

// 为常用类型定义别名



pub trait TranProcPluginBuilder {
    fn id(&self) -> &str;
    fn name(&self) -> &str;
    fn version(&self) -> &str;
    fn build(&self) -> Arc<dyn TranProcPlugin>;
}

#[async_trait]
pub trait TranProcPlugin: Any + Send + Sync {
    fn get_instance_id(&self) -> &str;
    fn set_instance_id(&mut self, id: &str);
    fn name(&self) -> &str;
    fn version(&self) -> &str;
    
    fn set_config(&mut self, config: Arc<KeyValue>);
    fn get_config(&self) -> Option<Arc<&KeyValue>>;

    async fn initialize(&self) -> Result<PluginValue, Box<dyn std::error::Error + Send + Sync>>;
    async fn shutdown(&self) -> Result<PluginValue, Box<dyn std::error::Error + Send + Sync>>;

    // 示例异步方法
    async fn prepare(
        &self,
        container   : AsyncContainer,
        context     : AsyncContext,
        request     : AsyncRequest,
        response    : AsyncResponse,
    ) -> Result<PluginValue, PluginError>;

    // 示例异步方法
    async fn execute(
        &self,
        container   : AsyncContainer,
        context     : AsyncContext,
        request     : AsyncRequest,
        response    : AsyncResponse,
    ) -> Result<PluginValue, PluginError>;

    // 示例异步方法
    async fn finish(
        &self,
        container   : AsyncContainer,
        context     : AsyncContext,
        request     : AsyncRequest,
        response    : AsyncResponse,
    ) -> Result<PluginValue, PluginError>;

    // 添加状态查询方法
    fn status(&self) -> PluginStatus;
    
    // 添加健康检查方法
    async fn health_check(&self) -> Result<PluginValue, PluginError>;
    
    // 添加版本兼容性检查
    fn is_compatible_with(&self, version: &str) -> bool;
    
    // 添加依赖声明
    fn dependencies(&self) -> Vec<PluginDependency>;
}

#[derive(Debug, Clone)]
pub struct PluginDependency {
    pub name: String,
    pub version_req: semver::VersionReq,
}

#[derive(Debug, Clone, PartialEq)]
pub enum PluginStatus {
    Uninitialized,
    Running,
    Error(String),
    Stopped,
}
